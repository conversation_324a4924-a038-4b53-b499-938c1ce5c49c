import React from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';

/**
 * This is a utility component to fix the maxHeight issue in DialogContent
 * It wraps the DialogContent and adds a max-height and overflow-y-auto class
 */
export const FixDialogIssues = () => {
  // Find all DialogContent components with maxHeight prop
  React.useEffect(() => {
    const fixDialogIssues = () => {
      // Find all DialogContent elements with maxHeight attribute
      const dialogContents = document.querySelectorAll('[maxheight]');
      
      // For each element, add the appropriate classes
      dialogContents.forEach((element) => {
        const maxHeight = element.getAttribute('maxheight');
        if (maxHeight) {
          element.classList.add('overflow-y-auto');
          element.style.maxHeight = maxHeight;
          // Remove the invalid attribute
          element.removeAttribute('maxheight');
        }
      });
    };

    // Run the fix when the component mounts
    fixDialogIssues();

    // Also run it when the DOM changes
    const observer = new MutationObserver(fixDialogIssues);
    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    });

    return () => {
      observer.disconnect();
    };
  }, []);

  return null;
};

export default FixDialogIssues;
