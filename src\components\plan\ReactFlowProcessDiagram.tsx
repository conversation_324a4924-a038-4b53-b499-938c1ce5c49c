import React, { useCallback, useMemo, useState } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  MiniMap,
  Background,
  BackgroundVariant,
  NodeTypes,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { ProcessStepNode } from './ProcessStepNode';
import { FishboneDiagram } from './FishboneDiagram';
import { ProcessStep, Hazard } from '@/models/types';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowRight, ArrowDown, GitBranch } from 'lucide-react';

interface ReactFlowProcessDiagramProps {
  processSteps: ProcessStep[];
  hazards: Hazard[];
  ccpData: any[];
  readOnly?: boolean;
}

const nodeTypes: NodeTypes = {
  processStep: ProcessStepNode,
};

export const ReactFlowProcessDiagram: React.FC<ReactFlowProcessDiagramProps> = ({
  processSteps,
  hazards,
  ccpData,
  readOnly = false,
}) => {
  const [layoutDirection, setLayoutDirection] = useState<'vertical' | 'horizontal'>('vertical');
  const [viewMode, setViewMode] = useState<'flow' | 'fishbone'>('flow');

  // Check if a step is a CCP
  const isStepCCP = useCallback((stepId: string) => {
    return ccpData.some(ccp => ccp.hazard.processStepId === stepId);
  }, [ccpData]);

  // Check if a step has hazards
  const stepHasHazards = useCallback((stepId: string) => {
    return hazards.some(hazard => hazard.processStepId === stepId);
  }, [hazards]);

  // Get hazards for a step
  const getHazardsForStep = useCallback((stepId: string) => {
    return hazards.filter(hazard => hazard.processStepId === stepId);
  }, [hazards]);

  // Generate nodes from process steps
  const initialNodes: Node[] = useMemo(() => {
    const sortedSteps = [...processSteps].sort((a, b) => a.order - b.order);

    return sortedSteps.map((step, index) => {
      const spacing = layoutDirection === 'vertical' ? { x: 0, y: index * 150 } : { x: index * 200, y: 0 };

      return {
        id: step.id,
        type: 'processStep',
        position: { x: 250 + spacing.x, y: 50 + spacing.y },
        data: {
          step,
          isCCP: isStepCCP(step.id),
          hasHazards: stepHasHazards(step.id),
          hazards: getHazardsForStep(step.id),
        },
        draggable: !readOnly,
      };
    });
  }, [processSteps, layoutDirection, isStepCCP, stepHasHazards, getHazardsForStep, readOnly]);

  // Generate edges to connect sequential steps
  const initialEdges: Edge[] = useMemo(() => {
    const sortedSteps = [...processSteps].sort((a, b) => a.order - b.order);

    return sortedSteps.slice(0, -1).map((step, index) => ({
      id: `e${step.id}-${sortedSteps[index + 1].id}`,
      source: step.id,
      target: sortedSteps[index + 1].id,
      type: 'smoothstep',
      animated: false,
      style: { stroke: '#94a3b8', strokeWidth: 2 },
    }));
  }, [processSteps]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

  // Update nodes when layout direction changes
  React.useEffect(() => {
    const sortedSteps = [...processSteps].sort((a, b) => a.order - b.order);

    const updatedNodes = sortedSteps.map((step, index) => {
      const spacing = layoutDirection === 'vertical' ? { x: 0, y: index * 150 } : { x: index * 200, y: 0 };

      return {
        id: step.id,
        type: 'processStep',
        position: { x: 250 + spacing.x, y: 50 + spacing.y },
        data: {
          step,
          isCCP: isStepCCP(step.id),
          hasHazards: stepHasHazards(step.id),
          hazards: getHazardsForStep(step.id),
        },
        draggable: !readOnly,
      };
    });

    setNodes(updatedNodes);
  }, [layoutDirection, processSteps, isStepCCP, stepHasHazards, getHazardsForStep, readOnly, setNodes]);

  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  const toggleLayout = () => {
    setLayoutDirection(prev => prev === 'vertical' ? 'horizontal' : 'vertical');
  };

  const toggleViewMode = () => {
    setViewMode(prev => prev === 'flow' ? 'fishbone' : 'flow');
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>2. Process Flow Diagram</CardTitle>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleViewMode}
                className="flex items-center gap-2"
              >
                <GitBranch className="h-4 w-4" />
                {viewMode === 'flow' ? 'Fishbone View' : 'Flow View'}
              </Button>
              {viewMode === 'flow' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleLayout}
                  className="flex items-center gap-2"
                >
                  {layoutDirection === 'vertical' ? (
                    <>
                      <ArrowRight className="h-4 w-4" />
                      Horizontal
                    </>
                  ) : (
                    <>
                      <ArrowDown className="h-4 w-4" />
                      Vertical
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[600px] border rounded-lg bg-gray-50">
            {viewMode === 'flow' ? (
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onConnect={onConnect}
                nodeTypes={nodeTypes}
                fitView
                fitViewOptions={{ padding: 0.2 }}
                minZoom={0.5}
                maxZoom={2}
                defaultViewport={{ x: 0, y: 0, zoom: 1 }}
              >
                <Controls />
                <MiniMap
                  nodeColor={(node) => {
                    if (node.data?.isCCP) return '#fbbf24';
                    if (node.data?.hasHazards) return '#fb923c';
                    return '#e5e7eb';
                  }}
                  nodeStrokeWidth={3}
                  zoomable
                  pannable
                />
                <Background variant={BackgroundVariant.Dots} gap={12} size={1} />
              </ReactFlow>
            ) : (
              <FishboneDiagram
                processSteps={processSteps}
                hazards={hazards}
                ccpData={ccpData}
              />
            )}
          </div>
          <div className="mt-4 flex flex-wrap gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-yellow-100 border border-yellow-400 rounded"></div>
              <span>Critical Control Point (CCP)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-100 border border-orange-200 rounded"></div>
              <span>Has Hazards</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-white border border-gray-200 rounded"></div>
              <span>Standard Process Step</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
