
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { determineCCP } from '@/utils/riskCalculation';

interface DecisionTreeProps {
  onComplete: (isCCP: boolean) => void;
  initialAnswers?: Record<string, boolean>;
  className?: string;
}

export function DecisionTree({ onComplete, initialAnswers, className }: DecisionTreeProps) {
  const [currentQuestion, setCurrentQuestion] = useState(1);
  const [answers, setAnswers] = useState<Record<string, boolean>>(initialAnswers || {
    q1: false,
    q2: false,
    q3: false,
    q4: false,
    q5: false,
  });
  const [result, setResult] = useState<boolean | null>(null);

  const questions = [
    {
      id: 'q1',
      text: 'Q1: Do control measures exist for the identified hazard?',
      yes: () => setCurrentQuestion(3), // If yes, jump to Q3
      no: () => setCurrentQuestion(2), // If no, go to Q2
    },
    {
      id: 'q2',
      text: 'Q2: Is control at this step necessary for safety?',
      yes: () => {
        setAnswers({ ...answers, q2: true });
        finishTree();
      },
      no: () => {
        setAnswers({ ...answers, q2: false });
        finishTree();
      },
    },
    {
      id: 'q3',
      text: 'Q3: Is this step specifically designed to eliminate or reduce the hazard to an acceptable level?',
      yes: () => {
        setAnswers({ ...answers, q3: true });
        finishTree();
      },
      no: () => setCurrentQuestion(4),
    },
    {
      id: 'q4',
      text: 'Q4: Could contamination occur at or increase to unacceptable levels?',
      yes: () => setCurrentQuestion(5),
      no: () => {
        setAnswers({ ...answers, q4: false });
        finishTree();
      },
    },
    {
      id: 'q5',
      text: 'Q5: Will a subsequent step eliminate or reduce the hazard to an acceptable level?',
      yes: () => {
        setAnswers({ ...answers, q5: true });
        finishTree();
      },
      no: () => {
        setAnswers({ ...answers, q5: false });
        finishTree();
      },
    },
  ];

  const handleAnswer = (questionId: string, answer: boolean) => {
    setAnswers({ ...answers, [questionId]: answer });
    const currentQ = questions[currentQuestion - 1];
    answer ? currentQ.yes() : currentQ.no();
  };

  const finishTree = () => {
    const isCCP = determineCCP(answers as any);
    setResult(isCCP);
    onComplete(isCCP);
  };

  const resetTree = () => {
    setCurrentQuestion(1);
    setAnswers({
      q1: false,
      q2: false,
      q3: false,
      q4: false,
      q5: false,
    });
    setResult(null);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>CCP Decision Tree</CardTitle>
        <CardDescription>
          Based on Codex Alimentarius HACCP guidance
        </CardDescription>
      </CardHeader>
      <CardContent>
        {result !== null ? (
          <div className="space-y-4">
            <div className={`p-4 rounded-lg border ${result ? 'bg-yellow-50 border-yellow-500' : 'bg-green-50 border-green-500'}`}>
              <h3 className="font-bold mb-2">
                {result ? 'This is a Critical Control Point (CCP)' : 'This is not a Critical Control Point'}
              </h3>
              <p className="text-sm">
                {result 
                  ? 'Implement critical limits, monitoring procedures, and corrective actions.'
                  : 'Consider implementing as a Prerequisite Program (PRP) or Operational Prerequisite Program (OPRP).'}
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold">Your answers:</h4>
              <ul className="space-y-1 text-sm">
                {Object.entries(answers).map(([q, answer]) => (
                  questions.find(question => question.id === q) && (
                    <li key={q} className="flex items-start">
                      <span className={`inline-block w-4 h-4 mt-1 mr-2 rounded-full ${answer ? 'bg-green-500' : 'bg-red-500'}`}></span>
                      <span>{questions.find(question => question.id === q)?.text}: {answer ? 'Yes' : 'No'}</span>
                    </li>
                  )
                ))}
              </ul>
            </div>
          </div>
        ) : (
          <>
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="font-medium">{questions[currentQuestion - 1].text}</p>
            </div>
            <div className="flex gap-4">
              <Button 
                onClick={() => handleAnswer(questions[currentQuestion - 1].id, true)}
                className="flex-1"
                variant="outline"
              >
                Yes
              </Button>
              <Button 
                onClick={() => handleAnswer(questions[currentQuestion - 1].id, false)}
                className="flex-1"
                variant="outline"
              >
                No
              </Button>
            </div>
          </>
        )}
      </CardContent>
      {result !== null && (
        <CardFooter>
          <Button onClick={resetTree} variant="outline" className="w-full">
            Start Over
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}

export default DecisionTree;
