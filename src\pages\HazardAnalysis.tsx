
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { HazardForm } from '@/components/hazards/hazard-form';
import { HazardTable } from '@/components/hazards/hazard-table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Hazard, ProcessStep } from '@/models/types';
import { useAuth } from '@/contexts/AuthContext';
import { AlertTriangle, Plus, FileDown, FileText } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useLocalStorage } from '@/hooks/use-local-storage';

// Mock data
const mockProcessSteps: ProcessStep[] = [
  { id: 'step1', name: 'Raw Material Reception', description: 'Receiving ingredients and raw materials', order: 1 },
  { id: 'step2', name: 'Storage', description: 'Refrigerated and ambient storage of ingredients', order: 2 },
  { id: 'step3', name: 'Preparation', description: 'Cutting, mixing, and pre-processing', order: 3 },
  { id: 'step4', name: 'Cooking', description: 'Heat treatment of product', order: 4 },
  { id: 'step5', name: 'Cooling', description: 'Controlled cooling of cooked product', order: 5 },
  { id: 'step6', name: 'Packaging', description: 'Final packaging of product', order: 6 },
  { id: 'step7', name: 'Storage & Distribution', description: 'Final storage and shipping', order: 7 },
];

const mockHazards: Hazard[] = [
  {
    id: 'h1',
    processStepId: 'step1',
    description: 'Salmonella in raw chicken',
    type: 'Biological',
    severity: 4,
    likelihood: 3,
    controlMeasures: 'Supplier approval program, Certificate of Analysis, Temperature control during reception',
    isCCP: false,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h2',
    processStepId: 'step1',
    description: 'Pesticide residues on produce',
    type: 'Chemical',
    severity: 3,
    likelihood: 2,
    controlMeasures: 'Approved supplier program, Certificates of Analysis',
    isCCP: false,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h3',
    processStepId: 'step3',
    description: 'Metal fragments from equipment',
    type: 'Physical',
    severity: 3,
    likelihood: 2,
    controlMeasures: 'Equipment maintenance program, Metal detection',
    isCCP: false,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h4',
    processStepId: 'step4',
    description: 'Survival of pathogens due to inadequate cooking',
    type: 'Biological',
    severity: 5,
    likelihood: 3,
    controlMeasures: 'Time/temperature control, Staff training, Regular calibration of thermometers',
    isCCP: true,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
];

export function HazardAnalysis() {
  const { hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState(mockProcessSteps[0].id);
  const [hazards, setHazards] = useLocalStorage<Hazard[]>('haccp_hazards', mockHazards);
  const [showAddHazardForm, setShowAddHazardForm] = useState(false);
  const [editingHazard, setEditingHazard] = useState<Hazard | null>(null);

  const hazardsForCurrentStep = hazards.filter(h => h.processStepId === activeTab);
  const canEdit = hasPermission('qa');

  const handleAddHazard = (newHazard: Partial<Hazard>) => {
    // In a real app, this would make an API call
    const hazardWithId: Hazard = {
      ...newHazard,
      id: `h${hazards.length + 1}`,
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date(),
    } as Hazard;

    setHazards([...hazards, hazardWithId]);
    setShowAddHazardForm(false);
    toast({
      title: 'Success',
      description: 'New hazard has been added',
    });
  };

  const handleUpdateHazard = (updatedHazard: Partial<Hazard>) => {
    if (!editingHazard) return;

    const updatedHazards = hazards.map(h =>
      h.id === editingHazard.id
        ? { ...h, ...updatedHazard, updatedAt: new Date() }
        : h
    );

    setHazards(updatedHazards);
    setEditingHazard(null);
    toast({
      title: 'Success',
      description: 'Hazard has been updated',
    });
  };

  const handleDeleteHazard = (hazardId: string) => {
    // In a real app, this would make an API call
    setHazards(hazards.filter(h => h.id !== hazardId));
    toast({
      title: 'Success',
      description: 'Hazard has been deleted',
    });
  };

  const handleEditHazard = (hazard: Hazard) => {
    setEditingHazard(hazard);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">Hazard Analysis</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <FileDown className="h-4 w-4 mr-2" />
            Export
          </Button>
          {canEdit && (
            <Button size="sm" onClick={() => setShowAddHazardForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Hazard
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-3 md:grid-cols-7 mb-4">
          {mockProcessSteps.map((step) => (
            <TabsTrigger key={step.id} value={step.id} className="text-xs md:text-sm">
              {step.name}
            </TabsTrigger>
          ))}
        </TabsList>

        {mockProcessSteps.map((step) => (
          <TabsContent key={step.id} value={step.id}>
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{step.name}</CardTitle>
                    <CardDescription>{step.description}</CardDescription>
                  </div>
                  <div className="flex items-center space-x-1 bg-muted px-2 py-1 rounded text-xs">
                    <span>Step</span>
                    <span className="font-bold">{step.order}</span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <HazardTable
                    hazards={hazardsForCurrentStep}
                    onEdit={canEdit ? handleEditHazard : undefined}
                    onDelete={canEdit ? handleDeleteHazard : undefined}
                    readonly={!canEdit}
                  />

                  {hazardsForCurrentStep.length === 0 && canEdit && (
                    <div className="text-center py-6">
                      <AlertTriangle className="h-10 w-10 text-yellow-500 mx-auto mb-3" />
                      <h3 className="text-lg font-medium mb-2">No hazards identified</h3>
                      <p className="text-muted-foreground mb-4">
                        This process step doesn't have any hazards identified yet.
                      </p>
                      <Button onClick={() => setShowAddHazardForm(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add First Hazard
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Add Hazard Dialog */}
      <Dialog open={showAddHazardForm} onOpenChange={setShowAddHazardForm}>
        <DialogContent className="max-w-4xl overflow-y-auto max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Add Hazard for {mockProcessSteps.find(step => step.id === activeTab)?.name}</DialogTitle>
          </DialogHeader>
          <div className="pr-1">
            <HazardForm
              onSubmit={handleAddHazard}
              processStepId={activeTab}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Hazard Dialog */}
      <Dialog open={!!editingHazard} onOpenChange={(open) => !open && setEditingHazard(null)}>
        <DialogContent className="max-w-4xl overflow-y-auto max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Edit Hazard for {editingHazard && mockProcessSteps.find(step => step.id === editingHazard.processStepId)?.name}</DialogTitle>
          </DialogHeader>
          <div className="pr-1">
            {editingHazard && (
              <HazardForm
                onSubmit={handleUpdateHazard}
                initialHazard={editingHazard}
                processStepId={editingHazard.processStepId}
              />
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default HazardAnalysis;
