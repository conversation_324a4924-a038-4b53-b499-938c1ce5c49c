import { PhotoFile } from '@/components/ui/photo-upload';

// Enhanced error handling
export enum PhotoErrorType {
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PROCESSING_FAILED = 'PROCESSING_FAILED',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  INVALID_IMAGE_FORMAT = 'INVALID_IMAGE_FORMAT',
  CORRUPTED_FILE = 'CORRUPTED_FILE'
}

export interface PhotoError {
  type: PhotoErrorType;
  message: string;
  details?: string;
  retryable: boolean;
}

export interface PhotoValidationResult {
  isValid: boolean;
  error?: PhotoError;
  warnings?: string[];
}

export interface StoredPhoto {
  id: string;
  url: string;
  name: string;
  size: number;
  uploadDate: Date;
  claimId?: string;
  thumbnailUrl?: string;
  compressedUrl?: string;
  metadata?: PhotoMetadata;
  checksum?: string;
}

export interface PhotoMetadata {
  width: number;
  height: number;
  format: string;
  originalSize: number;
  compressedSize?: number;
  aspectRatio: number;
  colorSpace?: string;
  hasTransparency?: boolean;
}

export interface PhotoServiceConfig {
  maxFileSize: number; // in bytes
  maxFiles: number;
  allowedTypes: string[];
  enableCompression: boolean;
  compressionQuality: number;
  thumbnailSize: number;
  enableThumbnails: boolean;
  retryAttempts: number;
  retryDelay: number;
}

// Default configuration
const DEFAULT_CONFIG: PhotoServiceConfig = {
  maxFileSize: 5 * 1024 * 1024, // 5MB
  maxFiles: 10,
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  enableCompression: true,
  compressionQuality: 0.8,
  thumbnailSize: 200,
  enableThumbnails: true,
  retryAttempts: 3,
  retryDelay: 1000
};

// Enhanced storage with indexing
const photoStorage = new Map<string, StoredPhoto>();
const photosByClaimId = new Map<string, Set<string>>();
const urlRegistry = new Set<string>(); // Track blob URLs for cleanup

let serviceConfig = { ...DEFAULT_CONFIG };

// Configuration management
export const updateServiceConfig = (newConfig: Partial<PhotoServiceConfig>): void => {
  serviceConfig = { ...serviceConfig, ...newConfig };
};

export const getServiceConfig = (): PhotoServiceConfig => ({ ...serviceConfig });

// Enhanced file validation with detailed error reporting
export const validatePhotoFile = (file: File): PhotoValidationResult => {
  const warnings: string[] = [];

  // Check file type
  if (!serviceConfig.allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: {
        type: PhotoErrorType.INVALID_FILE_TYPE,
        message: `File type ${file.type} is not supported.`,
        details: `Supported formats: ${serviceConfig.allowedTypes.join(', ')}`,
        retryable: false
      }
    };
  }

  // Check file size
  if (file.size > serviceConfig.maxFileSize) {
    return {
      isValid: false,
      error: {
        type: PhotoErrorType.FILE_TOO_LARGE,
        message: `File size exceeds maximum allowed size.`,
        details: `Current: ${formatFileSize(file.size)}, Maximum: ${formatFileSize(serviceConfig.maxFileSize)}`,
        retryable: false
      }
    };
  }

  // Check for potential issues
  if (file.size < 1024) {
    warnings.push('File size is very small, image quality may be poor');
  }

  if (file.size > serviceConfig.maxFileSize * 0.8) {
    warnings.push('File size is close to the maximum limit');
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  };
};

// Utility functions
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const generateChecksum = (input: string): string => {
  // Simple hash function for demo purposes
  let hash = 0;
  for (let i = 0; i < input.length; i++) {
    const char = input.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(16);
};

// Memory management utilities
export const cleanupPhotoUrl = (url: string): void => {
  if (url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
    urlRegistry.delete(url);
  }
};

export const cleanupAllUrls = (): void => {
  urlRegistry.forEach(url => {
    if (url.startsWith('blob:')) {
      URL.revokeObjectURL(url);
    }
  });
  urlRegistry.clear();
};

// Enhanced photo upload with retry logic and better error handling
export const uploadPhoto = async (
  photoFile: PhotoFile,
  claimId?: string,
  onProgress?: (progress: number) => void
): Promise<StoredPhoto> => {
  const uploadWithRetry = async (attempt: number = 1): Promise<StoredPhoto> => {
    return new Promise((resolve, reject) => {
      let progress = 0;
      const interval = setInterval(() => {
        progress += Math.random() * 20;
        if (progress > 100) progress = 100;

        onProgress?.(progress);

        if (progress >= 100) {
          clearInterval(interval);

          // Simulate occasional upload failure (5% chance, reduced on retries)
          const failureRate = Math.max(0.05 - (attempt - 1) * 0.02, 0.01);
          if (Math.random() < failureRate) {
            const error = new Error(`Upload failed on attempt ${attempt}. Please try again.`);
            reject(error);
            return;
          }

          // Generate metadata for the photo
          const metadata: PhotoMetadata = {
            width: 1920, // Mock dimensions - in real app, extract from image
            height: 1080,
            format: photoFile.file.type,
            originalSize: photoFile.size,
            aspectRatio: 1920 / 1080,
            colorSpace: 'sRGB'
          };

          // Create stored photo record
          const storedPhoto: StoredPhoto = {
            id: photoFile.id,
            url: photoFile.url, // In real app, this would be the server URL
            name: photoFile.name,
            size: photoFile.size,
            uploadDate: new Date(),
            claimId,
            metadata,
            checksum: generateChecksum(photoFile.name + photoFile.size) // Simple checksum
          };

          // Store in enhanced storage with indexing
          photoStorage.set(storedPhoto.id, storedPhoto);

          // Update claim index
          if (claimId) {
            if (!photosByClaimId.has(claimId)) {
              photosByClaimId.set(claimId, new Set());
            }
            photosByClaimId.get(claimId)!.add(storedPhoto.id);
          }

          // Track URL for cleanup
          urlRegistry.add(photoFile.url);

          resolve(storedPhoto);
        }
      }, 100);
    });
  };

  // Retry logic
  for (let attempt = 1; attempt <= serviceConfig.retryAttempts; attempt++) {
    try {
      return await uploadWithRetry(attempt);
    } catch (error) {
      if (attempt === serviceConfig.retryAttempts) {
        throw new Error(`Upload failed after ${serviceConfig.retryAttempts} attempts: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Wait before retry
      await new Promise(resolve => setTimeout(resolve, serviceConfig.retryDelay * attempt));
    }
  }

  throw new Error('Upload failed: Maximum retry attempts exceeded');
};

// Get photos for a specific claim
export const getPhotosForClaim = (claimId: string): StoredPhoto[] => {
  const photoIds = photosByClaimId.get(claimId);
  if (!photoIds) return [];

  return Array.from(photoIds)
    .map(id => photoStorage.get(id))
    .filter((photo): photo is StoredPhoto => photo !== undefined);
};

// Get a specific photo
export const getPhoto = (photoId: string): StoredPhoto | undefined => {
  return photoStorage.get(photoId);
};

// Delete a photo
export const deletePhoto = async (photoId: string): Promise<boolean> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  const photo = photoStorage.get(photoId);
  if (photo) {
    // Clean up blob URL
    if (photo.url.startsWith('blob:')) {
      URL.revokeObjectURL(photo.url);
    }

    // Remove from storage
    photoStorage.delete(photoId);

    // Remove from claim index
    if (photo.claimId) {
      const claimPhotos = photosByClaimId.get(photo.claimId);
      if (claimPhotos) {
        claimPhotos.delete(photoId);
        if (claimPhotos.size === 0) {
          photosByClaimId.delete(photo.claimId);
        }
      }
    }

    // Remove from URL registry
    urlRegistry.delete(photo.url);

    return true;
  }
  return false;
};

// Batch upload photos
export const uploadPhotos = async (
  photoFiles: PhotoFile[],
  claimId?: string,
  onProgress?: (photoId: string, progress: number) => void
): Promise<StoredPhoto[]> => {
  const uploadPromises = photoFiles.map(photoFile =>
    uploadPhoto(photoFile, claimId, (progress) => {
      onProgress?.(photoFile.id, progress);
    })
  );

  try {
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Batch upload failed:', error);
    throw error;
  }
};

// Convert PhotoFile to StoredPhoto (for immediate display)
export const convertToStoredPhoto = (photoFile: PhotoFile, claimId?: string): StoredPhoto => {
  return {
    id: photoFile.id,
    url: photoFile.url,
    name: photoFile.name,
    size: photoFile.size,
    uploadDate: new Date(),
    claimId
  };
};

// Generate a unique photo ID
export const generatePhotoId = (): string => {
  return `photo_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Get photo count for a claim (optimized using index)
export const getPhotoCountForClaim = (claimId: string): number => {
  const photoIds = photosByClaimId.get(claimId);
  return photoIds ? photoIds.size : 0;
};

// Check if claim has photos (optimized using index)
export const claimHasPhotos = (claimId: string): boolean => {
  return getPhotoCountForClaim(claimId) > 0;
};

// Mock function to simulate server-side photo processing
export const processPhoto = async (photoFile: PhotoFile): Promise<{
  thumbnailUrl: string;
  compressedUrl: string;
  metadata: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
}> => {
  // Simulate processing delay
  await new Promise(resolve => setTimeout(resolve, 1000));

  return {
    thumbnailUrl: photoFile.url, // In real app, this would be a thumbnail
    compressedUrl: photoFile.url, // In real app, this would be compressed
    metadata: {
      width: 1920, // Mock dimensions
      height: 1080,
      format: photoFile.file.type,
      size: photoFile.size
    }
  };
};

// Export all photos (for debugging/admin purposes)
export const getAllPhotos = (): StoredPhoto[] => {
  return Array.from(photoStorage.values());
};

// Clear all photos (for testing purposes)
export const clearAllPhotos = (): void => {
  // Clean up all blob URLs
  for (const photo of photoStorage.values()) {
    if (photo.url.startsWith('blob:')) {
      URL.revokeObjectURL(photo.url);
    }
  }

  // Clear all storage
  photoStorage.clear();
  photosByClaimId.clear();
  urlRegistry.clear();
};

// Enhanced photo statistics
export const getPhotoStatistics = () => {
  const photos = getAllPhotos();
  const totalSize = photos.reduce((sum, photo) => sum + photo.size, 0);
  const averageSize = photos.length > 0 ? totalSize / photos.length : 0;

  // Group by file type
  const typeStats = photos.reduce((acc, photo) => {
    const type = photo.metadata?.format || 'unknown';
    acc[type] = (acc[type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    totalPhotos: photos.length,
    totalSize,
    averageSize,
    formattedTotalSize: formatFileSize(totalSize),
    formattedAverageSize: formatFileSize(averageSize),
    typeDistribution: typeStats,
    claimsWithPhotos: photosByClaimId.size,
    urlsTracked: urlRegistry.size
  };
};

// Batch operations
export const deletePhotosForClaim = async (claimId: string): Promise<number> => {
  const photoIds = photosByClaimId.get(claimId);
  if (!photoIds) return 0;

  let deletedCount = 0;
  for (const photoId of Array.from(photoIds)) {
    const success = await deletePhoto(photoId);
    if (success) deletedCount++;
  }

  return deletedCount;
};

// Service health and monitoring
export const getServiceHealth = () => {
  const stats = getPhotoStatistics();
  const memoryUsage = stats.totalSize;
  const maxMemory = serviceConfig.maxFileSize * serviceConfig.maxFiles * 10; // Rough estimate

  return {
    status: memoryUsage > maxMemory * 0.9 ? 'warning' : 'healthy',
    memoryUsage: {
      used: memoryUsage,
      max: maxMemory,
      percentage: (memoryUsage / maxMemory) * 100,
      formatted: {
        used: formatFileSize(memoryUsage),
        max: formatFileSize(maxMemory)
      }
    },
    storage: {
      photos: stats.totalPhotos,
      claims: stats.claimsWithPhotos,
      urlsTracked: stats.urlsTracked
    },
    config: serviceConfig
  };
};
