import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react";
import { CAPA, PreventiveAction } from '@/models/capa';

interface EditPreventiveActionDialogProps {
  open: boolean;
  onClose: () => void;
  capas: CAPA[];
  action: PreventiveAction | null;
  onEdit: (editedAction: PreventiveAction) => void;
}

export function EditPreventiveActionDialog({ open, onClose, capas, action, onEdit }: EditPreventiveActionDialogProps) {
  const [capaId, setCapaId] = useState(action?.capaId || '');
  const [description, setDescription] = useState(action?.description || '');
  const [targetDate, setTargetDate] = useState<Date | undefined>(action?.targetDate || undefined);
  const [completionDate, setCompletionDate] = useState<Date | undefined>(action?.completionDate || undefined);
  const [responsiblePerson, setResponsiblePerson] = useState(action?.responsiblePerson || '');
  const [verificationMethod, setVerificationMethod] = useState(action?.verificationMethod || '');
  const [verificationResults, setVerificationResults] = useState(action?.verificationResults || '');
  const [verifiedBy, setVerifiedBy] = useState(action?.verifiedBy || '');
  const [verificationDate, setVerificationDate] = useState<Date | undefined>(action?.verificationDate || undefined);
  const [effectiveness, setEffectiveness] = useState<'effective' | 'partially_effective' | 'not_effective' | 'not_verified'>(action?.effectiveness || 'not_verified');
  const [status, setStatus] = useState<'planned' | 'in_progress' | 'completed' | 'verified' | 'delayed'>(action?.status || 'planned');
  const [notes, setNotes] = useState(action?.notes || '');

  const handleSubmit = () => {
    // Validate form fields
    if (!capaId || !description || !targetDate || !responsiblePerson || !verificationMethod) {
      alert('Please fill in all required fields.');
      return;
    }

    // Create edited preventive action object
    const editedAction = {
      id: action?.id || Math.random().toString(36).substring(7), // Use existing ID or generate a new one
      capaId,
      description,
      targetDate,
      completionDate,
      responsiblePerson,
      verificationMethod,
      verificationResults,
      verifiedBy,
      verificationDate,
      effectiveness,
      status,
      notes,
      createdAt: action?.createdAt || new Date(),
      updatedAt: new Date(),
      createdBy: action?.createdBy || 'user1',
      updatedBy: 'user1',
    };

    onEdit(editedAction as PreventiveAction);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Preventive Action</DialogTitle>
          <DialogDescription>
            Edit an existing preventive action.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="capa" className="text-right">
              CAPA
            </Label>
            <select
              id="capa"
              value={capaId}
              onChange={(e) => setCapaId(e.target.value)}
              className="col-span-3 rounded-md border border-gray-200 px-2 py-1 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">Select CAPA</option>
              {capas.map((capa) => (
                <option key={capa.id} value={capa.id}>{capa.title}</option>
              ))}
            </select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="targetDate" className="text-right">
              Target Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "col-span-3 pl-3 text-left font-normal",
                    !targetDate && "text-muted-foreground"
                  )}
                >
                  {targetDate ? format(targetDate, "PPP") : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={targetDate}
                  onSelect={setTargetDate}
                  disabled={(date) =>
                    date < new Date()
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="completionDate" className="text-right">
              Completion Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "col-span-3 pl-3 text-left font-normal",
                    !completionDate && "text-muted-foreground"
                  )}
                >
                  {completionDate ? format(completionDate, "PPP") : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={completionDate}
                  onSelect={setCompletionDate}
                  disabled={(date) =>
                    date < new Date()
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="responsiblePerson" className="text-right">
              Responsible Person
            </Label>
            <Input
              type="text"
              id="responsiblePerson"
              value={responsiblePerson}
              onChange={(e) => setResponsiblePerson(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="verificationMethod" className="text-right">
              Verification Method
            </Label>
            <Textarea
              id="verificationMethod"
              value={verificationMethod}
              onChange={(e) => setVerificationMethod(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="verificationResults" className="text-right">
              Verification Results
            </Label>
            <Textarea
              id="verificationResults"
              value={verificationResults}
              onChange={(e) => setVerificationResults(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="verifiedBy" className="text-right">
              Verified By
            </Label>
            <Input
              type="text"
              id="verifiedBy"
              value={verifiedBy}
              onChange={(e) => setVerifiedBy(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="verificationDate" className="text-right">
              Verification Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "col-span-3 pl-3 text-left font-normal",
                    !verificationDate && "text-muted-foreground"
                  )}
                >
                  {verificationDate ? format(verificationDate, "PPP") : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={verificationDate}
                  onSelect={setVerificationDate}
                  disabled={(date) =>
                    date < new Date()
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="effectiveness" className="text-right">
              Effectiveness
            </Label>
            <select
              id="effectiveness"
              value={effectiveness}
              onChange={(e) => setEffectiveness(e.target.value as 'effective' | 'partially_effective' | 'not_effective' | 'not_verified')}
              className="col-span-3 rounded-md border border-gray-200 px-2 py-1 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="effective">Effective</option>
              <option value="partially_effective">Partially Effective</option>
              <option value="not_effective">Not Effective</option>
              <option value="not_verified">Not Verified</option>
            </select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <select
              id="status"
              value={status}
              onChange={(e) => setStatus(e.target.value as 'planned' | 'in_progress' | 'completed' | 'verified' | 'delayed')}
              className="col-span-3 rounded-md border border-gray-200 px-2 py-1 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="planned">Planned</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="verified">Verified</option>
              <option value="delayed">Delayed</option>
            </select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              Notes/Comments
            </Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit}>Edit Action</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
