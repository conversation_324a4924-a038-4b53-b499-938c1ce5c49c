import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, AlertTriangle, Info } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { ProcessStep, Hazard } from '@/models/types';

interface ProcessStepCardProps {
  step: ProcessStep;
  isCCP: boolean;
  hasHazards: boolean;
  hazards?: Hazard[];
  isDragging?: boolean;
  onClick?: () => void;
  className?: string;
}

export const ProcessStepCard: React.FC<ProcessStepCardProps> = ({
  step,
  isCCP,
  hasHazards,
  hazards = [],
  isDragging = false,
  onClick,
  className,
}) => {
  // Determine the card style based on CCP status and hazards
  const getCardStyle = () => {
    if (isCCP) {
      return 'border-yellow-400 bg-yellow-50 shadow-md';
    } else if (hasHazards) {
      return 'border-orange-200 bg-orange-50';
    } else {
      return 'border-gray-200 bg-muted/30';
    }
  };

  // Get hazard summary for tooltip
  const getHazardSummary = () => {
    if (!hazards || hazards.length === 0) {
      return 'No hazards identified for this step';
    }

    return (
      <div className="space-y-2 max-w-xs">
        <p className="font-semibold">Identified Hazards:</p>
        <ul className="list-disc pl-4 space-y-1">
          {hazards.slice(0, 3).map((hazard) => (
            <li key={hazard.id} className="text-xs">
              {hazard.description} ({hazard.type})
            </li>
          ))}
          {hazards.length > 3 && (
            <li className="text-xs font-medium">
              +{hazards.length - 3} more hazards...
            </li>
          )}
        </ul>
      </div>
    );
  };

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <Card
            className={cn(
              'p-3 w-full md:w-64 text-center transition-all duration-200 cursor-pointer',
              getCardStyle(),
              isDragging && 'ring-2 ring-primary shadow-lg',
              isCCP && 'ring-1 ring-yellow-400',
              className
            )}
            onClick={onClick}
          >
            <div className="flex flex-col items-center space-y-1">
              <div className="flex items-center justify-center w-full">
                <span className="font-medium truncate">{step.name}</span>
                {isCCP && (
                  <Badge className="ml-2 bg-yellow-500 hover:bg-yellow-600">CCP</Badge>
                )}
              </div>
              
              <div className="flex items-center justify-center space-x-1 text-xs text-muted-foreground">
                <span>Step {step.order}</span>
                {hasHazards && !isCCP && (
                  <AlertCircle className="h-3 w-3 text-orange-500" />
                )}
                {isCCP && (
                  <AlertTriangle className="h-3 w-3 text-yellow-600" />
                )}
              </div>
            </div>
          </Card>
        </TooltipTrigger>
        <TooltipContent side="top" align="center" className="max-w-xs">
          <div className="space-y-1">
            <div className="font-semibold flex items-center">
              <span>{step.name}</span>
              {isCCP && (
                <Badge className="ml-2 bg-yellow-500 hover:bg-yellow-600">CCP</Badge>
              )}
            </div>
            <p className="text-xs">{step.description}</p>
            <div className="pt-1 text-xs">
              {getHazardSummary()}
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

export default ProcessStepCard;
