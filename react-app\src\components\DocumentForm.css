/* Form Styles */
.input-form {
  padding: var(--spacing-xl);
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.form-group {
  position: relative;
}

.form-group label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.875rem;
}

.form-group label i {
  color: var(--primary-color);
  font-size: 0.875rem;
}

.form-group label.required::after {
  content: '*';
  color: var(--danger-color);
  margin-left: var(--spacing-xs);
  font-weight: 600;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-size: 0.875rem;
  background: var(--bg-primary);
  color: var(--text-primary);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  background: var(--white);
}

.form-group input::placeholder {
  color: var(--text-muted);
}

.form-group input.error,
.form-group select.error {
  border-color: var(--danger-color);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Error Messages */
.error-message {
  color: var(--danger-color);
  font-size: 0.75rem;
  margin-top: var(--spacing-xs);
  font-weight: 500;
}

/* Tooltip Styles */
.tooltip-container {
  position: relative;
}

.tooltip {
  position: absolute;
  right: 0.75rem;
  top: 2.25rem;
  cursor: help;
  color: var(--text-muted);
  z-index: 10;
}

.tooltip:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

.tooltip-text {
  visibility: hidden;
  width: 200px;
  background-color: var(--gray-800);
  color: var(--white);
  text-align: center;
  border-radius: var(--border-radius);
  padding: var(--spacing-sm);
  position: absolute;
  z-index: 20;
  bottom: 125%;
  right: 0;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.75rem;
  font-weight: 400;
}

.tooltip-text::after {
  content: "";
  position: absolute;
  top: 100%;
  right: 1rem;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--gray-800) transparent transparent transparent;
}

/* File Upload Group */
.file-upload-group {
  grid-column: 1 / -1;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  margin-top: var(--spacing-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .form-actions {
    flex-direction: column-reverse;
  }

  .input-form {
    padding: var(--spacing-md);
  }
}

@media (max-width: 480px) {
  .input-form {
    padding: var(--spacing-md);
  }
}
