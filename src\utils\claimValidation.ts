import { Claim } from '@/models/capa';

const validateClaimForm = (values: Partial<Claim>) => {
  const errors: Record<string, string> = {};

  if (!values.customerName?.trim()) {
    errors.customerName = 'Customer name is required';
  }

  if (!values.productId) {
    errors.productId = 'Product selection is required';
  }

  if (!values.description?.trim()) {
    errors.description = 'Description is required';
  } else if (values.description.length < 10) {
    errors.description = 'Description must be at least 10 characters';
  }

  return { errors, isValid: Object.keys(errors).length === 0 };
};

export default validateClaimForm;