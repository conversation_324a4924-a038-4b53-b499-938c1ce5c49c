
import React from 'react';
import { cn } from '@/lib/utils';

interface HazardMatrixProps {
  selectedSeverity?: number;
  selectedLikelihood?: number;
  onSelect?: (severity: number, likelihood: number) => void;
  readonly?: boolean;
  className?: string;
}

export function HazardMatrix({
  selectedSeverity,
  selectedLikelihood,
  onSelect,
  readonly = false,
  className
}: HazardMatrixProps) {
  // Matrix dimensions
  const severityLevels = 5;
  const likelihoodLevels = 5;
  
  const handleCellClick = (severity: number, likelihood: number) => {
    if (!readonly && onSelect) {
      onSelect(severity, likelihood);
    }
  };
  
  const getCellColor = (severity: number, likelihood: number) => {
    const score = severity * likelihood;
    
    if (score <= 6) return 'bg-green-100 hover:bg-green-200';
    if (score <= 15) return 'bg-yellow-100 hover:bg-yellow-200';
    return 'bg-red-100 hover:bg-red-200';
  };
  
  return (
    <div className={cn("w-full max-w-xl", className)}>
      <div className="flex flex-col">
        {/* Header row with likelihood values */}
        <div className="flex">
          <div className="w-20 h-20 border flex items-center justify-center bg-muted font-semibold">
            <div className="transform -rotate-45">Severity / Likelihood</div>
          </div>
          {[...Array(likelihoodLevels)].map((_, idx) => (
            <div 
              key={`likelihood-${idx + 1}`}
              className="w-16 h-16 border flex items-center justify-center bg-muted font-semibold"
            >
              {idx + 1}
            </div>
          ))}
        </div>
        
        {/* Matrix body */}
        {[...Array(severityLevels)].map((_, sevIdx) => {
          const severity = severityLevels - sevIdx;
          
          return (
            <div key={`severity-${severity}`} className="flex">
              {/* Severity label column */}
              <div className="w-20 h-16 border flex items-center justify-center bg-muted font-semibold">
                {severity}
              </div>
              
              {/* Matrix cells */}
              {[...Array(likelihoodLevels)].map((_, likeIdx) => {
                const likelihood = likeIdx + 1;
                const isSelected = selectedSeverity === severity && selectedLikelihood === likelihood;
                
                return (
                  <div
                    key={`cell-${severity}-${likelihood}`}
                    className={cn(
                      "w-16 h-16 border flex items-center justify-center text-sm font-medium",
                      getCellColor(severity, likelihood),
                      isSelected ? "ring-2 ring-primary ring-inset" : "",
                      !readonly ? "cursor-pointer" : "",
                    )}
                    onClick={() => handleCellClick(severity, likelihood)}
                  >
                    {severity * likelihood}
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
      
      <div className="flex justify-between mt-4">
        <div className="flex items-center">
          <span className="inline-block w-4 h-4 bg-green-100 mr-2 border border-green-500"></span>
          <span className="text-sm">Low Risk (1-6)</span>
        </div>
        <div className="flex items-center">
          <span className="inline-block w-4 h-4 bg-yellow-100 mr-2 border border-yellow-500"></span>
          <span className="text-sm">Medium Risk (8-15)</span>
        </div>
        <div className="flex items-center">
          <span className="inline-block w-4 h-4 bg-red-100 mr-2 border border-red-500"></span>
          <span className="text-sm">High Risk (16-25)</span>
        </div>
      </div>
    </div>
  );
}

export default HazardMatrix;
