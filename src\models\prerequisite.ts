// Prerequisite Program Types for FSSC 22000 compliance

export type PrerequisiteStatus = 'implemented' | 'in_progress' | 'planned' | 'not_applicable';

export type PrerequisiteCategory = 
  | 'facilities' 
  | 'supplier_control' 
  | 'cleaning_sanitation' 
  | 'personal_hygiene' 
  | 'training' 
  | 'chemical_control' 
  | 'receiving_storage_shipping' 
  | 'traceability_recall' 
  | 'pest_control'
  | 'equipment_maintenance'
  | 'allergen_management'
  | 'environmental_monitoring'
  | 'foreign_material_control'
  | 'waste_management';

export interface PrerequisiteProgram {
  id: string;
  category: PrerequisiteCategory;
  name: string;
  description: string;
  status: PrerequisiteStatus;
  responsiblePerson: string;
  documentReference: string;
  lastReviewDate: Date;
  nextReviewDate: Date;
  verificationActivities: string;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Category information with descriptions
export const PREREQUISITE_CATEGORIES: Record<PrerequisiteCategory, { title: string; description: string }> = {
  facilities: {
    title: 'Facilities Management',
    description: 'Construction and layout of buildings and associated utilities to prevent contamination'
  },
  supplier_control: {
    title: 'Supplier Control',
    description: 'Evaluation and approval of suppliers to ensure safety of incoming materials'
  },
  cleaning_sanitation: {
    title: 'Cleaning and Sanitation',
    description: 'Systems to ensure appropriate cleaning and sanitizing of equipment and facilities'
  },
  personal_hygiene: {
    title: 'Personal Hygiene',
    description: 'Requirements for personal cleanliness and behavior to prevent contamination'
  },
  training: {
    title: 'Training',
    description: 'Programs to ensure staff are trained in food safety principles and practices'
  },
  chemical_control: {
    title: 'Chemical Control',
    description: 'Procedures for the safe handling, use and storage of chemicals'
  },
  receiving_storage_shipping: {
    title: 'Receiving, Storage and Shipping',
    description: 'Controls for the receipt, storage and dispatch of materials and products'
  },
  traceability_recall: {
    title: 'Traceability and Recall',
    description: 'Systems to identify, locate and remove unsafe products from the market'
  },
  pest_control: {
    title: 'Pest Control',
    description: 'Measures to prevent pest infestation and contamination'
  },
  equipment_maintenance: {
    title: 'Equipment Maintenance',
    description: 'Preventive maintenance program for equipment to ensure food safety'
  },
  allergen_management: {
    title: 'Allergen Management',
    description: 'Controls to prevent cross-contamination of allergens'
  },
  environmental_monitoring: {
    title: 'Environmental Monitoring',
    description: 'Program to verify the effectiveness of sanitation procedures'
  },
  foreign_material_control: {
    title: 'Foreign Material Control',
    description: 'Measures to prevent, detect and control foreign materials'
  },
  waste_management: {
    title: 'Waste Management',
    description: 'Procedures for the handling and disposal of waste materials'
  }
};

// Mock data for prerequisite programs
export const MOCK_PREREQUISITE_PROGRAMS: PrerequisiteProgram[] = [
  {
    id: 'prp1',
    category: 'cleaning_sanitation',
    name: 'Master Cleaning Schedule',
    description: 'Comprehensive cleaning and sanitation program for all food contact surfaces and processing areas',
    status: 'implemented',
    responsiblePerson: 'Sanitation Manager',
    documentReference: 'SOP-CL-001',
    lastReviewDate: new Date('2023-12-15'),
    nextReviewDate: new Date('2024-12-15'),
    verificationActivities: 'Monthly environmental sampling, ATP testing, visual inspection',
    createdBy: 'user1',
    createdAt: new Date('2023-01-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-12-15')
  },
  {
    id: 'prp2',
    category: 'personal_hygiene',
    name: 'Personnel Hygiene Requirements',
    description: 'Standards for personal cleanliness, protective clothing, and behavior in production areas',
    status: 'implemented',
    responsiblePerson: 'QA Manager',
    documentReference: 'SOP-PH-001',
    lastReviewDate: new Date('2024-01-20'),
    nextReviewDate: new Date('2025-01-20'),
    verificationActivities: 'Daily GMP audits, hand swab testing quarterly',
    createdBy: 'user1',
    createdAt: new Date('2023-01-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'prp3',
    category: 'allergen_management',
    name: 'Allergen Control Program',
    description: 'Procedures to prevent cross-contact of allergenic ingredients',
    status: 'implemented',
    responsiblePerson: 'Production Manager',
    documentReference: 'SOP-AL-001',
    lastReviewDate: new Date('2024-02-10'),
    nextReviewDate: new Date('2025-02-10'),
    verificationActivities: 'Allergen testing after cleaning, label verification',
    createdBy: 'user1',
    createdAt: new Date('2023-02-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-02-10')
  }
];
