import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  AlertTriangle,
  CheckCircle,
  ClipboardList,
  FileText,
  ShieldAlert,
  Calendar,
  Users,
  Activity,
  BookOpen
} from 'lucide-react';
import { format, isAfter, isBefore, addDays } from 'date-fns';
import {
  Allergen,
  AllergenControlMeasure,
  AllergenRiskAssessment,
  ProductAllergen,
  VerificationActivity,
  TrainingRecord
} from '@/models/allergen';

interface AllergenDashboardSummaryProps {
  allergens: Allergen[];
  controlMeasures: AllergenControlMeasure[];
  riskAssessments: AllergenRiskAssessment[];
  productAllergens: ProductAllergen[];
  verificationActivities?: VerificationActivity[];
  trainingRecords?: TrainingRecord[];
}

export function AllergenDashboardSummary({
  allergens,
  controlMeasures,
  riskAssessments,
  productAllergens,
  verificationActivities = [],
  trainingRecords = []
}: AllergenDashboardSummaryProps) {

  // Calculate statistics
  const totalAllergens = allergens.length;
  const highRiskAllergens = allergens.filter(a => a.riskLevel === 'high').length;
  const presentAllergens = new Set(productAllergens.filter(pa => pa.status === 'present').map(pa => pa.allergenId)).size;
  const mayContainAllergens = new Set(productAllergens.filter(pa => pa.status === 'may_contain').map(pa => pa.allergenId)).size;

  // Calculate control measure types
  const controlMeasureTypes = controlMeasures.reduce((acc, cm) => {
    acc[cm.type] = (acc[cm.type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Calculate upcoming reviews
  const today = new Date();
  const upcomingReviews = riskAssessments
    .filter(ra => isAfter(ra.nextReviewDate, today) && isBefore(ra.nextReviewDate, addDays(today, 30)))
    .sort((a, b) => a.nextReviewDate.getTime() - b.nextReviewDate.getTime());

  // Calculate verification activities statistics
  const totalVerifications = verificationActivities.length;
  const passedVerifications = verificationActivities.filter(va => va.result === 'pass').length;
  const failedVerifications = verificationActivities.filter(va => va.result === 'fail').length;
  const pendingVerifications = verificationActivities.filter(va => va.result === 'pending').length;

  // Calculate training records statistics
  const totalTrainings = trainingRecords.length;
  const upcomingTrainings = trainingRecords
    .filter(tr => tr.nextTrainingDate && isAfter(tr.nextTrainingDate, today) && isBefore(tr.nextTrainingDate, addDays(today, 60)))
    .sort((a, b) => (a.nextTrainingDate as Date).getTime() - (b.nextTrainingDate as Date).getTime());

  // Calculate compliance percentage based on verification activities and control measures
  const verificationPassRate = totalVerifications > 0 ? (passedVerifications / totalVerifications) * 100 : 0;
  const controlMeasureImplementationRate = controlMeasures.length > 0 ? 100 : 0;
  const compliancePercentage = Math.round((verificationPassRate * 0.6) + (controlMeasureImplementationRate * 0.4));

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {/* Allergen Statistics Card */}
        <Card className="xl:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <ShieldAlert className="h-5 w-5 mr-2 text-amber-500" />
              Allergen Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Allergens:</span>
                <span className="font-medium">{totalAllergens}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">High Risk Allergens:</span>
                <span className="font-medium">{highRiskAllergens}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Present in Products:</span>
                <span className="font-medium">{presentAllergens}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">May Contain:</span>
                <span className="font-medium">{mayContainAllergens}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Control Measures Card */}
        <Card className="xl:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <ClipboardList className="h-5 w-5 mr-2 text-blue-500" />
              Control Measures
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Measures:</span>
                <span className="font-medium">{controlMeasures.length}</span>
              </div>
              {Object.entries(controlMeasureTypes).map(([type, count]) => (
                <div key={type} className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground capitalize">{type.replace('_', ' ')}:</span>
                  <span className="font-medium">{count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Verification Activities Card */}
        <Card className="xl:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Activity className="h-5 w-5 mr-2 text-green-500" />
              Verification Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Activities:</span>
                <span className="font-medium">{totalVerifications}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Passed:</span>
                <span className="font-medium text-green-600">{passedVerifications}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Failed:</span>
                <span className="font-medium text-red-600">{failedVerifications}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Pending:</span>
                <span className="font-medium text-amber-600">{pendingVerifications}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Training Records Card */}
        <Card className="xl:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <BookOpen className="h-5 w-5 mr-2 text-indigo-500" />
              Training Records
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Trainings:</span>
                <span className="font-medium">{totalTrainings}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Upcoming Trainings:</span>
                <span className="font-medium">{upcomingTrainings.length}</span>
              </div>
              {upcomingTrainings.length > 0 && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-muted-foreground">Next Training:</span>
                  <span className="font-medium">{format(upcomingTrainings[0].nextTrainingDate as Date, 'MMM d, yyyy')}</span>
                </div>
              )}
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Trained Personnel:</span>
                <span className="font-medium">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                    {trainingRecords.reduce((total, record) => total + record.attendees.length, 0)}
                  </div>
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Compliance Card */}
        <Card className="xl:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              FSSC 22000 Compliance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Overall Compliance:</span>
                <span className="font-medium">{compliancePercentage}%</span>
              </div>
              <Progress value={compliancePercentage} className="h-2" />
              <div className="pt-2">
                <Badge className={compliancePercentage >= 90 ? "bg-green-500" : compliancePercentage >= 70 ? "bg-amber-500" : "bg-red-500"}>
                  {compliancePercentage >= 90 ? "Compliant" : compliancePercentage >= 70 ? "Needs Improvement" : "Non-Compliant"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Reviews Card */}
        <Card className="xl:col-span-2">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-purple-500" />
              Upcoming Reviews
            </CardTitle>
          </CardHeader>
          <CardContent>
            {upcomingReviews.length === 0 ? (
              <p className="text-sm text-muted-foreground">No upcoming reviews in the next 30 days.</p>
            ) : (
              <div className="space-y-2">
                {upcomingReviews.slice(0, 3).map((review) => (
                  <div key={review.id} className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">
                      Product {review.productId.replace('product', '')}:
                    </span>
                    <span className="font-medium">{format(review.nextReviewDate, 'MMM d, yyyy')}</span>
                  </div>
                ))}
                {upcomingReviews.length > 3 && (
                  <p className="text-xs text-muted-foreground text-right">
                    +{upcomingReviews.length - 3} more
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* FSSC 22000 Requirements Card */}
      <Card>
        <CardHeader>
          <CardTitle>FSSC 22000 v6.0 Allergen Management Requirements</CardTitle>
          <CardDescription>
            Key requirements for allergen management compliance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Allergen Inventory</p>
                    <p className="text-sm text-muted-foreground">Comprehensive list of allergens present in the facility</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Risk Assessment</p>
                    <p className="text-sm text-muted-foreground">Documented assessment of allergen risks in all products</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Product-Allergen Matrix</p>
                    <p className="text-sm text-muted-foreground">Clear mapping of allergens to products</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Control Measures</p>
                    <p className="text-sm text-muted-foreground">Implemented and validated control measures to prevent cross-contamination</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Allergen Labeling</p>
                    <p className="text-sm text-muted-foreground">Accurate allergen information on product labels</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Documentation</p>
                    <p className="text-sm text-muted-foreground">Complete documentation of allergen management program</p>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Verification Activities</p>
                    <p className="text-sm text-muted-foreground">Regular verification of allergen control effectiveness</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Employee Training</p>
                    <p className="text-sm text-muted-foreground">Comprehensive allergen awareness training for all staff</p>
                  </div>
                </div>

                <div className="flex items-start gap-2">
                  <div className="mt-0.5">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  </div>
                  <div>
                    <p className="font-medium">Continuous Improvement</p>
                    <p className="text-sm text-muted-foreground">Regular review and updating of allergen management program</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
