import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { TrainingRecord, TrainingType } from '@/models/allergen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, PenLine, Trash2, Search, Users, Clock, Calendar, FileText } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { format } from 'date-fns';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';

interface TrainingRecordsProps {
  trainingRecords: TrainingRecord[];
  setTrainingRecords: React.Dispatch<React.SetStateAction<TrainingRecord[]>>;
}

export function TrainingRecords({
  trainingRecords,
  setTrainingRecords
}: TrainingRecordsProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<TrainingRecord | null>(null);

  // Form state
  const [formValues, setFormValues] = useState<Partial<TrainingRecord>>({
    type: 'allergen_awareness',
    title: '',
    description: '',
    date: new Date(),
    duration: 60,
    trainer: '',
    attendees: [],
    materials: ''
  });

  // Filter records based on search query
  const filteredRecords = trainingRecords.filter(record =>
    record.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    record.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    record.trainer.toLowerCase().includes(searchQuery.toLowerCase()) ||
    getTrainingTypeName(record.type).toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormValues(prev => ({ ...prev, date }));
    }
  };

  const handleNextTrainingDateChange = (date: Date | undefined) => {
    setFormValues(prev => ({ ...prev, nextTrainingDate: date }));
  };

  const handleAttendeesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const attendeesArray = e.target.value.split(',').map(name => name.trim()).filter(name => name !== '');
    setFormValues(prev => ({ ...prev, attendees: attendeesArray }));
  };

  const handleDurationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const duration = parseInt(e.target.value);
    if (!isNaN(duration) && duration > 0) {
      setFormValues(prev => ({ ...prev, duration }));
    }
  };

  const openAddDialog = () => {
    setFormValues({
      type: 'allergen_awareness',
      title: '',
      description: '',
      date: new Date(),
      duration: 60,
      trainer: '',
      attendees: [],
      materials: ''
    });
    setIsAddDialogOpen(true);
  };

  const openEditDialog = (record: TrainingRecord) => {
    setSelectedRecord(record);
    setFormValues({
      type: record.type,
      title: record.title,
      description: record.description,
      date: record.date,
      duration: record.duration,
      trainer: record.trainer,
      attendees: record.attendees,
      materials: record.materials,
      assessmentResults: record.assessmentResults,
      nextTrainingDate: record.nextTrainingDate
    });
    setIsEditDialogOpen(true);
  };

  const handleAddRecord = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.title?.trim()) {
      toast({
        title: "Error",
        description: "Training title is required",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.trainer?.trim()) {
      toast({
        title: "Error",
        description: "Trainer name is required",
        variant: "destructive"
      });
      return;
    }

    const newRecord: TrainingRecord = {
      id: `tr${trainingRecords.length + 1}`,
      type: formValues.type as TrainingType,
      title: formValues.title || '',
      description: formValues.description || '',
      date: formValues.date || new Date(),
      duration: formValues.duration || 60,
      trainer: formValues.trainer || '',
      attendees: formValues.attendees || [],
      materials: formValues.materials || '',
      assessmentResults: formValues.assessmentResults,
      nextTrainingDate: formValues.nextTrainingDate,
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };

    setTrainingRecords([...trainingRecords, newRecord]);
    setIsAddDialogOpen(false);

    toast({
      title: "Success",
      description: "Training record added successfully"
    });
  };

  const handleEditRecord = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.title?.trim()) {
      toast({
        title: "Error",
        description: "Training title is required",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.trainer?.trim()) {
      toast({
        title: "Error",
        description: "Trainer name is required",
        variant: "destructive"
      });
      return;
    }

    if (!selectedRecord) return;

    const updatedRecords = trainingRecords.map(record =>
      record.id === selectedRecord.id
        ? {
            ...record,
            type: formValues.type as TrainingType,
            title: formValues.title || '',
            description: formValues.description || '',
            date: formValues.date || new Date(),
            duration: formValues.duration || 60,
            trainer: formValues.trainer || '',
            attendees: formValues.attendees || [],
            materials: formValues.materials || '',
            assessmentResults: formValues.assessmentResults,
            nextTrainingDate: formValues.nextTrainingDate,
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : record
    );

    setTrainingRecords(updatedRecords);
    setIsEditDialogOpen(false);

    toast({
      title: "Success",
      description: "Training record updated successfully"
    });
  };

  const handleDeleteRecord = (id: string) => {
    setTrainingRecords(trainingRecords.filter(record => record.id !== id));

    toast({
      title: "Success",
      description: "Training record deleted successfully"
    });
  };

  const getTrainingTypeName = (type: TrainingType): string => {
    switch (type) {
      case 'allergen_awareness': return 'Allergen Awareness';
      case 'cross_contact_prevention': return 'Cross-Contact Prevention';
      case 'cleaning_procedures': return 'Cleaning Procedures';
      case 'label_verification': return 'Label Verification';
      case 'emergency_response': return 'Emergency Response';
      default: return type;
    }
  };

  const formatDuration = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;

    if (hours === 0) {
      return `${mins} min`;
    } else if (mins === 0) {
      return `${hours} hr`;
    } else {
      return `${hours} hr ${mins} min`;
    }
  };

  const getTrainingTypeBadge = (type: TrainingType) => {
    switch (type) {
      case 'allergen_awareness':
        return <Badge className="bg-blue-500"><FileText className="h-3 w-3 mr-1" /> Awareness</Badge>;
      case 'cross_contact_prevention':
        return <Badge className="bg-yellow-500"><FileText className="h-3 w-3 mr-1" /> Cross-Contact</Badge>;
      case 'cleaning_procedures':
        return <Badge className="bg-green-500"><FileText className="h-3 w-3 mr-1" /> Cleaning</Badge>;
      case 'label_verification':
        return <Badge className="bg-purple-500"><FileText className="h-3 w-3 mr-1" /> Labeling</Badge>;
      case 'emergency_response':
        return <Badge className="bg-red-500"><FileText className="h-3 w-3 mr-1" /> Emergency</Badge>;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Training Records</CardTitle>
              <CardDescription>
                Document and track allergen management training
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Training Record
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search training records..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Trainer</TableHead>
                  <TableHead>Attendees</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredRecords.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No training records found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredRecords.map((record) => (
                    <TableRow key={record.id}>
                      <TableCell className="font-medium">{record.title}</TableCell>
                      <TableCell>{getTrainingTypeBadge(record.type)}</TableCell>
                      <TableCell>{format(record.date, 'MMM d, yyyy')}</TableCell>
                      <TableCell>{formatDuration(record.duration)}</TableCell>
                      <TableCell>{record.trainer}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                          <span>{record.attendees.length}</span>
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" size="sm" onClick={() => openEditDialog(record)}>
                            <PenLine className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteRecord(record.id)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Training Record Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Training Record</DialogTitle>
            <DialogDescription>
              Document a new allergen management training session
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleAddRecord}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="title">Training Title</Label>
                <Input
                  id="title"
                  name="title"
                  value={formValues.title || ''}
                  onChange={handleInputChange}
                  placeholder="e.g., Allergen Awareness Training"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="type">Training Type</Label>
                <Select
                  value={formValues.type}
                  onValueChange={(value) => handleSelectChange('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select training type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="allergen_awareness">Allergen Awareness</SelectItem>
                    <SelectItem value="cross_contact_prevention">Cross-Contact Prevention</SelectItem>
                    <SelectItem value="cleaning_procedures">Cleaning Procedures</SelectItem>
                    <SelectItem value="label_verification">Label Verification</SelectItem>
                    <SelectItem value="emergency_response">Emergency Response</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formValues.description || ''}
                  onChange={handleInputChange}
                  placeholder="Brief description of the training content"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Training Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formValues.date ? format(formValues.date, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formValues.date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="duration">Duration (minutes)</Label>
                <Input
                  id="duration"
                  name="duration"
                  type="number"
                  min="1"
                  value={formValues.duration || ''}
                  onChange={handleDurationChange}
                  placeholder="e.g., 60"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="trainer">Trainer</Label>
                <Input
                  id="trainer"
                  name="trainer"
                  value={formValues.trainer || ''}
                  onChange={handleInputChange}
                  placeholder="Name of the trainer"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="attendees">Attendees (comma separated)</Label>
                <Textarea
                  id="attendees"
                  name="attendees"
                  value={formValues.attendees?.join(', ') || ''}
                  onChange={handleAttendeesChange}
                  placeholder="e.g., Production Team, QA Team, New Employees"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="materials">Training Materials</Label>
                <Textarea
                  id="materials"
                  name="materials"
                  value={formValues.materials || ''}
                  onChange={handleInputChange}
                  placeholder="Description of training materials used"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="assessmentResults">Assessment Results (Optional)</Label>
                <Textarea
                  id="assessmentResults"
                  name="assessmentResults"
                  value={formValues.assessmentResults || ''}
                  onChange={handleInputChange}
                  placeholder="e.g., 95% pass rate, 2 employees requiring retraining"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="nextTrainingDate">Next Training Date (Optional)</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formValues.nextTrainingDate ? format(formValues.nextTrainingDate, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formValues.nextTrainingDate}
                      onSelect={handleNextTrainingDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Plus className="h-4 w-4 mr-2" />
                Add Record
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Training Record Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Training Record</DialogTitle>
            <DialogDescription>
              Update training record details
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleEditRecord}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-title">Training Title</Label>
                <Input
                  id="edit-title"
                  name="title"
                  value={formValues.title || ''}
                  onChange={handleInputChange}
                  placeholder="e.g., Allergen Awareness Training"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-type">Training Type</Label>
                <Select
                  value={formValues.type}
                  onValueChange={(value) => handleSelectChange('type', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select training type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="allergen_awareness">Allergen Awareness</SelectItem>
                    <SelectItem value="cross_contact_prevention">Cross-Contact Prevention</SelectItem>
                    <SelectItem value="cleaning_procedures">Cleaning Procedures</SelectItem>
                    <SelectItem value="label_verification">Label Verification</SelectItem>
                    <SelectItem value="emergency_response">Emergency Response</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formValues.description || ''}
                  onChange={handleInputChange}
                  placeholder="Brief description of the training content"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-date">Training Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formValues.date ? format(formValues.date, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formValues.date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-duration">Duration (minutes)</Label>
                <Input
                  id="edit-duration"
                  name="duration"
                  type="number"
                  min="1"
                  value={formValues.duration || ''}
                  onChange={handleDurationChange}
                  placeholder="e.g., 60"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-trainer">Trainer</Label>
                <Input
                  id="edit-trainer"
                  name="trainer"
                  value={formValues.trainer || ''}
                  onChange={handleInputChange}
                  placeholder="Name of the trainer"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-attendees">Attendees (comma separated)</Label>
                <Textarea
                  id="edit-attendees"
                  name="attendees"
                  value={formValues.attendees?.join(', ') || ''}
                  onChange={handleAttendeesChange}
                  placeholder="e.g., Production Team, QA Team, New Employees"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-materials">Training Materials</Label>
                <Textarea
                  id="edit-materials"
                  name="materials"
                  value={formValues.materials || ''}
                  onChange={handleInputChange}
                  placeholder="Description of training materials used"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-assessmentResults">Assessment Results (Optional)</Label>
                <Textarea
                  id="edit-assessmentResults"
                  name="assessmentResults"
                  value={formValues.assessmentResults || ''}
                  onChange={handleInputChange}
                  placeholder="e.g., 95% pass rate, 2 employees requiring retraining"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-nextTrainingDate">Next Training Date (Optional)</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formValues.nextTrainingDate ? format(formValues.nextTrainingDate, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formValues.nextTrainingDate}
                      onSelect={handleNextTrainingDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <FileText className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
