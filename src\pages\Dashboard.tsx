import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/contexts/AuthContext';
import {
  <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer,
  PieChart, Pie, Cell, LineChart, Line, Legend
} from 'recharts';
import {
  AlertCircle, CheckCircle, Clock, FileText, AlertTriangle, ClipboardCheck,
  LayoutGrid, FileBarChart, FilePlus, ShieldAlert, Users, BookOpen,
  FileWarning, Wrench, Activity, ThumbsUp, BellRing, Calendar
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';

// Mock data for dashboard
const mockData = {
  // HACCP Data
  products: 5,
  hazards: 24,
  ccps: 7,
  plansInProgress: 3,
  plansApproved: 2,

  // CAPA Data
  capas: {
    total: 12,
    open: 5,
    closed: 7,
    byPriority: [
      { name: 'Critical', value: 2, color: '#ef4444' },
      { name: 'High', value: 3, color: '#f97316' },
      { name: 'Medium', value: 5, color: '#eab308' },
      { name: 'Low', value: 2, color: '#22c55e' }
    ],
    byCategory: [
      { name: 'Food Safety', value: 4 },
      { name: 'Product Quality', value: 3 },
      { name: 'Foreign Material', value: 2 },
      { name: 'Allergen Management', value: 2 },
      { name: 'Documentation', value: 1 }
    ],
    byStatus: [
      { name: 'Open', value: 2 },
      { name: 'Investigation', value: 1 },
      { name: 'Action Planned', value: 1 },
      { name: 'Implementing', value: 1 },
      { name: 'Verification', value: 0 },
      { name: 'Closed', value: 7 }
    ]
  },

  // Claims Data
  claims: {
    total: 8,
    open: 3,
    resolved: 5
  },

  // Actions Data
  actions: {
    corrective: 15,
    preventive: 10,
    completed: 18,
    pending: 7
  },

  // Food Safety Culture Data
  foodSafetyCulture: {
    implemented: 5,
    inProgress: 2,
    planned: 1,
    byCategory: [
      { name: 'Communication', status: 'implemented', percentage: 100 },
      { name: 'Training', status: 'implemented', percentage: 90 },
      { name: 'Feedback', status: 'in_progress', percentage: 70 },
      { name: 'Performance', status: 'implemented', percentage: 85 },
      { name: 'Leadership', status: 'implemented', percentage: 95 },
      { name: 'Resources', status: 'implemented', percentage: 80 },
      { name: 'Awareness', status: 'in_progress', percentage: 60 }
    ]
  },

  // Allergen Management Data
  allergens: {
    total: 14,
    highRisk: 5,
    present: 8,
    mayContain: 3,
    controlMeasures: 12,
    verificationActivities: 8,
    compliancePercentage: 92
  },

  // Activity Data
  recentActivity: [
    { id: 1, action: 'Updated Hazard Analysis', user: 'Admin User', time: '2 hours ago', type: 'haccp' },
    { id: 2, action: 'Created CCP for Cooking Step', user: 'QA Manager', time: '1 day ago', type: 'haccp' },
    { id: 3, action: 'Approved HACCP Plan v1.2', user: 'Food Safety Auditor', time: '2 days ago', type: 'haccp' },
    { id: 4, action: 'Added new Product: Chicken Soup', user: 'Admin User', time: '1 week ago', type: 'haccp' },
    { id: 5, action: 'Created CAPA for Foreign Material', user: 'Quality Manager', time: '3 days ago', type: 'capa' },
    { id: 6, action: 'Updated Allergen Control Measures', user: 'Food Safety Manager', time: '4 days ago', type: 'allergen' },
    { id: 7, action: 'Completed Food Safety Culture Training', user: 'HR Manager', time: '5 days ago', type: 'culture' }
  ],

  // Hazard Data
  hazardsByType: [
    { name: 'Raw Materials', biological: 4, chemical: 2, physical: 1 },
    { name: 'Storage', biological: 3, chemical: 1, physical: 0 },
    { name: 'Preparation', biological: 2, chemical: 2, physical: 3 },
    { name: 'Cooking', biological: 1, chemical: 0, physical: 1 },
    { name: 'Packaging', biological: 0, chemical: 2, physical: 2 },
  ],

  // Compliance Data
  compliance: {
    fssc22000: { status: 'Compliant', percentage: 95, color: 'green' },
    iso22000: { status: 'Compliant', percentage: 92, color: 'green' },
    iso9001: { status: 'In Progress', percentage: 78, color: 'yellow' },
    haccp: { status: 'Compliant', percentage: 98, color: 'green' }
  },

  // Upcoming Tasks
  upcomingTasks: [
    { id: 1, title: 'Annual HACCP Review Due', daysRemaining: 7, priority: 'medium', type: 'haccp' },
    { id: 2, title: 'Missing CCP Documentation', daysRemaining: 0, priority: 'high', type: 'haccp' },
    { id: 3, title: 'CAPA Verification Required', daysRemaining: 3, priority: 'high', type: 'capa' },
    { id: 4, title: 'Allergen Training Due', daysRemaining: 14, priority: 'medium', type: 'allergen' },
    { id: 5, title: 'Food Safety Culture Assessment', daysRemaining: 21, priority: 'low', type: 'culture' }
  ],

  // Trend Data
  trends: {
    capas: [
      { month: 'Jan', count: 3 },
      { month: 'Feb', count: 2 },
      { month: 'Mar', count: 4 },
      { month: 'Apr', count: 1 },
      { month: 'May', count: 2 }
    ],
    nonConformities: [
      { month: 'Jan', count: 5 },
      { month: 'Feb', count: 3 },
      { month: 'Mar', count: 6 },
      { month: 'Apr', count: 2 },
      { month: 'May', count: 3 }
    ]
  }
};

export function Dashboard() {
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  const handleNewPlan = () => {
    navigate('/plan-generator');
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">Dashboard</h1>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <FileBarChart className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button
            size="sm"
            className="bg-primary hover:bg-primary/90 text-white font-medium gap-1.5 px-4 py-2 rounded-md shadow-sm hover:shadow transition-all"
            onClick={handleNewPlan}
          >
            <FilePlus className="h-4 w-4" />
            New Plan
          </Button>
        </div>
      </div>

      {/* Key Metrics Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <div className="bg-primary/10 p-2 rounded-full">
              <LayoutGrid className="h-4 w-4 text-primary" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.products}</div>
            <p className="text-xs text-muted-foreground">
              Food products in the system
            </p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Identified Hazards</CardTitle>
            <div className="bg-amber-100 p-2 rounded-full">
              <AlertTriangle className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.hazards}</div>
            <p className="text-xs text-muted-foreground">
              Across all process steps
            </p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Critical Control Points</CardTitle>
            <div className="bg-green-100 p-2 rounded-full">
              <ClipboardCheck className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.ccps}</div>
            <p className="text-xs text-muted-foreground">
              With monitoring procedures
            </p>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">HACCP Plans</CardTitle>
            <div className="bg-blue-100 p-2 rounded-full">
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {mockData.plansApproved} <span className="text-sm font-normal text-muted-foreground">/ {mockData.plansInProgress + mockData.plansApproved}</span>
            </div>
            <p className="text-xs text-muted-foreground">
              Approved plans / Total plans
            </p>
          </CardContent>
        </Card>
      </div>

      {/* CAPA Metrics Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Total CAPAs</CardTitle>
            <div className="bg-orange-100 p-2 rounded-full">
              <FileWarning className="h-4 w-4 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.capas.total}</div>
            <p className="text-xs text-muted-foreground">
              {mockData.capas.open} open, {mockData.capas.closed} closed
            </p>
            <div className="mt-3">
              <Progress value={(mockData.capas.closed / mockData.capas.total) * 100} className="h-2 bg-muted/30" />
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((mockData.capas.closed / mockData.capas.total) * 100)}% completion rate
              </p>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Customer Claims</CardTitle>
            <div className="bg-red-100 p-2 rounded-full">
              <AlertCircle className="h-4 w-4 text-red-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.claims.total}</div>
            <p className="text-xs text-muted-foreground">
              {mockData.claims.open} open claims requiring action
            </p>
            <div className="mt-3">
              <Progress value={(mockData.claims.resolved / mockData.claims.total) * 100} className="h-2 bg-muted/30" />
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((mockData.claims.resolved / mockData.claims.total) * 100)}% resolution rate
              </p>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Actions</CardTitle>
            <div className="bg-blue-100 p-2 rounded-full">
              <Wrench className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.actions.corrective + mockData.actions.preventive}</div>
            <p className="text-xs text-muted-foreground">
              {mockData.actions.completed} completed, {mockData.actions.pending} pending
            </p>
            <div className="mt-3">
              <Progress value={(mockData.actions.completed / (mockData.actions.corrective + mockData.actions.preventive)) * 100} className="h-2 bg-muted/30" />
              <p className="text-xs text-muted-foreground mt-1">
                {Math.round((mockData.actions.completed / (mockData.actions.corrective + mockData.actions.preventive)) * 100)}% completion rate
              </p>
            </div>
          </CardContent>
        </Card>
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Allergen Management</CardTitle>
            <div className="bg-purple-100 p-2 rounded-full">
              <ShieldAlert className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockData.allergens.total}</div>
            <p className="text-xs text-muted-foreground">
              {mockData.allergens.highRisk} high risk allergens
            </p>
            <div className="mt-3">
              <Progress value={mockData.allergens.compliancePercentage} className="h-2 bg-muted/30" />
              <p className="text-xs text-muted-foreground mt-1">
                {mockData.allergens.compliancePercentage}% compliance rate
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1 hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Hazards by Process Step</CardTitle>
                <CardDescription>Distribution of hazard types</CardDescription>
              </div>
              <div className="bg-green-100 p-2 rounded-full">
                <AlertTriangle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="h-80 pt-4">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={mockData.hazardsByType}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                <XAxis dataKey="name" tick={{ fontSize: 12 }} />
                <YAxis tick={{ fontSize: 12 }} />
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    border: '1px solid #e2e8f0'
                  }}
                />
                <Bar dataKey="biological" name="Biological" fill="#4ade80" radius={[4, 4, 0, 0]} />
                <Bar dataKey="chemical" name="Chemical" fill="#a855f7" radius={[4, 4, 0, 0]} />
                <Bar dataKey="physical" name="Physical" fill="#f97316" radius={[4, 4, 0, 0]} />
                <Legend iconSize={10} wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }} />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="col-span-1 hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>CAPA by Priority</CardTitle>
                <CardDescription>Distribution of CAPAs by priority level</CardDescription>
              </div>
              <div className="bg-orange-100 p-2 rounded-full">
                <FileWarning className="h-4 w-4 text-orange-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="h-80 pt-4">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={mockData.capas.byPriority}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={90}
                  innerRadius={30}
                  fill="#8884d8"
                  dataKey="value"
                  paddingAngle={2}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                >
                  {mockData.capas.byPriority.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} stroke="#ffffff" strokeWidth={2} />
                  ))}
                </Pie>
                <Tooltip
                  contentStyle={{
                    backgroundColor: 'white',
                    borderRadius: '8px',
                    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
                    border: '1px solid #e2e8f0'
                  }}
                />
                <Legend iconSize={10} wrapperStyle={{ fontSize: '12px', paddingTop: '10px' }} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Activity and Tasks Section */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card className="col-span-1 hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest actions in the system</CardDescription>
              </div>
              <div className="bg-blue-100 p-2 rounded-full">
                <Clock className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="space-y-4">
              {mockData.recentActivity.slice(0, 5).map(activity => (
                <div key={activity.id} className="flex items-start space-x-4 p-3 rounded-md hover:bg-muted/20 transition-colors">
                  <div className={`p-2 rounded-full ${
                    activity.type === 'haccp' ? 'bg-blue-100' :
                    activity.type === 'capa' ? 'bg-amber-100' :
                    activity.type === 'allergen' ? 'bg-purple-100' :
                    'bg-green-100'
                  }`}>
                    {activity.type === 'haccp' && <ClipboardCheck className="h-4 w-4 text-blue-600" />}
                    {activity.type === 'capa' && <FileWarning className="h-4 w-4 text-amber-600" />}
                    {activity.type === 'allergen' && <ShieldAlert className="h-4 w-4 text-purple-600" />}
                    {activity.type === 'culture' && <Users className="h-4 w-4 text-green-600" />}
                  </div>
                  <div className="space-y-1 flex-1">
                    <p className="text-sm font-medium leading-none">
                      {activity.action}
                    </p>
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-muted-foreground">
                        {activity.user}
                      </p>
                      <span className="text-xs bg-muted/30 px-2 py-1 rounded-full">
                        {activity.time}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-1 hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Upcoming Tasks</CardTitle>
                <CardDescription>Tasks requiring attention</CardDescription>
              </div>
              <div className="bg-amber-100 p-2 rounded-full">
                <AlertCircle className="h-4 w-4 text-amber-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-4">
            <div className="space-y-3">
              {mockData.upcomingTasks.slice(0, 4).map(task => (
                <div key={task.id} className={`p-3 rounded-md ${
                  task.priority === 'high' ? 'bg-red-50 border border-red-100 hover:bg-red-100' :
                  task.priority === 'medium' ? 'bg-yellow-50 border border-yellow-100 hover:bg-yellow-100' :
                  'bg-blue-50 border border-blue-100 hover:bg-blue-100'
                } transition-colors cursor-pointer`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {task.type === 'haccp' && <ClipboardCheck className={`h-4 w-4 ${task.priority === 'high' ? 'text-red-500' : task.priority === 'medium' ? 'text-yellow-500' : 'text-blue-500'}`} />}
                      {task.type === 'capa' && <FileWarning className={`h-4 w-4 ${task.priority === 'high' ? 'text-red-500' : task.priority === 'medium' ? 'text-yellow-500' : 'text-blue-500'}`} />}
                      {task.type === 'allergen' && <ShieldAlert className={`h-4 w-4 ${task.priority === 'high' ? 'text-red-500' : task.priority === 'medium' ? 'text-yellow-500' : 'text-blue-500'}`} />}
                      {task.type === 'culture' && <Users className={`h-4 w-4 ${task.priority === 'high' ? 'text-red-500' : task.priority === 'medium' ? 'text-yellow-500' : 'text-blue-500'}`} />}
                      <span className="text-sm font-medium">{task.title}</span>
                    </div>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      task.priority === 'high' ? 'bg-red-200 text-red-700' :
                      task.priority === 'medium' ? 'bg-yellow-200 text-yellow-700' :
                      'bg-blue-200 text-blue-700'
                    }`}>
                      {task.priority}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-2 flex justify-between items-center">
                    <span>
                      {task.daysRemaining === 0 ? 'Due today' : task.daysRemaining === 1 ? 'Due tomorrow' : `Due in ${task.daysRemaining} days`}
                    </span>
                    <Button variant="ghost" size="sm" className="h-6 px-2 text-xs">View</Button>
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Compliance and Status Section */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle>HACCP Plans Status</CardTitle>
              <div className="bg-blue-100 p-2 rounded-full">
                <FileText className="h-4 w-4 text-blue-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent className="grid gap-3">
            <div className="flex items-center gap-3 p-3 bg-green-50 rounded-md border border-green-100">
              <div className="bg-green-100 p-2 rounded-full">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium">Approved</div>
              </div>
              <div className="text-lg font-bold">{mockData.plansApproved}</div>
            </div>
            <div className="flex items-center gap-3 p-3 bg-yellow-50 rounded-md border border-yellow-100">
              <div className="bg-yellow-100 p-2 rounded-full">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium">In Progress</div>
              </div>
              <div className="text-lg font-bold">{mockData.plansInProgress}</div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle>Compliance Status</CardTitle>
              <div className="bg-green-100 p-2 rounded-full">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(mockData.compliance).map(([standard, data]) => (
                <div key={standard} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{standard.toUpperCase().replace('_', ' ')}</span>
                    <span className={`${
                      data.color === 'green' ? 'bg-green-100 text-green-800' :
                      data.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    } text-xs px-2 py-1 rounded-full`}>
                      {data.status}
                    </span>
                  </div>
                  <Progress value={data.percentage} className={`h-2 ${
                    data.color === 'green' ? 'bg-green-100' :
                    data.color === 'yellow' ? 'bg-yellow-100' :
                    'bg-red-100'
                  }`} />
                  <p className="text-xs text-muted-foreground">{data.percentage}% complete</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="lg:col-span-1 hover:shadow-md transition-all duration-200 hover:border-primary/20">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle>Food Safety Culture</CardTitle>
              <div className="bg-green-100 p-2 rounded-full">
                <Users className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockData.foodSafetyCulture.byCategory.slice(0, 4).map(category => (
                <div key={category.name} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{category.name}</span>
                    <span className={`${
                      category.status === 'implemented' ? 'bg-green-100 text-green-800' :
                      'bg-yellow-100 text-yellow-800'
                    } text-xs px-2 py-1 rounded-full`}>
                      {category.status === 'implemented' ? 'Implemented' : 'In Progress'}
                    </span>
                  </div>
                  <Progress value={category.percentage} className={`h-2 ${
                    category.status === 'implemented' ? 'bg-green-100' : 'bg-yellow-100'
                  }`} />
                  <p className="text-xs text-muted-foreground">{category.percentage}% implemented</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default Dashboard;