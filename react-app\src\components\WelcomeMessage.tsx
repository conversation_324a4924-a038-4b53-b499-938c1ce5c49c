import React from 'react';
import { useDocuments } from '../context/DocumentContext';
import './WelcomeMessage.css';

const WelcomeMessage: React.FC = () => {
  const { hasSeenWelcome, dismissWelcome } = useDocuments();

  if (hasSeenWelcome) {
    return null;
  }

  return (
    <div className="welcome-message">
      <div className="welcome-content">
        <i className="fas fa-info-circle"></i>
        <div>
          <h3>Welcome to the Enhanced Document Management System!</h3>
          <p>Sample data has been loaded with 15 realistic documents to demonstrate all features:</p>
          <ul className="feature-list">
            <li><strong>Search:</strong> Try searching for "Qualité", "Formation", or "PR.QM"</li>
            <li><strong>Filter:</strong> Use the dropdowns to filter by document type or status</li>
            <li><strong>Sort:</strong> Click any column header to sort the table</li>
            <li><strong>Edit:</strong> Click the edit button on any document to test the form</li>
            <li><strong>Visual Elements:</strong> Notice the colored badges, icons, and enhanced styling</li>
          </ul>
          <button 
            className="btn-link" 
            onClick={dismissWelcome}
            type="button"
          >
            Got it, dismiss this message
          </button>
        </div>
      </div>
    </div>
  );
};

export default WelcomeMessage;
