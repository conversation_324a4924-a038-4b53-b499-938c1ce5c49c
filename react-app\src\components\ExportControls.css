.export-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-xl);
  flex-wrap: wrap;
}

.export-group,
.data-management-group {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.data-management-group .btn {
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .export-options {
    flex-direction: column;
    padding: var(--spacing-md);
  }

  .export-group,
  .data-management-group {
    width: 100%;
    justify-content: center;
  }
}
