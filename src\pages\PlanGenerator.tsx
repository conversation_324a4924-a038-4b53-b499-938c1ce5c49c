
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { useLocalStorage } from '@/hooks/use-local-storage';

import { PlanHeader } from '@/components/plan/PlanHeader';
import { GeneratePlanTab } from '@/components/plan/GeneratePlanTab';
import { PreviewPlanTab } from '@/components/plan/PreviewPlanTab';
import { ApprovePlanTab } from '@/components/plan/ApprovePlanTab';
import { HACCPPlan, Product } from '@/models/types';
import { createPlan, updatePlan, getProducts, getCCPsForProduct, getHazardsForProduct } from '@/services/planService';
import { generateHACCPPlanPDF } from '@/services/pdfExportService';
import { initializeMockData } from '@/services/mockDataService';

export function PlanGenerator() {
  const { currentUser, hasPermission } = useAuth();
  const [activeTab, setActiveTab] = useState('generate');
  const [planName, setPlanName] = useState('Chicken Soup HACCP Plan');
  const [planVersion, setPlanVersion] = useState('1.0');
  const [selectedProduct, setSelectedProduct] = useState('product1');
  const [currentPlan, setCurrentPlan] = useState<HACCPPlan | null>(null);
  const [plans, setPlans] = useLocalStorage<HACCPPlan[]>('haccp_plans', []);
  const [selectedProductDetails, setSelectedProductDetails] = useState<Product | null>(null);
  const [ccpData, setCcpData] = useState<any[]>([]);
  const [hazardData, setHazardData] = useState<any[]>([]);

  const canApprove = hasPermission('admin');

  // Initialize mock data on component mount
  useEffect(() => {
    // Initialize mock data
    initializeMockData();
  }, []);

  // Load product details when selected product changes
  useEffect(() => {
    if (selectedProduct) {
      const products = getProducts();
      const product = products.find(p => p.id === selectedProduct);
      setSelectedProductDetails(product || null);

      // Load hazards and CCPs for the selected product
      const hazards = getHazardsForProduct(selectedProduct);
      setHazardData(hazards);

      const ccps = getCCPsForProduct(selectedProduct);
      setCcpData(ccps);
    }
  }, [selectedProduct]);

  const handleGeneratePlan = () => {
    // Create a new plan or update existing one
    const planData: Partial<HACCPPlan> = {
      productId: selectedProduct,
      name: planName,
      version: planVersion,
      status: 'draft',
      createdBy: currentUser?.id || 'user1',
      createdAt: new Date(),
      updatedBy: currentUser?.id || 'user1',
      updatedAt: new Date(),
    };

    const newPlan = createPlan(planData);
    setCurrentPlan(newPlan);

    // Update plans in localStorage
    setPlans([...plans, newPlan]);

    toast({
      title: 'Plan Generated',
      description: 'Your HACCP plan has been generated successfully.',
    });

    setActiveTab('preview');
  };

  const handleSavePlan = () => {
    if (currentPlan) {
      // Update the plan status
      const updatedPlan = updatePlan(currentPlan.id, {
        status: 'under_review',
        updatedBy: currentUser?.id || 'user1',
        updatedAt: new Date(),
      });

      if (updatedPlan) {
        setCurrentPlan(updatedPlan);

        // Update plans in localStorage
        const updatedPlans = plans.map(p =>
          p.id === updatedPlan.id ? updatedPlan : p
        );
        setPlans(updatedPlans);
      }
    }

    toast({
      title: 'Plan Saved',
      description: 'Your HACCP plan has been saved successfully.',
    });
  };

  const handleApprovePlan = () => {
    if (currentPlan) {
      // Update the plan status
      const updatedPlan = updatePlan(currentPlan.id, {
        status: 'approved',
        approvedBy: currentUser?.id || 'admin',
        approvedDate: new Date(),
        updatedBy: currentUser?.id || 'user1',
        updatedAt: new Date(),
      });

      if (updatedPlan) {
        setCurrentPlan(updatedPlan);

        // Update plans in localStorage
        const updatedPlans = plans.map(p =>
          p.id === updatedPlan.id ? updatedPlan : p
        );
        setPlans(updatedPlans);
      }
    }

    toast({
      title: 'Plan Approved',
      description: 'The HACCP plan has been approved and is now in effect.',
    });
  };

  const handleExportPDF = () => {
    toast({
      title: 'PDF Export Started',
      description: 'Your HACCP plan is being exported to PDF.',
    });

    try {
      // Generate the PDF using our service
      const pdf = generateHACCPPlanPDF(
        planName,
        planVersion,
        selectedProduct,
        currentPlan,
        selectedProductDetails,
        ccpData,
        hazardData,
        {
          includeProductDescription: true,
          includeProcessFlow: true,
          includeHazardAnalysis: true,
          includeCCPs: true,
          includeVerification: true,
          includeApprovals: true
        }
      );

      // Save the PDF
      pdf.save(`${planName.replace(/\s+/g, '_')}.pdf`);

      toast({
        title: 'PDF Export Complete',
        description: 'Your HACCP plan has been exported to PDF successfully.',
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: 'PDF Export Failed',
        description: 'There was an error generating the PDF. Please try again.',
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="space-y-6">
      <PlanHeader handleExportPDF={handleExportPDF} />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="generate">1. Generate Plan</TabsTrigger>
          <TabsTrigger value="preview">2. Preview</TabsTrigger>
          <TabsTrigger value="approve">3. Approve &amp; Finalize</TabsTrigger>
        </TabsList>

        <TabsContent value="generate">
          <GeneratePlanTab
            planName={planName}
            setPlanName={setPlanName}
            planVersion={planVersion}
            setPlanVersion={setPlanVersion}
            selectedProduct={selectedProduct}
            setSelectedProduct={setSelectedProduct}
            handleGeneratePlan={handleGeneratePlan}
          />
        </TabsContent>

        <TabsContent value="preview">
          <PreviewPlanTab
            planName={planName}
            planVersion={planVersion}
            selectedProduct={selectedProduct}
            currentPlan={currentPlan}
            ccpData={ccpData}
            hazardData={hazardData}
            setActiveTab={setActiveTab}
            handleSavePlan={handleSavePlan}
          />
        </TabsContent>

        <TabsContent value="approve">
          <ApprovePlanTab
            planName={planName}
            planVersion={planVersion}
            selectedProduct={selectedProduct}
            currentPlan={currentPlan}
            ccpData={ccpData}
            canApprove={canApprove}
            setActiveTab={setActiveTab}
            handleApprovePlan={handleApprovePlan}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default PlanGenerator;
