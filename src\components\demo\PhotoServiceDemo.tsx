import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  Image, 
  Trash2, 
  Settings, 
  BarChart3, 
  Heart,
  AlertCircle,
  CheckCircle,
  Info
} from 'lucide-react';
import { usePhotoService } from '@/hooks/usePhotoService';
import * as photoService from '@/services/photoStorageService';

export function PhotoServiceDemo() {
  const [selectedClaimId, setSelectedClaimId] = useState('demo-claim-1');
  const [serviceHealth, setServiceHealth] = useState(photoService.getServiceHealth());
  const [statistics, setStatistics] = useState(photoService.getPhotoStatistics());

  const {
    photos,
    storedPhotos,
    isUploading,
    uploadProgress,
    addPhotos,
    removePhoto,
    clearAllPhotos,
    uploadAllPhotos,
    validateFile,
    getPhotoCount,
    hasPhotos,
    refreshStoredPhotos
  } = usePhotoService({
    claimId: selectedClaimId,
    maxFiles: 10,
    onUploadComplete: (uploadedPhotos) => {
      console.log('Upload completed:', uploadedPhotos);
      updateStats();
    },
    onUploadError: (error) => {
      console.error('Upload error:', error);
    }
  });

  const updateStats = () => {
    setServiceHealth(photoService.getServiceHealth());
    setStatistics(photoService.getPhotoStatistics());
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      addPhotos(event.target.files);
    }
  };

  const handleConfigUpdate = () => {
    photoService.updateServiceConfig({
      maxFileSize: 10 * 1024 * 1024, // 10MB
      retryAttempts: 5
    });
    updateStats();
  };

  const handleClearAll = () => {
    photoService.clearAllPhotos();
    clearAllPhotos();
    updateStats();
  };

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertCircle className="h-4 w-4" />;
      case 'error': return <AlertCircle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Enhanced Photo Service Demo</h1>
        <Badge variant="outline" className="text-sm">
          v2.0 Enhanced
        </Badge>
      </div>

      <Tabs defaultValue="upload" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="upload">Upload & Manage</TabsTrigger>
          <TabsTrigger value="statistics">Statistics</TabsTrigger>
          <TabsTrigger value="health">Service Health</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Photo Upload & Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-4">
                <label className="text-sm font-medium">Claim ID:</label>
                <select 
                  value={selectedClaimId}
                  onChange={(e) => setSelectedClaimId(e.target.value)}
                  className="px-3 py-1 border rounded"
                >
                  <option value="demo-claim-1">Demo Claim 1</option>
                  <option value="demo-claim-2">Demo Claim 2</option>
                  <option value="demo-claim-3">Demo Claim 3</option>
                </select>
              </div>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <input
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="photo-upload"
                />
                <label htmlFor="photo-upload" className="cursor-pointer">
                  <Image className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium">Click to select photos</p>
                  <p className="text-sm text-gray-500">or drag and drop files here</p>
                </label>
              </div>

              {photos.length > 0 && (
                <div className="space-y-2">
                  <h3 className="font-medium">Pending Photos ({photos.length})</h3>
                  <div className="grid grid-cols-1 gap-2">
                    {photos.map((photo) => (
                      <div key={photo.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="flex items-center gap-2">
                          <img 
                            src={photo.url} 
                            alt={photo.name}
                            className="h-10 w-10 object-cover rounded"
                          />
                          <div>
                            <p className="text-sm font-medium">{photo.name}</p>
                            <p className="text-xs text-gray-500">
                              {photoService.formatFileSize(photo.size)}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {photo.uploading && (
                            <Progress 
                              value={uploadProgress[photo.id] || 0} 
                              className="w-20"
                            />
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => removePhoto(photo.id)}
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {storedPhotos.length > 0 && (
                <div className="space-y-2">
                  <h3 className="font-medium">Stored Photos ({storedPhotos.length})</h3>
                  <div className="grid grid-cols-4 gap-2">
                    {storedPhotos.map((photo) => (
                      <div key={photo.id} className="relative group">
                        <img 
                          src={photo.url} 
                          alt={photo.name}
                          className="w-full h-20 object-cover rounded"
                        />
                        <Button
                          size="sm"
                          variant="destructive"
                          className="absolute top-1 right-1 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => removePhoto(photo.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex gap-2">
                <Button 
                  onClick={uploadAllPhotos} 
                  disabled={isUploading || photos.length === 0}
                  className="flex-1"
                >
                  {isUploading ? 'Uploading...' : `Upload ${photos.length} Photos`}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={clearAllPhotos}
                  disabled={photos.length === 0}
                >
                  Clear All
                </Button>
              </div>

              {hasPhotos() && (
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertDescription>
                    Total photos for this claim: {getPhotoCount()}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="statistics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Photo Statistics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded">
                  <p className="text-2xl font-bold">{statistics.totalPhotos}</p>
                  <p className="text-sm text-gray-500">Total Photos</p>
                </div>
                <div className="text-center p-4 border rounded">
                  <p className="text-2xl font-bold">{statistics.formattedTotalSize}</p>
                  <p className="text-sm text-gray-500">Total Size</p>
                </div>
                <div className="text-center p-4 border rounded">
                  <p className="text-2xl font-bold">{statistics.formattedAverageSize}</p>
                  <p className="text-sm text-gray-500">Average Size</p>
                </div>
                <div className="text-center p-4 border rounded">
                  <p className="text-2xl font-bold">{statistics.claimsWithPhotos}</p>
                  <p className="text-sm text-gray-500">Claims with Photos</p>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">File Type Distribution</h3>
                <div className="space-y-2">
                  {Object.entries(statistics.typeDistribution).map(([type, count]) => (
                    <div key={type} className="flex justify-between items-center">
                      <span className="text-sm">{type}</span>
                      <Badge variant="secondary">{count}</Badge>
                    </div>
                  ))}
                </div>
              </div>

              <Button onClick={updateStats} variant="outline" className="w-full">
                Refresh Statistics
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="health" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Service Health
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className={`flex items-center gap-2 ${getHealthStatusColor(serviceHealth.status)}`}>
                {getHealthIcon(serviceHealth.status)}
                <span className="font-medium capitalize">{serviceHealth.status}</span>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Memory Usage</h3>
                <Progress value={serviceHealth.memoryUsage.percentage} className="w-full" />
                <div className="flex justify-between text-sm text-gray-500">
                  <span>{serviceHealth.memoryUsage.formatted.used}</span>
                  <span>{serviceHealth.memoryUsage.formatted.max}</span>
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-3 border rounded">
                  <p className="text-lg font-bold">{serviceHealth.storage.photos}</p>
                  <p className="text-xs text-gray-500">Photos Stored</p>
                </div>
                <div className="text-center p-3 border rounded">
                  <p className="text-lg font-bold">{serviceHealth.storage.claims}</p>
                  <p className="text-xs text-gray-500">Claims</p>
                </div>
                <div className="text-center p-3 border rounded">
                  <p className="text-lg font-bold">{serviceHealth.storage.urlsTracked}</p>
                  <p className="text-xs text-gray-500">URLs Tracked</p>
                </div>
              </div>

              <Button onClick={updateStats} variant="outline" className="w-full">
                Refresh Health Status
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Service Configuration
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Max File Size</label>
                  <p className="text-sm text-gray-500">
                    {photoService.formatFileSize(serviceHealth.config.maxFileSize)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium">Max Files</label>
                  <p className="text-sm text-gray-500">{serviceHealth.config.maxFiles}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Retry Attempts</label>
                  <p className="text-sm text-gray-500">{serviceHealth.config.retryAttempts}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Compression</label>
                  <p className="text-sm text-gray-500">
                    {serviceHealth.config.enableCompression ? 'Enabled' : 'Disabled'}
                  </p>
                </div>
              </div>

              <div>
                <h3 className="font-medium mb-2">Allowed File Types</h3>
                <div className="flex flex-wrap gap-1">
                  {serviceHealth.config.allowedTypes.map((type) => (
                    <Badge key={type} variant="outline" className="text-xs">
                      {type}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleConfigUpdate} variant="outline">
                  Update Config (Demo)
                </Button>
                <Button onClick={handleClearAll} variant="destructive">
                  Clear All Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
