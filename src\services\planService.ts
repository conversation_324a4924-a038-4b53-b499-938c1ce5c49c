import { HACCPPlan, Hazard, CCP, Product, ProcessStep } from '@/models/types';

/**
 * Service for handling HACCP Plan operations
 */

// Get plans from localStorage
export const getPlans = (): HACCPPlan[] => {
  try {
    const plans = localStorage.getItem('haccp_plans');
    return plans ? JSON.parse(plans) : [];
  } catch (error) {
    console.error('Error getting plans from localStorage:', error);
    return [];
  }
};

// Save plans to localStorage
export const savePlans = (plans: HACCPPlan[]): void => {
  try {
    localStorage.setItem('haccp_plans', JSON.stringify(plans));
  } catch (error) {
    console.error('Error saving plans to localStorage:', error);
  }
};

// Get hazards from localStorage
export const getHazards = (): Hazard[] => {
  try {
    const hazards = localStorage.getItem('haccp_hazards');
    return hazards ? JSON.parse(hazards) : [];
  } catch (error) {
    console.error('Error getting hazards from localStorage:', error);
    return [];
  }
};

// Get CCPs from localStorage
export const getCCPs = (): CCP[] => {
  try {
    const ccps = localStorage.getItem('haccp_ccps');
    return ccps ? JSON.parse(ccps) : [];
  } catch (error) {
    console.error('Error getting CCPs from localStorage:', error);
    return [];
  }
};

// Get products from localStorage
export const getProducts = (): Product[] => {
  try {
    const products = localStorage.getItem('haccp_allergen_products');
    return products ? JSON.parse(products) : [];
  } catch (error) {
    console.error('Error getting products from localStorage:', error);
    return [];
  }
};

// Get process steps for a product
export const getProcessSteps = (): ProcessStep[] => {
  try {
    const steps = localStorage.getItem('haccp_process_steps');
    if (steps) {
      return JSON.parse(steps);
    } else {
      // Return mock process steps if none exist in localStorage
      const mockSteps: ProcessStep[] = [
        { id: 'step1', name: 'Raw Material Reception', description: 'Receiving raw materials from suppliers', order: 1 },
        { id: 'step2', name: 'Storage', description: 'Storing raw materials in appropriate conditions', order: 2 },
        { id: 'step3', name: 'Preparation', description: 'Preparing ingredients for cooking', order: 3 },
        { id: 'step4', name: 'Cooking', description: 'Cooking the product to required temperature', order: 4 },
        { id: 'step5', name: 'Cooling', description: 'Cooling the product to safe temperature', order: 5 },
        { id: 'step6', name: 'Packaging', description: 'Packaging the product for distribution', order: 6 },
        { id: 'step7', name: 'Storage & Distribution', description: 'Storing and distributing the finished product', order: 7 },
      ];
      localStorage.setItem('haccp_process_steps', JSON.stringify(mockSteps));
      return mockSteps;
    }
  } catch (error) {
    console.error('Error getting process steps from localStorage:', error);
    return [];
  }
};

// Save process steps to localStorage
export const saveProcessSteps = (steps: ProcessStep[]): void => {
  try {
    localStorage.setItem('haccp_process_steps', JSON.stringify(steps));
  } catch (error) {
    console.error('Error saving process steps to localStorage:', error);
  }
};

// Update the order of process steps
export const updateProcessStepOrder = (steps: ProcessStep[]): boolean => {
  try {
    // Sort steps by order
    const sortedSteps = [...steps].sort((a, b) => a.order - b.order);

    // Save to localStorage
    saveProcessSteps(sortedSteps);
    return true;
  } catch (error) {
    console.error('Error updating process step order:', error);
    return false;
  }
};

// Get hazards for a specific product
export const getHazardsForProduct = (productId: string): Hazard[] => {
  const hazards = getHazards();
  const processSteps = getProcessSteps();

  // In a real app, we would filter hazards by product
  // For now, we'll return all hazards as a mock implementation
  return hazards;
};

// Get CCPs for a specific product
export const getCCPsForProduct = (productId: string): { ccp: CCP, hazard: Hazard }[] => {
  const hazards = getHazardsForProduct(productId);
  const ccps = getCCPs();

  const ccpWithHazards = hazards
    .filter(hazard => hazard.isCCP)
    .map(hazard => {
      const ccp = ccps.find(c => c.hazardId === hazard.id) || {
        id: '',
        hazardId: hazard.id,
        criticalLimit: '',
        monitoringProcedure: '',
        monitoringFrequency: '',
        correctiveAction: '',
        verificationProcedure: '',
        recordkeepingProcedure: '',
      };

      return { ccp, hazard };
    });

  return ccpWithHazards;
};

// Create a new HACCP plan
export const createPlan = (plan: Partial<HACCPPlan>): HACCPPlan => {
  const plans = getPlans();

  const newPlan: HACCPPlan = {
    id: `plan${plans.length + 1}`,
    productId: plan.productId || '',
    name: plan.name || '',
    version: plan.version || '1.0',
    status: 'draft',
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  };

  plans.push(newPlan);
  savePlans(plans);

  return newPlan;
};

// Update an existing HACCP plan
export const updatePlan = (planId: string, updates: Partial<HACCPPlan>): HACCPPlan | null => {
  const plans = getPlans();
  const planIndex = plans.findIndex(p => p.id === planId);

  if (planIndex === -1) {
    return null;
  }

  const updatedPlan = {
    ...plans[planIndex],
    ...updates,
    updatedBy: 'user1',
    updatedAt: new Date(),
  };

  plans[planIndex] = updatedPlan;
  savePlans(plans);

  return updatedPlan;
};

// Validate a HACCP plan
export const validatePlan = (plan: Partial<HACCPPlan>): { isValid: boolean, errors: string[] } => {
  const errors: string[] = [];

  if (!plan.name || plan.name.trim() === '') {
    errors.push('Plan name is required');
  }

  if (!plan.version || plan.version.trim() === '') {
    errors.push('Plan version is required');
  }

  if (!plan.productId || plan.productId.trim() === '') {
    errors.push('Product selection is required');
  }

  // In a real app, we would do more validation
  // For example, check if all CCPs have critical limits, monitoring procedures, etc.

  return {
    isValid: errors.length === 0,
    errors,
  };
};

