import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Camera, Upload, X, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from '@/hooks/use-toast';

export interface PhotoFile {
  id: string;
  file: File;
  url: string;
  name: string;
  size: number;
  uploading?: boolean;
  uploadProgress?: number;
  error?: string;
}

interface PhotoUploadProps {
  photos: PhotoFile[];
  onPhotosChange: (photos: PhotoFile[]) => void;
  maxFiles?: number;
  maxFileSize?: number; // in MB
  acceptedFormats?: string[];
  disabled?: boolean;
  className?: string;
}

export function PhotoUpload({
  photos,
  onPhotosChange,
  maxFiles = 10,
  maxFileSize = 5,
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  disabled = false,
  className
}: PhotoUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false);

  const validateFile = (file: File): string | null => {
    if (!acceptedFormats.includes(file.type)) {
      return `File type ${file.type} is not supported. Please use JPG, PNG, or WebP.`;
    }

    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB.`;
    }

    return null;
  };

  const generatePhotoId = () => `photo_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

  const processFiles = useCallback(async (files: FileList) => {
    if (disabled) return;

    const fileArray = Array.from(files);

    if (photos.length + fileArray.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `You can only upload up to ${maxFiles} photos.`,
        variant: "destructive"
      });
      return;
    }

    const newPhotos: PhotoFile[] = [];

    for (const file of fileArray) {
      const error = validateFile(file);

      if (error) {
        toast({
          title: "Invalid file",
          description: error,
          variant: "destructive"
        });
        continue;
      }

      const photoId = generatePhotoId();
      const url = URL.createObjectURL(file);

      const photoFile: PhotoFile = {
        id: photoId,
        file,
        url,
        name: file.name,
        size: file.size,
        uploading: true,
        uploadProgress: 0
      };

      newPhotos.push(photoFile);
    }

    if (newPhotos.length > 0) {
      const updatedPhotos = [...photos, ...newPhotos];
      onPhotosChange(updatedPhotos);

      // Simulate upload process
      for (const photo of newPhotos) {
        await simulateUpload(photo);
      }
    }
  }, [photos, onPhotosChange, maxFiles, maxFileSize, disabled]);

  const simulateUpload = async (photo: PhotoFile) => {
    // Simulate upload progress
    for (let progress = 0; progress <= 100; progress += 10) {
      await new Promise(resolve => setTimeout(resolve, 100));

      onPhotosChange(prevPhotos =>
        prevPhotos.map(p =>
          p.id === photo.id
            ? { ...p, uploadProgress: progress }
            : p
        )
      );
    }

    // Mark as completed
    onPhotosChange(prevPhotos =>
      prevPhotos.map(p =>
        p.id === photo.id
          ? { ...p, uploading: false, uploadProgress: 100 }
          : p
      )
    );

    toast({
      title: "Photo uploaded",
      description: `${photo.name} has been uploaded successfully.`,
    });
  };

  const removePhoto = (photoId: string) => {
    const photoToRemove = photos.find(p => p.id === photoId);
    if (photoToRemove) {
      URL.revokeObjectURL(photoToRemove.url);
    }
    onPhotosChange(photos.filter(p => p.id !== photoId));
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (!disabled && e.dataTransfer.files) {
      processFiles(e.dataTransfer.files);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      processFiles(e.target.files);
      e.target.value = ''; // Reset input
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn("space-y-4", className)}>
      <div className="space-y-2">
        <Label>Photos</Label>
        <p className="text-sm text-muted-foreground">
          Upload photos related to this claim. Supported formats: JPG, PNG, WebP. Max size: {maxFileSize}MB each.
        </p>
      </div>

      {/* Upload Area */}
      <Card
        className={cn(
          "border-2 border-dashed transition-colors cursor-pointer",
          isDragOver && !disabled && "border-primary bg-primary/5",
          disabled && "opacity-50 cursor-not-allowed"
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="p-4 rounded-full bg-muted">
              <Camera className="h-8 w-8 text-muted-foreground" />
            </div>

            <div className="text-center space-y-2">
              <p className="text-sm font-medium">
                Drag and drop photos here, or click to select
              </p>
              <p className="text-xs text-muted-foreground">
                Up to {maxFiles} photos, {maxFileSize}MB each
              </p>
            </div>

            <input
              type="file"
              multiple
              accept={acceptedFormats.join(',')}
              onChange={handleFileSelect}
              disabled={disabled}
              className="hidden"
              id="photo-upload"
            />

            <Button
              variant="outline"
              size="sm"
              disabled={disabled || photos.length >= maxFiles}
              onClick={() => document.getElementById('photo-upload')?.click()}
            >
              <Upload className="h-4 w-4 mr-2" />
              Select Photos
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Photo Previews */}
      {photos.length > 0 && (
        <div className="space-y-3">
          <Label>Uploaded Photos ({photos.length}/{maxFiles})</Label>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {photos.map((photo) => (
              <div key={photo.id} className="relative group">
                <Card className="overflow-hidden">
                  <div className="aspect-square relative">
                    <img
                      src={photo.url}
                      alt={photo.name}
                      className="w-full h-full object-cover"
                    />

                    {/* Upload Progress Overlay */}
                    {photo.uploading && (
                      <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                        <div className="text-center space-y-2">
                          <Loader2 className="h-6 w-6 text-white animate-spin mx-auto" />
                          <Progress
                            value={photo.uploadProgress || 0}
                            className="w-16 h-2"
                          />
                        </div>
                      </div>
                    )}

                    {/* Remove Button */}
                    {!photo.uploading && (
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => removePhoto(photo.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>

                  <CardContent className="p-2">
                    <p className="text-xs font-medium truncate" title={photo.name}>
                      {photo.name}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {formatFileSize(photo.size)}
                    </p>

                    {photo.error && (
                      <div className="flex items-center space-x-1 mt-1">
                        <AlertCircle className="h-3 w-3 text-destructive" />
                        <p className="text-xs text-destructive">{photo.error}</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
