import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { Allergen } from '@/models/allergen';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, PenLine, Trash2, Search, AlertTriangle, CheckCircle, Info } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface AllergenInventoryProps {
  allergens: Allergen[];
  setAllergens: React.Dispatch<React.SetStateAction<Allergen[]>>;
}

export function AllergenInventory({ allergens, setAllergens }: AllergenInventoryProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedAllergen, setSelectedAllergen] = useState<Allergen | null>(null);

  // Form state
  const [formValues, setFormValues] = useState<Partial<Allergen>>({
    name: '',
    description: '',
    commonNames: [],
    riskLevel: 'high'
  });

  // Filter allergens based on search query
  const filteredAllergens = allergens.filter(allergen =>
    allergen.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    allergen.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    allergen.commonNames.some(name => name.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleCommonNamesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const commonNamesArray = e.target.value.split(',').map(name => name.trim()).filter(name => name !== '');
    setFormValues(prev => ({ ...prev, commonNames: commonNamesArray }));
  };

  const openAddDialog = () => {
    setFormValues({
      name: '',
      description: '',
      commonNames: [],
      riskLevel: 'high'
    });
    setIsAddDialogOpen(true);
  };

  const openEditDialog = (allergen: Allergen) => {
    setSelectedAllergen(allergen);
    setFormValues({
      name: allergen.name,
      description: allergen.description,
      commonNames: allergen.commonNames,
      riskLevel: allergen.riskLevel
    });
    setIsEditDialogOpen(true);
  };

  const handleAddAllergen = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.name?.trim()) {
      toast({
        title: "Error",
        description: "Allergen name is required",
        variant: "destructive"
      });
      return;
    }

    const exists = allergens.some(a => a.name.toLowerCase() === formValues.name?.toLowerCase());
    if (exists) {
      toast({
        title: "Error",
        description: "An allergen with this name already exists",
        variant: "destructive"
      });
      return;
    }

    const newAllergen: Allergen = {
      id: `a${allergens.length + 1}`,
      name: formValues.name,
      description: formValues.description || '',
      commonNames: formValues.commonNames || [],
      riskLevel: formValues.riskLevel as 'high' | 'medium' | 'low'
    };

    setAllergens([...allergens, newAllergen]);
    setIsAddDialogOpen(false);

    toast({
      title: "Success",
      description: "Allergen added successfully"
    });
  };

  const handleEditAllergen = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.name?.trim()) {
      toast({
        title: "Error",
        description: "Allergen name is required",
        variant: "destructive"
      });
      return;
    }

    if (!selectedAllergen) return;

    const nameExists = allergens.some(a =>
      a.id !== selectedAllergen.id &&
      a.name.toLowerCase() === formValues.name?.toLowerCase()
    );

    if (nameExists) {
      toast({
        title: "Error",
        description: "An allergen with this name already exists",
        variant: "destructive"
      });
      return;
    }

    const updatedAllergens = allergens.map(allergen =>
      allergen.id === selectedAllergen.id
        ? {
            ...allergen,
            name: formValues.name!,
            description: formValues.description || '',
            commonNames: formValues.commonNames || [],
            riskLevel: formValues.riskLevel as 'high' | 'medium' | 'low'
          }
        : allergen
    );

    setAllergens(updatedAllergens);
    setIsEditDialogOpen(false);

    toast({
      title: "Success",
      description: "Allergen updated successfully"
    });
  };

  const handleDeleteAllergen = (id: string) => {
    setAllergens(allergens.filter(allergen => allergen.id !== id));

    toast({
      title: "Success",
      description: "Allergen deleted successfully"
    });
  };

  const getRiskLevelBadge = (riskLevel: 'high' | 'medium' | 'low') => {
    switch (riskLevel) {
      case 'high':
        return <Badge className="bg-red-500"><AlertTriangle className="h-3 w-3 mr-1" /> High</Badge>;
      case 'medium':
        return <Badge className="bg-yellow-500"><Info className="h-3 w-3 mr-1" /> Medium</Badge>;
      case 'low':
        return <Badge className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" /> Low</Badge>;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Allergen Inventory</CardTitle>
              <CardDescription>
                Manage allergens present in your facility
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Allergen
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search allergens..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Common Names</TableHead>
                  <TableHead>Risk Level</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAllergens.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="h-24 text-center">
                      No allergens found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAllergens.map((allergen) => (
                    <TableRow key={allergen.id}>
                      <TableCell className="font-medium">{allergen.name}</TableCell>
                      <TableCell>{allergen.commonNames.join(', ')}</TableCell>
                      <TableCell>{getRiskLevelBadge(allergen.riskLevel)}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" size="sm" onClick={() => openEditDialog(allergen)}>
                            <PenLine className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteAllergen(allergen.id)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Allergen Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add Allergen</DialogTitle>
            <DialogDescription>
              Add a new allergen to your inventory
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleAddAllergen}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={formValues.name || ''}
                  onChange={handleInputChange}
                  placeholder="e.g., Peanuts"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formValues.description || ''}
                  onChange={handleInputChange}
                  placeholder="Brief description of the allergen"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="commonNames">Common Names (comma separated)</Label>
                <Textarea
                  id="commonNames"
                  name="commonNames"
                  value={formValues.commonNames?.join(', ') || ''}
                  onChange={handleCommonNamesChange}
                  placeholder="e.g., Groundnuts, Arachis, Monkey nuts"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="riskLevel">Risk Level</Label>
                <Select
                  value={formValues.riskLevel}
                  onValueChange={(value) => handleSelectChange('riskLevel', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select risk level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Plus className="h-4 w-4 mr-2" />
                Add Allergen
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Allergen Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Allergen</DialogTitle>
            <DialogDescription>
              Update allergen information
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleEditAllergen}>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  value={formValues.name || ''}
                  onChange={handleInputChange}
                  placeholder="e.g., Peanuts"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formValues.description || ''}
                  onChange={handleInputChange}
                  placeholder="Brief description of the allergen"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-commonNames">Common Names (comma separated)</Label>
                <Textarea
                  id="edit-commonNames"
                  name="commonNames"
                  value={formValues.commonNames?.join(', ') || ''}
                  onChange={handleCommonNamesChange}
                  placeholder="e.g., Groundnuts, Arachis, Monkey nuts"
                  rows={2}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-riskLevel">Risk Level</Label>
                <Select
                  value={formValues.riskLevel}
                  onValueChange={(value) => handleSelectChange('riskLevel', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select risk level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
