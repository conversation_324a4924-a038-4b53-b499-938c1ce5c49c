import React, { useState } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Carousel, 
  CarouselContent, 
  CarouselItem, 
  CarouselNext, 
  CarouselPrevious 
} from '@/components/ui/carousel';
import { Image, X, Download, ZoomIn, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Photo {
  id: string;
  url: string;
  name: string;
  size?: number;
  uploadDate?: Date;
}

interface PhotoGalleryProps {
  photos: Photo[];
  onRemovePhoto?: (photoId: string) => void;
  showRemoveButton?: boolean;
  maxThumbnails?: number;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function PhotoGallery({
  photos,
  onRemovePhoto,
  showRemoveButton = false,
  maxThumbnails = 4,
  className,
  size = 'md'
}: PhotoGalleryProps) {
  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number | null>(null);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const sizeClasses = {
    sm: 'h-12 w-12',
    md: 'h-16 w-16',
    lg: 'h-24 w-24'
  };

  const openViewer = (index: number) => {
    setSelectedPhotoIndex(index);
    setIsViewerOpen(true);
  };

  const closeViewer = () => {
    setIsViewerOpen(false);
    setSelectedPhotoIndex(null);
  };

  const navigatePhoto = (direction: 'prev' | 'next') => {
    if (selectedPhotoIndex === null) return;
    
    if (direction === 'prev') {
      setSelectedPhotoIndex(selectedPhotoIndex > 0 ? selectedPhotoIndex - 1 : photos.length - 1);
    } else {
      setSelectedPhotoIndex(selectedPhotoIndex < photos.length - 1 ? selectedPhotoIndex + 1 : 0);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      navigatePhoto('prev');
    } else if (e.key === 'ArrowRight') {
      navigatePhoto('next');
    } else if (e.key === 'Escape') {
      closeViewer();
    }
  };

  const downloadPhoto = (photo: Photo) => {
    const link = document.createElement('a');
    link.href = photo.url;
    link.download = photo.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '';
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (photos.length === 0) {
    return (
      <div className={cn("flex items-center justify-center p-4 border-2 border-dashed rounded-lg", className)}>
        <div className="text-center space-y-2">
          <Image className="h-8 w-8 text-muted-foreground mx-auto" />
          <p className="text-sm text-muted-foreground">No photos uploaded</p>
        </div>
      </div>
    );
  }

  const visiblePhotos = photos.slice(0, maxThumbnails);
  const remainingCount = photos.length - maxThumbnails;

  return (
    <>
      <div className={cn("flex items-center space-x-2", className)}>
        {visiblePhotos.map((photo, index) => (
          <div key={photo.id} className="relative group">
            <Card className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow">
              <div 
                className={cn("relative", sizeClasses[size])}
                onClick={() => openViewer(index)}
              >
                <img
                  src={photo.url}
                  alt={photo.name}
                  className="w-full h-full object-cover"
                />
                
                {/* Hover overlay */}
                <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                  <ZoomIn className="h-4 w-4 text-white" />
                </div>

                {/* Remove button */}
                {showRemoveButton && onRemovePhoto && (
                  <Button
                    variant="destructive"
                    size="icon"
                    className="absolute -top-2 -right-2 h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      onRemovePhoto(photo.id);
                    }}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </Card>
          </div>
        ))}

        {/* Show remaining count */}
        {remainingCount > 0 && (
          <Card 
            className={cn("overflow-hidden cursor-pointer hover:shadow-md transition-shadow", sizeClasses[size])}
            onClick={() => openViewer(maxThumbnails)}
          >
            <CardContent className="p-0 h-full flex items-center justify-center bg-muted">
              <div className="text-center">
                <p className="text-xs font-medium">+{remainingCount}</p>
                <p className="text-xs text-muted-foreground">more</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Photo count badge */}
        <Badge variant="secondary" className="ml-2">
          {photos.length} photo{photos.length !== 1 ? 's' : ''}
        </Badge>
      </div>

      {/* Photo Viewer Modal */}
      <Dialog open={isViewerOpen} onOpenChange={setIsViewerOpen}>
        <DialogContent 
          className="max-w-4xl w-full h-[80vh] p-0"
          onKeyDown={handleKeyDown}
        >
          <DialogHeader className="p-4 pb-0">
            <div className="flex items-center justify-between">
              <DialogTitle>
                {selectedPhotoIndex !== null && photos[selectedPhotoIndex]?.name}
              </DialogTitle>
              <div className="flex items-center space-x-2">
                {selectedPhotoIndex !== null && (
                  <>
                    <Badge variant="outline">
                      {selectedPhotoIndex + 1} of {photos.length}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadPhoto(photos[selectedPhotoIndex])}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  </>
                )}
              </div>
            </div>
          </DialogHeader>

          <div className="flex-1 relative p-4">
            {selectedPhotoIndex !== null && (
              <Carousel className="w-full h-full">
                <CarouselContent>
                  {photos.map((photo, index) => (
                    <CarouselItem key={photo.id}>
                      <div className="flex items-center justify-center h-full">
                        <img
                          src={photo.url}
                          alt={photo.name}
                          className="max-w-full max-h-full object-contain"
                        />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                
                {photos.length > 1 && (
                  <>
                    <CarouselPrevious 
                      className="left-4"
                      onClick={() => navigatePhoto('prev')}
                    />
                    <CarouselNext 
                      className="right-4"
                      onClick={() => navigatePhoto('next')}
                    />
                  </>
                )}
              </Carousel>
            )}

            {/* Navigation buttons for keyboard users */}
            {photos.length > 1 && selectedPhotoIndex !== null && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
                <div className="flex items-center space-x-2 bg-black/50 rounded-lg p-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigatePhoto('prev')}
                    className="text-white hover:bg-white/20"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-white text-sm px-2">
                    {selectedPhotoIndex + 1} / {photos.length}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigatePhoto('next')}
                    className="text-white hover:bg-white/20"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Photo info */}
          {selectedPhotoIndex !== null && (
            <div className="p-4 pt-0 border-t">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>{photos[selectedPhotoIndex].name}</span>
                <div className="flex items-center space-x-4">
                  {photos[selectedPhotoIndex].size && (
                    <span>{formatFileSize(photos[selectedPhotoIndex].size)}</span>
                  )}
                  {photos[selectedPhotoIndex].uploadDate && (
                    <span>
                      Uploaded {photos[selectedPhotoIndex].uploadDate?.toLocaleDateString()}
                    </span>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}
