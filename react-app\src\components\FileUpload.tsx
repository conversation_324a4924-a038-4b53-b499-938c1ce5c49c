import React, { useRef, useState } from 'react';
import toast from 'react-hot-toast';
import './FileUpload.css';

interface FileUploadProps {
  currentFile: File | null;
  onFileChange: (file: File | null) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({ currentFile, onFileChange }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const processFile = (file: File) => {
    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('File size must be less than 10MB');
      return;
    }

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'image/jpeg',
      'image/png',
      'text/plain',
      'text/csv'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast.error('File type not supported. Please select a valid document file.');
      return;
    }

    onFileChange(file);
    toast.success(`File "${file.name}" selected successfully`);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragOver(false);

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      processFile(files[0]);
    }
  };

  const handleRemoveFile = () => {
    onFileChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="file-upload-container">
      <label htmlFor="fileInput">
        <i className="fas fa-paperclip"></i> Attachment
      </label>
      
      <div className="file-upload-controls">
        <button 
          type="button" 
          onClick={handleFileSelect}
          className="file-btn"
        >
          <i className="fas fa-upload"></i> Choose File
        </button>
        
        <span className={`file-name ${!currentFile ? 'text-muted' : ''}`}>
          {currentFile ? currentFile.name : 'No file chosen'}
        </span>
        
        {currentFile && (
          <button 
            type="button" 
            onClick={handleRemoveFile}
            className="remove-file-btn"
            title="Remove file"
          >
            <i className="fas fa-times"></i>
          </button>
        )}
      </div>

      {!currentFile && (
        <div 
          className={`file-drop-zone ${isDragOver ? 'dragover' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleFileSelect}
        >
          <i className="fas fa-cloud-upload-alt"></i>
          <p>Drag & drop files here or click to browse</p>
          <small>Supported: PDF, Word, Excel, PowerPoint, Images, Text files (Max 10MB)</small>
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        id="fileInput"
        onChange={handleFileChange}
        accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.jpg,.jpeg,.png,.txt,.csv"
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default FileUpload;
