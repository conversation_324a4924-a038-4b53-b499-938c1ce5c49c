import { Document } from '../types';

export const generateSampleData = (): Document[] => {
  return [
    {
      id: 'doc_001',
      type: 'Processus',
      designation: 'Processus de Gestion de la Qualité',
      referenceId: 'PR.QM.01',
      format: 'PDF',
      effectiveDate: '2024-01-15',
      version: '02',
      status: 'RAS',
      fileName: 'Processus_Gestion_Qualite_v02.pdf',
      fileData: 'data:application/pdf;base64,sample',
      createdAt: '2024-01-10T09:00:00.000Z',
      updatedAt: '2024-01-15T14:30:00.000Z'
    },
    {
      id: 'doc_002',
      type: 'Procédure',
      designation: 'Procédure de Contrôle des Documents',
      referenceId: 'PR.DC.01',
      format: 'Word',
      effectiveDate: '2024-02-01',
      version: '01',
      status: 'Modifier',
      fileName: 'Procedure_Controle_Documents_v01.docx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,sample',
      createdAt: '2024-01-25T10:15:00.000Z',
      updatedAt: '2024-02-01T16:45:00.000Z'
    },
    {
      id: 'doc_003',
      type: 'Enregistrement',
      designation: 'Registre des Audits Internes',
      referenceId: 'EN.AI.01',
      format: 'Excel',
      effectiveDate: '2024-01-01',
      version: '03',
      status: 'RAS',
      fileName: 'Registre_Audits_Internes_2024.xlsx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,sample',
      createdAt: '2023-12-15T08:30:00.000Z',
      updatedAt: '2024-01-01T12:00:00.000Z'
    },
    {
      id: 'doc_004',
      type: 'Manuel',
      designation: 'Manuel Qualité ISO 9001:2015',
      referenceId: 'MQ.IS.01',
      format: 'PDF',
      effectiveDate: '2024-03-01',
      version: '05',
      status: 'Créer',
      fileName: 'Manuel_Qualite_ISO9001_v05.pdf',
      fileData: 'data:application/pdf;base64,sample',
      createdAt: '2024-02-20T11:20:00.000Z',
      updatedAt: '2024-02-28T15:10:00.000Z'
    },
    {
      id: 'doc_005',
      type: 'Logiciel',
      designation: 'Système de Gestion Documentaire',
      referenceId: 'LG.GD.01',
      format: 'Other',
      effectiveDate: '2024-01-20',
      version: '01',
      status: 'RAS',
      fileName: 'SGD_Installation_Guide.pdf',
      fileData: 'data:application/pdf;base64,sample',
      createdAt: '2024-01-18T13:45:00.000Z',
      updatedAt: '2024-01-20T09:30:00.000Z'
    },
    {
      id: 'doc_006',
      type: 'Procédure',
      designation: 'Procédure de Formation du Personnel',
      referenceId: 'PR.FP.02',
      format: 'Word',
      effectiveDate: '2024-02-15',
      version: '02',
      status: 'Modifier',
      fileName: 'Procedure_Formation_Personnel_v02.docx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,sample',
      createdAt: '2024-02-01T14:20:00.000Z',
      updatedAt: '2024-02-15T11:15:00.000Z'
    },
    {
      id: 'doc_007',
      type: 'Enregistrement',
      designation: 'Fiche de Non-Conformité',
      referenceId: 'EN.NC.01',
      format: 'Excel',
      effectiveDate: '2024-01-10',
      version: '01',
      status: 'RAS',
      fileName: 'Fiche_Non_Conformite_Template.xlsx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,sample',
      createdAt: '2024-01-08T16:00:00.000Z',
      updatedAt: '2024-01-10T10:45:00.000Z'
    },
    {
      id: 'doc_008',
      type: 'Processus',
      designation: 'Processus de Gestion des Ressources Humaines',
      referenceId: 'PR.RH.01',
      format: 'PPT',
      effectiveDate: '2024-03-15',
      version: '01',
      status: 'Créer',
      fileName: 'Processus_RH_Presentation.pptx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,sample',
      createdAt: '2024-03-01T09:30:00.000Z',
      updatedAt: '2024-03-10T14:20:00.000Z'
    },
    {
      id: 'doc_009',
      type: 'Manuel',
      designation: 'Manuel d\'Utilisation ERP',
      referenceId: 'MU.ER.01',
      format: 'PDF',
      effectiveDate: '2024-02-20',
      version: '03',
      status: 'RAS',
      fileName: 'Manuel_Utilisation_ERP_v03.pdf',
      fileData: 'data:application/pdf;base64,sample',
      createdAt: '2024-02-10T12:15:00.000Z',
      updatedAt: '2024-02-20T08:45:00.000Z'
    },
    {
      id: 'doc_010',
      type: 'Procédure',
      designation: 'Procédure de Gestion des Achats',
      referenceId: 'PR.AC.01',
      format: 'Word',
      effectiveDate: '2024-01-25',
      version: '04',
      status: 'Annuler',
      fileName: 'Procedure_Gestion_Achats_v04.docx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,sample',
      createdAt: '2024-01-15T15:30:00.000Z',
      updatedAt: '2024-01-25T13:20:00.000Z'
    },
    {
      id: 'doc_011',
      type: 'Enregistrement',
      designation: 'Plan de Formation Annuel',
      referenceId: 'EN.PF.01',
      format: 'Excel',
      effectiveDate: '2024-01-01',
      version: '02',
      status: 'RAS',
      fileName: 'Plan_Formation_2024.xlsx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,sample',
      createdAt: '2023-12-20T10:00:00.000Z',
      updatedAt: '2024-01-01T14:30:00.000Z'
    },
    {
      id: 'doc_012',
      type: 'Logiciel',
      designation: 'Application de Suivi des Indicateurs',
      referenceId: 'LG.SI.01',
      format: 'Other',
      effectiveDate: '2024-02-10',
      version: '02',
      status: 'Modifier',
      fileName: 'App_Suivi_Indicateurs_v02.zip',
      fileData: 'data:application/zip;base64,sample',
      createdAt: '2024-02-01T11:45:00.000Z',
      updatedAt: '2024-02-10T16:20:00.000Z'
    },
    {
      id: 'doc_013',
      type: 'Processus',
      designation: 'Processus de Gestion des Réclamations Clients',
      referenceId: 'PR.RC.01',
      format: 'PDF',
      effectiveDate: '2024-03-01',
      version: '01',
      status: 'Créer',
      fileName: 'Processus_Reclamations_Clients.pdf',
      fileData: 'data:application/pdf;base64,sample',
      createdAt: '2024-02-25T13:10:00.000Z',
      updatedAt: '2024-03-01T09:15:00.000Z'
    },
    {
      id: 'doc_014',
      type: 'Manuel',
      designation: 'Guide de Sécurité Informatique',
      referenceId: 'GU.SI.01',
      format: 'PDF',
      effectiveDate: '2024-01-30',
      version: '02',
      status: 'RAS',
      fileName: 'Guide_Securite_Informatique_v02.pdf',
      fileData: 'data:application/pdf;base64,sample',
      createdAt: '2024-01-20T14:45:00.000Z',
      updatedAt: '2024-01-30T11:30:00.000Z'
    },
    {
      id: 'doc_015',
      type: 'Enregistrement',
      designation: 'Tableau de Bord Qualité Mensuel',
      referenceId: 'EN.TQ.01',
      format: 'Excel',
      effectiveDate: '2024-02-01',
      version: '01',
      status: 'Modifier',
      fileName: 'Tableau_Bord_Qualite_Fevrier2024.xlsx',
      fileData: 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,sample',
      createdAt: '2024-01-28T16:20:00.000Z',
      updatedAt: '2024-02-01T12:10:00.000Z'
    }
  ];
};
