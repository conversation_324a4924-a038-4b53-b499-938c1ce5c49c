import React, { useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useDocuments } from '@/contexts/DocumentContext';
import { Document } from '@/models/document';
import DocumentRow from './DocumentRow';
import DocumentCard from './DocumentCard';
import ColumnVisibilityControl from './ColumnVisibilityControl';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  FileText,
  Inbox,
  Grid3X3,
  List,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Default column configuration
const defaultColumns = [
  { key: 'documentReferenceNumber' as keyof Document, label: 'Reference Number', visible: true, essential: true },
  { key: 'documentTitle' as keyof Document, label: 'Title', visible: true, essential: true },
  { key: 'version' as keyof Document, label: 'Version', visible: true, essential: true },
  { key: 'issueDate' as keyof Document, label: 'Issue Date', visible: true, essential: true },
  { key: 'authorFunction' as keyof Document, label: 'Author', visible: true },
  { key: 'responsibleDepartment' as keyof Document, label: 'Department', visible: true },
  { key: 'status' as keyof Document, label: 'Status', visible: true, essential: true },
  { key: 'storageLocation' as keyof Document, label: 'Storage Location', visible: false },
  { key: 'approvalDate' as keyof Document, label: 'Validation Date', visible: false },
  { key: 'approvedByFunction' as keyof Document, label: 'Validated By', visible: false },
  { key: 'accessLevel' as keyof Document, label: 'Access Level', visible: true },
];

const DocumentTable: React.FC = () => {
  const {
    documents,
    searchFilters,
    sortConfig,
    setSortConfig
  } = useDocuments();

  const [viewMode, setViewMode] = useState<'table' | 'cards'>('table');
  const [visibleColumns, setVisibleColumns] = useState(defaultColumns);

  // Auto-switch to cards on mobile
  React.useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setViewMode('cards');
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Filter and sort documents
  const filteredAndSortedDocuments = useMemo(() => {
    let filtered = documents.filter(doc => {
      const matchesSearch = !searchFilters.searchTerm ||
        doc.documentTitle.toLowerCase().includes(searchFilters.searchTerm.toLowerCase()) ||
        doc.documentReferenceNumber.toLowerCase().includes(searchFilters.searchTerm.toLowerCase());

      const matchesStatus = !searchFilters.statusFilter || doc.status === searchFilters.statusFilter;

      // Show all documents regardless of access level for now
      return matchesSearch && matchesStatus;
    });

    // Apply sorting
    if (sortConfig.column) {
      filtered.sort((a, b) => {
        const aVal = a[sortConfig.column!];
        const bVal = b[sortConfig.column!];

        // Handle date sorting
        if (sortConfig.column === 'issueDate') {
          const aDate = new Date(aVal as string);
          const bDate = new Date(bVal as string);
          return sortConfig.direction === 'asc'
            ? aDate.getTime() - bDate.getTime()
            : bDate.getTime() - aDate.getTime();
        }

        // Handle string sorting
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          const aStr = aVal.toLowerCase();
          const bStr = bVal.toLowerCase();

          if (aStr < bStr) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aStr > bStr) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }

        return 0;
      });
    }

    return filtered;
  }, [documents, searchFilters, sortConfig]);

  const handleSort = (column: keyof Document) => {
    const direction =
      sortConfig.column === column && sortConfig.direction === 'asc'
        ? 'desc'
        : 'asc';

    setSortConfig({ column, direction });
  };

  const getSortIcon = (column: keyof Document) => {
    if (sortConfig.column !== column) {
      return <ArrowUpDown className="h-4 w-4" />;
    }
    return sortConfig.direction === 'asc'
      ? <ArrowUp className="h-4 w-4" />
      : <ArrowDown className="h-4 w-4" />;
  };

  // Get visible sortable columns
  const visibleSortableColumns = visibleColumns.filter(col => col.visible);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Document List
          </div>
          <div className="flex items-center gap-2">
            <div className="text-sm font-normal text-muted-foreground">
              {filteredAndSortedDocuments.length === documents.length
                ? `${documents.length} document${documents.length !== 1 ? 's' : ''}`
                : `${filteredAndSortedDocuments.length} of ${documents.length} document${documents.length !== 1 ? 's' : ''}`
              }
            </div>

            {/* View Mode Controls */}
            <div className="flex items-center gap-1 border rounded-md p-1">
              <Button
                variant={viewMode === 'table' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('table')}
                className="h-7 px-2"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'cards' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('cards')}
                className="h-7 px-2"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
            </div>

            {/* Column Visibility Control (only show in table mode) */}
            {viewMode === 'table' && (
              <ColumnVisibilityControl
                columns={visibleColumns}
                onColumnsChange={setVisibleColumns}
              />
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {filteredAndSortedDocuments.length === 0 ? (
          <div className="text-center py-12">
            <Inbox className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No documents found</h3>
            <p className="text-muted-foreground">
              {documents.length === 0
                ? 'Start by adding your first document.'
                : 'No documents match your search criteria. Try adjusting your filters.'
              }
            </p>
          </div>
        ) : viewMode === 'table' ? (
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  {visibleSortableColumns.map((column) => (
                    <TableHead key={column.key}>
                      <Button
                        variant="ghost"
                        onClick={() => handleSort(column.key)}
                        className="h-auto p-0 font-medium hover:bg-transparent"
                      >
                        {column.label}
                        <span className="ml-2">
                          {getSortIcon(column.key)}
                        </span>
                      </Button>
                    </TableHead>
                  ))}
                  <TableHead>Attachment</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAndSortedDocuments.map(document => (
                  <DocumentRow
                    key={document.id}
                    document={document}
                    visibleColumns={visibleSortableColumns}
                  />
                ))}
              </TableBody>
            </Table>
          </div>
        ) : (
          /* Card View */
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredAndSortedDocuments.map(document => (
              <DocumentCard key={document.id} document={document} />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DocumentTable;
