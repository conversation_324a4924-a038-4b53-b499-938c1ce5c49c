.file-upload-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.file-upload-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.file-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: 1px solid var(--primary-color);
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.file-btn:hover {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
  transform: translateY(-1px);
}

.file-name {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-style: italic;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file-btn {
  background: var(--danger-color);
  color: var(--white);
  border: none;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  font-size: 0.75rem;
}

.remove-file-btn:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.file-drop-zone {
  border: 2px dashed var(--gray-300);
  border-radius: var(--border-radius);
  padding: var(--spacing-xl);
  text-align: center;
  background: var(--bg-secondary);
  transition: var(--transition);
  cursor: pointer;
}

.file-drop-zone:hover,
.file-drop-zone.dragover {
  border-color: var(--primary-color);
  background: rgba(79, 70, 229, 0.05);
}

.file-drop-zone i {
  font-size: 2rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-sm);
}

.file-drop-zone p {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.file-drop-zone small {
  color: var(--text-muted);
  font-size: 0.75rem;
}

/* Responsive Design */
@media (max-width: 480px) {
  .file-upload-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .file-drop-zone {
    padding: var(--spacing-md);
  }

  .file-drop-zone i {
    font-size: 1.5rem;
  }
}
