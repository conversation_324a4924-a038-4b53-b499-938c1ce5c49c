import React from 'react';
import { DocumentType } from '../types';
import './Badge.css';

interface TypeBadgeProps {
  type: DocumentType;
}

const TypeBadge: React.FC<TypeBadgeProps> = ({ type }) => {
  const getBadgeClass = (type: DocumentType): string => {
    const classes: Record<DocumentType, string> = {
      'Processus': 'badge-processus',
      'Procédure': 'badge-procedure',
      'Enregistrement': 'badge-enregistrement',
      'Manuel': 'badge-manuel',
      'Logiciel': 'badge-logiciel'
    };
    return classes[type] || 'badge-default';
  };

  return (
    <span className={`badge ${getBadgeClass(type)}`}>
      {type}
    </span>
  );
};

export default TypeBadge;
