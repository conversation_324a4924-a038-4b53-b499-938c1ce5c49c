.search-controls {
  padding: var(--spacing-xl);
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-lg);
  align-items: end;
}

.search-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--text-muted);
  font-size: 0.875rem;
  z-index: 5;
}

#searchInput {
  width: 100%;
  padding: 0.75rem 2.5rem 0.75rem 2.5rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: var(--transition);
}

#searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  background: var(--white);
}

#searchInput::placeholder {
  color: var(--text-muted);
}

.clear-search {
  position: absolute;
  right: 0.5rem;
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: var(--transition);
  display: none;
  width: 1.5rem;
  height: 1.5rem;
  align-items: center;
  justify-content: center;
}

.clear-search:hover {
  background-color: var(--gray-100);
  color: var(--text-primary);
}

.clear-search.show {
  display: flex;
}

.filter-group {
  display: flex;
  gap: var(--spacing-md);
}

.filter-group select {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid var(--gray-300);
  border-radius: var(--border-radius);
  background: var(--bg-primary);
  font-size: 0.875rem;
  color: var(--text-primary);
  transition: var(--transition);
}

.filter-group select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  background: var(--white);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .search-controls {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .filter-group {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .search-controls {
    padding: var(--spacing-md);
  }
}
