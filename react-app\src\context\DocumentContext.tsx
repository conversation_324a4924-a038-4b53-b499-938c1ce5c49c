import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { Document, AppState, SearchFilters, SortConfig } from '../types';
import { generateSampleData } from '../utils/sampleData';
import { saveToLocalStorage, loadFromLocalStorage } from '../utils/localStorage';

interface DocumentContextType extends AppState {
  addDocument: (document: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateDocument: (id: string, document: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => void;
  deleteDocument: (id: string) => void;
  setCurrentEditId: (id: string | null) => void;
  setSearchFilters: (filters: Partial<SearchFilters>) => void;
  setSortConfig: (config: SortConfig) => void;
  setLoading: (loading: boolean) => void;
  resetToSampleData: () => void;
  clearAllDocuments: () => void;
  dismissWelcome: () => void;
}

type DocumentAction =
  | { type: 'SET_DOCUMENTS'; payload: Document[] }
  | { type: 'ADD_DOCUMENT'; payload: Document }
  | { type: 'UPDATE_DOCUMENT'; payload: { id: string; document: Document } }
  | { type: 'DELETE_DOCUMENT'; payload: string }
  | { type: 'SET_CURRENT_EDIT_ID'; payload: string | null }
  | { type: 'SET_SEARCH_FILTERS'; payload: Partial<SearchFilters> }
  | { type: 'SET_SORT_CONFIG'; payload: SortConfig }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'DISMISS_WELCOME' };

const initialState: AppState = {
  documents: [],
  currentEditId: null,
  searchFilters: {
    searchTerm: '',
    typeFilter: '',
    statusFilter: '',
  },
  sortConfig: {
    column: null,
    direction: 'asc',
  },
  isLoading: false,
  hasSeenWelcome: localStorage.getItem('hasSeenWelcome') === 'true',
};

const documentReducer = (state: AppState, action: DocumentAction): AppState => {
  switch (action.type) {
    case 'SET_DOCUMENTS':
      return { ...state, documents: action.payload };
    case 'ADD_DOCUMENT':
      return { ...state, documents: [...state.documents, action.payload] };
    case 'UPDATE_DOCUMENT':
      return {
        ...state,
        documents: state.documents.map(doc =>
          doc.id === action.payload.id ? action.payload.document : doc
        ),
      };
    case 'DELETE_DOCUMENT':
      return {
        ...state,
        documents: state.documents.filter(doc => doc.id !== action.payload),
      };
    case 'SET_CURRENT_EDIT_ID':
      return { ...state, currentEditId: action.payload };
    case 'SET_SEARCH_FILTERS':
      return {
        ...state,
        searchFilters: { ...state.searchFilters, ...action.payload },
      };
    case 'SET_SORT_CONFIG':
      return { ...state, sortConfig: action.payload };
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'DISMISS_WELCOME':
      return { ...state, hasSeenWelcome: true };
    default:
      return state;
  }
};

const DocumentContext = createContext<DocumentContextType | undefined>(undefined);

export const DocumentProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(documentReducer, initialState);

  useEffect(() => {
    // Load documents from localStorage on mount
    const savedDocuments = loadFromLocalStorage();
    if (savedDocuments.length === 0) {
      // Generate sample data if no documents exist
      const sampleData = generateSampleData();
      dispatch({ type: 'SET_DOCUMENTS', payload: sampleData });
      saveToLocalStorage(sampleData);
    } else {
      dispatch({ type: 'SET_DOCUMENTS', payload: savedDocuments });
    }
  }, []);

  const addDocument = (documentData: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => {
    const newDocument: Document = {
      ...documentData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    dispatch({ type: 'ADD_DOCUMENT', payload: newDocument });
    const updatedDocuments = [...state.documents, newDocument];
    saveToLocalStorage(updatedDocuments);
  };

  const updateDocument = (id: string, documentData: Omit<Document, 'id' | 'createdAt' | 'updatedAt'>) => {
    const existingDoc = state.documents.find(doc => doc.id === id);
    if (!existingDoc) return;

    const updatedDocument: Document = {
      ...documentData,
      id,
      createdAt: existingDoc.createdAt,
      updatedAt: new Date().toISOString(),
    };
    dispatch({ type: 'UPDATE_DOCUMENT', payload: { id, document: updatedDocument } });
    const updatedDocuments = state.documents.map(doc =>
      doc.id === id ? updatedDocument : doc
    );
    saveToLocalStorage(updatedDocuments);
  };

  const deleteDocument = (id: string) => {
    dispatch({ type: 'DELETE_DOCUMENT', payload: id });
    const updatedDocuments = state.documents.filter(doc => doc.id !== id);
    saveToLocalStorage(updatedDocuments);
  };

  const setCurrentEditId = (id: string | null) => {
    dispatch({ type: 'SET_CURRENT_EDIT_ID', payload: id });
  };

  const setSearchFilters = (filters: Partial<SearchFilters>) => {
    dispatch({ type: 'SET_SEARCH_FILTERS', payload: filters });
  };

  const setSortConfig = (config: SortConfig) => {
    dispatch({ type: 'SET_SORT_CONFIG', payload: config });
  };

  const setLoading = (loading: boolean) => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  };

  const resetToSampleData = () => {
    const sampleData = generateSampleData();
    dispatch({ type: 'SET_DOCUMENTS', payload: sampleData });
    saveToLocalStorage(sampleData);
  };

  const clearAllDocuments = () => {
    dispatch({ type: 'SET_DOCUMENTS', payload: [] });
    saveToLocalStorage([]);
  };

  const dismissWelcome = () => {
    dispatch({ type: 'DISMISS_WELCOME' });
    localStorage.setItem('hasSeenWelcome', 'true');
  };

  const contextValue: DocumentContextType = {
    ...state,
    addDocument,
    updateDocument,
    deleteDocument,
    setCurrentEditId,
    setSearchFilters,
    setSortConfig,
    setLoading,
    resetToSampleData,
    clearAllDocuments,
    dismissWelcome,
  };

  return (
    <DocumentContext.Provider value={contextValue}>
      {children}
    </DocumentContext.Provider>
  );
};

export const useDocuments = (): DocumentContextType => {
  const context = useContext(DocumentContext);
  if (!context) {
    throw new Error('useDocuments must be used within a DocumentProvider');
  }
  return context;
};
