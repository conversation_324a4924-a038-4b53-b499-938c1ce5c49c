# Enhanced Photo Storage Service

A comprehensive, production-ready photo storage service for the HACCP application with advanced features including retry logic, memory management, indexing, and comprehensive error handling.

## Features

### 🚀 Core Functionality
- **File Upload & Storage**: Robust photo upload with progress tracking
- **Validation**: Comprehensive file type and size validation
- **Batch Operations**: Upload and manage multiple photos efficiently
- **Claim Association**: Link photos to specific claims with optimized indexing

### 🛡️ Enhanced Error Handling
- **Typed Errors**: Specific error types with detailed messages
- **Retry Logic**: Automatic retry for failed uploads with exponential backoff
- **Validation Warnings**: Non-blocking warnings for potential issues
- **Graceful Degradation**: Handles edge cases and network failures

### ⚡ Performance Optimizations
- **Indexed Storage**: Fast claim-based photo retrieval using Map indexing
- **Memory Management**: Automatic blob URL cleanup to prevent memory leaks
- **Efficient Queries**: O(1) photo count and existence checks
- **Batch Processing**: Optimized batch upload and deletion operations

### 📊 Monitoring & Analytics
- **Service Health**: Real-time health monitoring and memory usage tracking
- **Statistics**: Comprehensive photo statistics and type distribution
- **Configuration**: Runtime configuration management
- **Debugging**: Enhanced logging and debugging utilities

## Quick Start

### Basic Usage

```typescript
import * as photoService from '@/services/photoStorageService';

// Upload a single photo
const photoFile: PhotoFile = {
  id: 'photo_123',
  file: selectedFile,
  url: URL.createObjectURL(selectedFile),
  name: selectedFile.name,
  size: selectedFile.size
};

try {
  const storedPhoto = await photoService.uploadPhoto(photoFile, 'claim-123');
  console.log('Photo uploaded:', storedPhoto);
} catch (error) {
  console.error('Upload failed:', error);
}

// Get photos for a claim
const claimPhotos = photoService.getPhotosForClaim('claim-123');
```

### Using the React Hook

```typescript
import { usePhotoService } from '@/hooks/usePhotoService';

function PhotoUploadComponent({ claimId }: { claimId: string }) {
  const {
    photos,
    storedPhotos,
    isUploading,
    addPhotos,
    uploadAllPhotos,
    removePhoto,
    hasPhotos
  } = usePhotoService({
    claimId,
    maxFiles: 10,
    onUploadComplete: (photos) => {
      console.log('Upload completed:', photos);
    }
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      addPhotos(event.target.files);
    }
  };

  return (
    <div>
      <input type="file" multiple accept="image/*" onChange={handleFileSelect} />
      <button onClick={uploadAllPhotos} disabled={isUploading}>
        Upload All Photos
      </button>
      {hasPhotos() && <p>Total photos: {photos.length + storedPhotos.length}</p>}
    </div>
  );
}
```

## Configuration

### Service Configuration

```typescript
import { updateServiceConfig } from '@/services/photoStorageService';

// Update configuration
updateServiceConfig({
  maxFileSize: 10 * 1024 * 1024, // 10MB
  maxFiles: 20,
  enableCompression: true,
  compressionQuality: 0.8,
  retryAttempts: 5,
  retryDelay: 2000
});
```

### Default Configuration

```typescript
{
  maxFileSize: 5 * 1024 * 1024, // 5MB
  maxFiles: 10,
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  enableCompression: true,
  compressionQuality: 0.8,
  thumbnailSize: 200,
  enableThumbnails: true,
  retryAttempts: 3,
  retryDelay: 1000
}
```

## API Reference

### Core Functions

#### `uploadPhoto(photoFile, claimId?, onProgress?)`
Upload a single photo with retry logic and progress tracking.

#### `uploadPhotos(photoFiles, claimId?, onProgress?)`
Batch upload multiple photos with individual progress tracking.

#### `getPhotosForClaim(claimId)`
Retrieve all photos associated with a specific claim (optimized with indexing).

#### `deletePhoto(photoId)`
Delete a photo and clean up associated resources.

#### `validatePhotoFile(file)`
Comprehensive file validation with detailed error reporting.

### Utility Functions

#### `formatFileSize(bytes)`
Format file size in human-readable format.

#### `generatePhotoId()`
Generate unique photo identifiers.

#### `generateChecksum(input)`
Generate checksums for file integrity verification.

### Memory Management

#### `cleanupPhotoUrl(url)`
Clean up individual blob URLs to prevent memory leaks.

#### `cleanupAllUrls()`
Clean up all tracked blob URLs.

#### `clearAllPhotos()`
Clear all photos and associated data (for testing).

### Monitoring

#### `getPhotoStatistics()`
Get comprehensive statistics about stored photos.

#### `getServiceHealth()`
Get service health information including memory usage.

## Error Handling

### Error Types

```typescript
enum PhotoErrorType {
  INVALID_FILE_TYPE = 'INVALID_FILE_TYPE',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  NETWORK_ERROR = 'NETWORK_ERROR',
  PROCESSING_FAILED = 'PROCESSING_FAILED',
  STORAGE_QUOTA_EXCEEDED = 'STORAGE_QUOTA_EXCEEDED',
  INVALID_IMAGE_FORMAT = 'INVALID_IMAGE_FORMAT',
  CORRUPTED_FILE = 'CORRUPTED_FILE'
}
```

### Error Handling Example

```typescript
try {
  const result = await photoService.uploadPhoto(photoFile);
} catch (error) {
  if (error instanceof Error) {
    // Handle specific error types
    if (error.message.includes('retry')) {
      // Handle retry exhaustion
    } else if (error.message.includes('network')) {
      // Handle network errors
    }
  }
}
```

## Testing

Run the comprehensive test suite:

```bash
npm test src/services/__tests__/photoStorageService.test.ts
```

The test suite covers:
- Configuration management
- File validation
- Upload functionality with retries
- Photo retrieval and indexing
- Deletion operations
- Statistics and health monitoring
- Memory management
- Utility functions

## Production Considerations

### Migration to Real Backend

When moving to production, replace the mock implementation with real backend calls:

1. **Upload Function**: Replace mock upload with actual API calls
2. **Storage**: Replace Map storage with database integration
3. **File Processing**: Implement real image compression and thumbnail generation
4. **Authentication**: Add proper authentication and authorization
5. **CDN Integration**: Use cloud storage services (AWS S3, Google Cloud Storage)

### Performance Optimization

- Implement lazy loading for large photo collections
- Add image compression and optimization
- Use CDN for photo delivery
- Implement caching strategies
- Add database indexing for production queries

### Security Considerations

- Validate file types on server-side
- Implement virus scanning
- Add rate limiting for uploads
- Secure file storage with proper permissions
- Implement CSRF protection

## Contributing

When contributing to the photo service:

1. Maintain backward compatibility with existing PhotoFile interface
2. Add comprehensive tests for new features
3. Update documentation for API changes
4. Follow the established error handling patterns
5. Consider performance implications of changes

## License

This photo storage service is part of the HACCP application and follows the same licensing terms.
