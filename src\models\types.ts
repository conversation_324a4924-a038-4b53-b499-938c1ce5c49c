type SeverityLevel = 'critical' | 'major' | 'minor';

export interface ClaimsManagementConfig {
  maxPhotosPerClaim: number;
  maxPhotoSizeMB: number;
  allowedFileTypes: string[];
  autoSaveDraftInterval: number;
  defaultSeverity: SeverityLevel;
}

// User types
export type UserRole = 'admin' | 'qa' | 'auditor' | 'viewer';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
}

// HACCP types
export interface Product {
  id: string;
  name: string;
  description: string;
  processSteps: ProcessStep[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

export interface ProcessStep {
  id: string;
  name: string;
  description: string;
  order: number;
}

export type HazardType = 'Biological' | 'Chemical' | 'Physical';

export interface Hazard {
  id: string;
  processStepId: string;
  description: string;
  type: HazardType;
  severity: number; // 1-5
  likelihood: number; // 1-5
  controlMeasures: string;
  isCCP: boolean;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

export interface CCP {
  id: string;
  hazardId: string;
  criticalLimit: string;
  monitoringProcedure: string;
  monitoringFrequency: string;
  correctiveAction: string;
  verificationProcedure: string;
  recordkeepingProcedure: string;
}

export interface HACCPPlan {
  id: string;
  productId: string;
  name: string;
  version: string;
  status: 'draft' | 'under_review' | 'approved' | 'archived';
  approvedBy?: string;
  approvedDate?: Date;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Audit types
export interface AuditLog {
  id: string;
  entityType: 'product' | 'hazard' | 'ccp' | 'plan';
  entityId: string;
  action: 'create' | 'update' | 'delete' | 'approve' | 'review';
  userId: string;
  timestamp: Date;
  details: string;
}

export interface ClaimsManagementConfig {
  maxPhotosPerClaim: number;
  maxPhotoSizeMB: number;
  allowedFileTypes: string[];
  autoSaveDraftInterval: number;
  defaultSeverity: SeverityLevel;
}

