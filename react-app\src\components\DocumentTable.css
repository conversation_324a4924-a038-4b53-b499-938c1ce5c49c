.table-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-md);
}

.document-count {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.table-container {
  overflow-x: auto;
  border-radius: var(--border-radius);
  border: 1px solid var(--gray-200);
}

table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.875rem;
}

th, td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
  vertical-align: middle;
}

th {
  background-color: var(--gray-800);
  color: var(--white);
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
  white-space: nowrap;
}

th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

th.sortable:hover {
  background-color: var(--gray-700);
}

.sort-icon {
  margin-left: var(--spacing-sm);
  opacity: 0.5;
  font-size: 0.75rem;
}

th.sortable:hover .sort-icon {
  opacity: 1;
}

tbody tr {
  transition: var(--transition-fast);
}

tbody tr:hover {
  background-color: var(--gray-50);
}

tbody tr:nth-child(even) {
  background-color: rgba(249, 250, 251, 0.5);
}

tbody tr:nth-child(even):hover {
  background-color: var(--gray-50);
}

.status-modifier {
  background-color: rgba(245, 158, 11, 0.1) !important;
}

.status-modifier:hover {
  background-color: rgba(245, 158, 11, 0.15) !important;
}

/* Table Cell Enhancements */
.cell-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.cell-title {
  font-weight: 500;
  color: var(--text-primary);
}

.cell-subtitle {
  font-size: 0.75rem;
  color: var(--text-muted);
}

.ref-id {
  background: var(--gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.75rem;
  color: var(--text-primary);
  border: 1px solid var(--gray-200);
}

/* File Link */
.file-link {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
}

.file-link:hover {
  text-decoration: underline;
  color: var(--primary-hover);
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-2xl);
  color: var(--text-secondary);
}

.empty-state i {
  font-size: 3rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
}

.empty-state h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.empty-state p {
  font-size: 0.875rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  th, td {
    padding: var(--spacing-sm);
    font-size: 0.75rem;
  }
}
