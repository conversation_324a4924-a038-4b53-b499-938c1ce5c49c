// Enhanced PDF Export Service with improved UI
import jsPDF from 'jspdf';
import { HACCPPlan, Product, ProcessStep, Hazard } from '@/models/types';
import { getProcessSteps } from './planService';
import 'jspdf-autotable';

// Add type declaration for jsPD<PERSON> with autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
    previousAutoTable: {
      finalY: number;
    };
  }
  interface jsPDFAPI { }
}

// Define color palette
const COLORS = {
  primary: { r: 26, g: 54, b: 93 },     // Deep blue #1A365D
  secondary: { r: 242, g: 201, b: 76 }, // Gold #F2C94C
  accent1: { r: 45, g: 156, b: 219 },   // Teal #2D9CDB
  accent2: { r: 235, g: 87, b: 87 },    // Red #EB5757
  text: {
    dark: { r: 51, g: 51, b: 51 },      // Dark gray #333333
    medium: { r: 79, g: 79, b: 79 },    // Medium gray #4F4F4F
    light: { r: 130, g: 130, b: 130 }   // Light gray #828282
  },
  background: {
    white: { r: 255, g: 255, b: 255 },  // White #FFFFFF
    light: { r: 249, g: 249, b: 249 },  // Light gray #F9F9F9
    blue: { r: 247, g: 250, b: 252 }    // Very light blue #F7FAFC
  },
  hazard: {
    biological: { r: 198, g: 40, b: 40 },    // Red #C62828
    chemical: { r: 21, g: 101, b: 192 },     // Blue #1565C0
    physical: { r: 239, g: 108, b: 0 }       // Orange #EF6C00
  }
};

// Helper function to truncate text
const truncateText = (text: string, maxLength: number = 100): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

// Helper function to create a table with auto width and improved styling
const createEnhancedTable = (
  pdf: jsPDF,
  headers: string[],
  data: string[][],
  startY: number,
  options: any = {}
): number => {
  try {
    // Calculate available width based on page orientation
    const pageWidth = pdf.internal.pageSize.getWidth();
    const margin = 19; // Increased margins (0.75 inches)
    const availableWidth = pageWidth - (margin * 2);

    // Set default options with improved styling
    const defaultOptions = {
      startY: startY,
      head: [headers],
      body: data,
      headStyles: {
        fillColor: [COLORS.primary.r, COLORS.primary.g, COLORS.primary.b],
        textColor: [255, 255, 255],
        fontSize: 10,
        cellPadding: 6,
        halign: 'center',
        fontStyle: 'bold'
      },
      bodyStyles: {
        fontSize: 9,
        cellPadding: 6
      },
      alternateRowStyles: {
        fillColor: [249, 249, 249]
      },
      styles: {
        overflow: 'linebreak',
        cellWidth: 'auto',
        minCellWidth: 5,
        halign: 'left',
        valign: 'middle',
        fontSize: 9,
        font: 'helvetica',
        lineColor: [220, 220, 220],
        lineWidth: 0.1
      },
      margin: { top: margin, right: margin, bottom: margin, left: margin },
      theme: 'grid',
      tableWidth: availableWidth,
      horizontalPageBreak: true,
      horizontalPageBreakRepeat: 0,
      didParseCell: function(data: any) {
        // Style CCP cells
        if (data.section === 'body' && data.column.dataKey === 5 && data.cell.raw === 'Yes') {
          data.cell.styles.fontStyle = 'bold';
          data.cell.styles.textColor = [COLORS.secondary.r, COLORS.secondary.g, COLORS.secondary.b];
        }

        // Style hazard type cells
        if (data.section === 'body' && data.column.dataKey === 2) {
          if (data.cell.raw === 'Biological') {
            data.cell.styles.textColor = [COLORS.hazard.biological.r, COLORS.hazard.biological.g, COLORS.hazard.biological.b];
          } else if (data.cell.raw === 'Chemical') {
            data.cell.styles.textColor = [COLORS.hazard.chemical.r, COLORS.hazard.chemical.g, COLORS.hazard.chemical.b];
          } else if (data.cell.raw === 'Physical') {
            data.cell.styles.textColor = [COLORS.hazard.physical.r, COLORS.hazard.physical.g, COLORS.hazard.physical.b];
          }
        }
      },
      willDrawCell: function(data: any) {
        // Ensure text fits in cell by reducing font size if needed
        if (data.cell.raw && data.cell.raw.length > 0) {
          const cellWidth = data.cell.width;
          const text = data.cell.raw;
          const fontSize = data.cell.styles.fontSize;

          // Approximate width calculation
          const textWidth = text.length * (fontSize * 0.5);

          if (textWidth > cellWidth) {
            // Reduce font size to fit
            const newFontSize = Math.max(7, fontSize * (cellWidth / textWidth));
            data.cell.styles.fontSize = newFontSize;
          }
        }
      }
    };

    // Merge default options with provided options
    const mergedOptions = { ...defaultOptions, ...options };

    // Create the table
    pdf.autoTable(mergedOptions);

    // Return the final Y position
    try {
      return pdf.previousAutoTable.finalY + 10;
    } catch (error) {
      console.error('Error accessing table position:', error);
      return startY + 100; // Provide a reasonable default
    }
  } catch (error) {
    console.error('Error creating enhanced table:', error);
    // Fallback to simple text if autoTable fails
    pdf.setFontSize(10);
    pdf.text('Error creating table. See console for details.', 19, startY + 15);
    return startY + 30;
  }
};

// Interface for PDF export options
interface PDFExportOptions {
  includeProductDescription?: boolean;
  includeProcessFlow?: boolean;
  includeHazardAnalysis?: boolean;
  includeCCPs?: boolean;
  includeVerification?: boolean;
  includeApprovals?: boolean;
  includeCoverPage?: boolean;
  companyName?: string;
  companyLogo?: string;
}

// Default export options
const defaultOptions: PDFExportOptions = {
  includeProductDescription: true,
  includeProcessFlow: true,
  includeHazardAnalysis: true,
  includeCCPs: true,
  includeVerification: true,
  includeApprovals: true,
  includeCoverPage: true,
  companyName: 'HACCP Plan Pilot',
};

/**
 * Add a section divider to the PDF
 */
const addSectionDivider = (pdf: jsPDF, yPos: number): number => {
  pdf.setDrawColor(200, 200, 200);
  pdf.setLineWidth(0.5);
  pdf.line(19, yPos, pdf.internal.pageSize.getWidth() - 19, yPos);
  return yPos + 15;
};

/**
 * Add a cover page to the PDF
 */
const addCoverPage = (
  pdf: jsPDF,
  planName: string,
  planVersion: string,
  productName: string,
  currentPlan: HACCPPlan | null,
  ccpData: any[],
  companyName: string
): void => {
  // Background color for the header area
  pdf.setFillColor(COLORS.background.blue.r, COLORS.background.blue.g, COLORS.background.blue.b);
  pdf.rect(0, 0, pdf.internal.pageSize.getWidth(), 60, 'F');

  // Company name
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(16);
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text(companyName, 19, 30);

  // Document title
  pdf.setFontSize(28);
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('HACCP PLAN', pdf.internal.pageSize.getWidth() / 2, 100, { align: 'center' });

  // Plan name
  pdf.setFontSize(20);
  pdf.text(planName, pdf.internal.pageSize.getWidth() / 2, 120, { align: 'center' });

  // Status badge
  const status = currentPlan?.status
    ? currentPlan.status.charAt(0).toUpperCase() + currentPlan.status.slice(1).replace('_', ' ')
    : 'Draft';

  // Draw status badge
  const badgeWidth = 80;
  const badgeHeight = 25;
  const badgeX = pdf.internal.pageSize.getWidth() - 19 - badgeWidth;
  const badgeY = 19;

  // Set badge color based on status
  if (status === 'Approved') {
    pdf.setFillColor(39, 174, 96); // Green
  } else if (status === 'Under Review') {
    pdf.setFillColor(242, 153, 74); // Orange
  } else {
    pdf.setFillColor(149, 165, 166); // Gray for Draft
  }

  pdf.roundedRect(badgeX, badgeY, badgeWidth, badgeHeight, 3, 3, 'F');

  pdf.setFontSize(12);
  pdf.setTextColor(255, 255, 255);
  pdf.text(status, badgeX + badgeWidth / 2, badgeY + badgeHeight / 2 + 4, { align: 'center' });

  // Summary box
  const boxWidth = 300;
  const boxHeight = 120;
  const boxX = (pdf.internal.pageSize.getWidth() - boxWidth) / 2;
  const boxY = 150;

  pdf.setFillColor(COLORS.background.light.r, COLORS.background.light.g, COLORS.background.light.b);
  pdf.setDrawColor(220, 220, 220);
  pdf.roundedRect(boxX, boxY, boxWidth, boxHeight, 5, 5, 'FD');

  // Summary content
  pdf.setFontSize(12);
  pdf.setTextColor(COLORS.text.dark.r, COLORS.text.dark.g, COLORS.text.dark.b);
  pdf.setFont('helvetica', 'bold');
  pdf.text('Plan Summary', boxX + 15, boxY + 20);

  pdf.setFont('helvetica', 'normal');
  pdf.setFontSize(11);
  pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);

  const summaryItems = [
    { label: 'Product:', value: productName || 'Unknown Product' },
    { label: 'Version:', value: planVersion },
    { label: 'Date:', value: new Date().toLocaleDateString() },
    { label: 'Critical Control Points:', value: `${ccpData.length} CCPs identified` },
  ];

  let itemY = boxY + 40;
  summaryItems.forEach(item => {
    pdf.setFont('helvetica', 'bold');
    pdf.text(item.label, boxX + 15, itemY);
    pdf.setFont('helvetica', 'normal');
    pdf.text(item.value, boxX + 100, itemY);
    itemY += 15;
  });

  // Footer
  pdf.setFontSize(9);
  pdf.setTextColor(COLORS.text.light.r, COLORS.text.light.g, COLORS.text.light.b);
  pdf.text(
    'CONFIDENTIAL DOCUMENT',
    pdf.internal.pageSize.getWidth() / 2,
    pdf.internal.pageSize.getHeight() - 20,
    { align: 'center' }
  );

  pdf.text(
    `Generated on ${new Date().toLocaleDateString()} by HACCP Plan Pilot`,
    pdf.internal.pageSize.getWidth() / 2,
    pdf.internal.pageSize.getHeight() - 15,
    { align: 'center' }
  );
};

/**
 * Add enhanced header to the PDF
 */
const addEnhancedHeader = (
  pdf: jsPDF,
  planName: string,
  planVersion: string,
  productName: string,
  currentPlan: HACCPPlan | null,
  sectionName: string = ''
): void => {
  // Background color for the header area
  pdf.setFillColor(COLORS.background.blue.r, COLORS.background.blue.g, COLORS.background.blue.b);
  pdf.rect(0, 0, pdf.internal.pageSize.getWidth(), 25, 'F');

  // Company name (left side)
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(10);
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('HACCP Plan Pilot', 19, 10);

  // Plan name (center)
  pdf.setFontSize(10);
  pdf.setTextColor(COLORS.text.dark.r, COLORS.text.dark.g, COLORS.text.dark.b);
  pdf.text(planName, pdf.internal.pageSize.getWidth() / 2, 10, { align: 'center' });

  // Section name if provided
  if (sectionName) {
    pdf.setFontSize(9);
    pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);
    pdf.text(sectionName, pdf.internal.pageSize.getWidth() / 2, 17, { align: 'center' });
  }

  // Version and status (right side)
  const status = currentPlan?.status
    ? currentPlan.status.charAt(0).toUpperCase() + currentPlan.status.slice(1).replace('_', ' ')
    : 'Draft';

  pdf.setFontSize(8);
  pdf.text(`v${planVersion} | ${status}`, pdf.internal.pageSize.getWidth() - 19, 10, { align: 'right' });

  // Add horizontal line below header
  pdf.setDrawColor(200, 200, 200);
  pdf.setLineWidth(0.5);
  pdf.line(19, 25, pdf.internal.pageSize.getWidth() - 19, 25);
};

/**
 * Add enhanced footer to the PDF
 */
const addEnhancedFooter = (
  pdf: jsPDF,
  currentPage: number,
  pageCount: number,
  planName: string,
  planVersion: string
): void => {
  // Get page dimensions
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();

  // Add horizontal line above footer
  pdf.setDrawColor(200, 200, 200);
  pdf.setLineWidth(0.5);
  pdf.line(19, pageHeight - 15, pageWidth - 19, pageHeight - 15);

  // Add footer text
  pdf.setFontSize(8);
  pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);

  // Left side: Document ID and confidentiality
  pdf.text('CONFIDENTIAL', 19, pageHeight - 8);

  // Center: Plan name and version
  pdf.text(
    `${planName} v${planVersion}`,
    pageWidth / 2,
    pageHeight - 8,
    { align: 'center' }
  );

  // Right side: Page numbers
  pdf.text(
    `Page ${currentPage} of ${pageCount}`,
    pageWidth - 19,
    pageHeight - 8,
    { align: 'right' }
  );
};

/**
 * Add enhanced product description section
 */
const addEnhancedProductDescription = (
  pdf: jsPDF,
  productDetails: Product | null,
  startY: number
): number => {
  // Section title with improved styling
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('1. Product Description', 19, startY);

  // Section background
  pdf.setFillColor(COLORS.background.light.r, COLORS.background.light.g, COLORS.background.light.b);
  pdf.rect(19, startY + 5, pdf.internal.pageSize.getWidth() - 38, 50, 'F');

  // Product description
  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);

  const description = productDetails?.description || 'No product description available.';

  // Split long text into multiple lines
  const textLines = pdf.splitTextToSize(description, pdf.internal.pageSize.getWidth() - 50);
  pdf.text(textLines, 25, startY + 20);

  // If no description, add a note
  if (!productDetails?.description) {
    pdf.setFont('helvetica', 'italic');
    pdf.setTextColor(COLORS.accent2.r, COLORS.accent2.g, COLORS.accent2.b);
    pdf.text(
      'Please add a detailed product description including packaging, storage conditions, and intended use.',
      25,
      startY + 35
    );
  }

  // Return the new Y position
  return startY + 65;
};

/**
 * Add enhanced process flow diagram
 */
const addEnhancedProcessFlow = (
  pdf: jsPDF,
  processSteps: ProcessStep[],
  ccpData: any[],
  startY: number
): number => {
  // Section title with improved styling
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('2. Process Flow Diagram', 19, startY);

  // Start position for the flow diagram
  let yPos = startY + 15;
  const xCenter = pdf.internal.pageSize.getWidth() / 2;
  const boxWidth = 100;
  const boxHeight = 40;
  const arrowHeight = 15;
  const arrowWidth = 8;

  // Draw process flow boxes
  if (processSteps.length > 0) {
    // Sort steps by order
    const sortedSteps = [...processSteps].sort((a, b) => a.order - b.order);

    sortedSteps.forEach((step, index) => {
      // Determine if this step is a CCP
      const isCCP = ccpData.some(ccp => ccp.hazard.processStepId === step.id);

      // Draw box with enhanced styling
      if (isCCP) {
        // Gold gradient for CCPs
        const gradient = pdf.setFillColor(255, 245, 200);
        pdf.setDrawColor(COLORS.secondary.r, COLORS.secondary.g, COLORS.secondary.b);
        pdf.setLineWidth(2);
      } else {
        // Light gradient for regular steps
        pdf.setFillColor(250, 250, 250);
        pdf.setDrawColor(220, 220, 220);
        pdf.setLineWidth(0.75);
      }

      // Draw rounded rectangle with shadow effect
      // First draw a light shadow
      pdf.setFillColor(230, 230, 230);
      pdf.roundedRect(
        xCenter - (boxWidth / 2) + 2,
        yPos + 2,
        boxWidth,
        boxHeight,
        5,
        5,
        'F'
      );

      // Then draw the actual box
      if (isCCP) {
        pdf.setFillColor(255, 245, 200); // Yellow for CCPs
      } else {
        pdf.setFillColor(250, 250, 250); // White with slight gray for regular steps
      }

      pdf.roundedRect(
        xCenter - (boxWidth / 2),
        yPos,
        boxWidth,
        boxHeight,
        5,
        5,
        'FD'
      );

      // Add step name with enhanced styling
      pdf.setFontSize(isCCP ? 12 : 11);
      pdf.setFont('helvetica', isCCP ? 'bold' : 'normal');
      pdf.setTextColor(COLORS.text.dark.r, COLORS.text.dark.g, COLORS.text.dark.b);
      pdf.text(
        step.name,
        xCenter,
        yPos + 20,
        { align: 'center' }
      );

      // Add step number
      pdf.setFontSize(9);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(COLORS.text.light.r, COLORS.text.light.g, COLORS.text.light.b);
      pdf.text(
        `Step ${step.order}`,
        xCenter,
        yPos + 30,
        { align: 'center' }
      );

      // Add CCP badge if applicable
      if (isCCP) {
        // Draw CCP badge
        const badgeWidth = 30;
        const badgeHeight = 15;
        const badgeX = xCenter + (boxWidth / 2) - badgeWidth - 5;
        const badgeY = yPos + 5;

        pdf.setFillColor(COLORS.secondary.r, COLORS.secondary.g, COLORS.secondary.b);
        pdf.setDrawColor(COLORS.secondary.r - 30, COLORS.secondary.g - 30, COLORS.secondary.b - 30);
        pdf.roundedRect(
          badgeX,
          badgeY,
          badgeWidth,
          badgeHeight,
          3,
          3,
          'FD'
        );

        pdf.setFontSize(9);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(255, 255, 255);
        pdf.text(
          'CCP',
          badgeX + (badgeWidth / 2),
          badgeY + 10,
          { align: 'center' }
        );
      }

      // Add arrow if not the last step
      if (index < sortedSteps.length - 1) {
        yPos += boxHeight + 10;

        // Enhanced arrow styling with gradient
        pdf.setDrawColor(150, 150, 150);
        pdf.setLineWidth(1);

        // Arrow line
        pdf.line(xCenter, yPos, xCenter, yPos + arrowHeight);

        // Arrow head with enhanced styling
        pdf.setFillColor(150, 150, 150);

        // Draw arrow head as a filled triangle
        pdf.triangle(
          xCenter - arrowWidth, yPos + arrowHeight - arrowWidth,
          xCenter + arrowWidth, yPos + arrowHeight - arrowWidth,
          xCenter, yPos + arrowHeight,
          'F'
        );

        yPos += arrowHeight + 10;
      }
    });
  } else {
    // Default message if no process steps
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'italic');
    pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);
    pdf.text('No process steps have been defined.', 19, yPos + 20);
    yPos += 30;
  }

  // Return the new Y position
  return yPos + boxHeight + 10;
};

/**
 * Add enhanced hazard analysis section
 */
const addEnhancedHazardAnalysis = (
  pdf: jsPDF,
  hazardData: any[],
  processSteps: ProcessStep[],
  startY: number
): number => {
  // Section title with improved styling
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('3. Hazard Analysis', 19, startY);

  // If no hazards, show message with improved styling
  if (!hazardData || hazardData.length === 0) {
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'italic');
    pdf.setTextColor(COLORS.accent2.r, COLORS.accent2.g, COLORS.accent2.b);

    // Add warning box
    pdf.setFillColor(255, 245, 245);
    pdf.setDrawColor(COLORS.accent2.r, COLORS.accent2.g, COLORS.accent2.b);
    pdf.roundedRect(19, startY + 10, pdf.internal.pageSize.getWidth() - 38, 30, 3, 3, 'FD');

    pdf.text('No hazards have been identified for this product.', 25, startY + 25);
    pdf.text('A complete HACCP plan requires hazard identification and analysis.', 25, startY + 35);

    return startY + 50;
  }

  // Add brief explanation
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);
  pdf.text(
    'This section identifies potential hazards associated with each process step and evaluates their significance.',
    19,
    startY + 10
  );

  // Create table data with more aggressive truncation
  const tableData = hazardData.map(hazard => {
    const step = processSteps.find(s => s.id === hazard.processStepId);
    return [
      truncateText(step?.name || 'Unknown Step', 20),
      truncateText(hazard.description, 30),
      truncateText(hazard.type, 10),
      `${hazard.severity} x ${hazard.likelihood}`,
      truncateText(hazard.controlMeasures, 40),
      hazard.isCCP ? 'Yes' : 'No'
    ];
  });

  // Use our enhanced table function
  const headers = ['Process Step', 'Hazard', 'Type', 'Risk', 'Control Measures', 'CCP?'];

  // Set column widths proportionally
  const columnWidths = {
    0: { cellWidth: '15%' }, // Process Step
    1: { cellWidth: '25%' }, // Hazard
    2: { cellWidth: '10%' }, // Type
    3: { cellWidth: '10%' }, // Risk
    4: { cellWidth: '30%' }, // Control Measures
    5: { cellWidth: '10%' }  // CCP?
  };

  // Add color coding for hazard types
  const cellStyles = {
    2: { // Type column
      'Biological': {
        fillColor: [255, 235, 238],
        textColor: [COLORS.hazard.biological.r, COLORS.hazard.biological.g, COLORS.hazard.biological.b]
      },
      'Chemical': {
        fillColor: [227, 242, 253],
        textColor: [COLORS.hazard.chemical.r, COLORS.hazard.chemical.g, COLORS.hazard.chemical.b]
      },
      'Physical': {
        fillColor: [255, 243, 224],
        textColor: [COLORS.hazard.physical.r, COLORS.hazard.physical.g, COLORS.hazard.physical.b]
      }
    },
    5: { // CCP column
      'Yes': {
        fillColor: [255, 249, 230],
        textColor: [COLORS.secondary.r, COLORS.secondary.g, COLORS.secondary.b],
        fontStyle: 'bold'
      }
    }
  };

  return createEnhancedTable(pdf, headers, tableData, startY + 15, {
    columnStyles: columnWidths,
    cellStyles: cellStyles
  });
};

/**
 * Add enhanced CCPs section
 */
const addEnhancedCCPs = (
  pdf: jsPDF,
  ccpData: any[],
  processSteps: ProcessStep[],
  startY: number
): number => {
  // Section title with improved styling
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('4. Critical Control Points', 19, startY);

  // If no CCPs, show message with improved styling
  if (!ccpData || ccpData.length === 0) {
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'italic');
    pdf.setTextColor(COLORS.accent2.r, COLORS.accent2.g, COLORS.accent2.b);

    // Add warning box
    pdf.setFillColor(255, 245, 245);
    pdf.setDrawColor(COLORS.accent2.r, COLORS.accent2.g, COLORS.accent2.b);
    pdf.roundedRect(19, startY + 10, pdf.internal.pageSize.getWidth() - 38, 30, 3, 3, 'FD');

    pdf.text('No Critical Control Points have been defined for this product.', 25, startY + 25);
    pdf.text('A valid HACCP plan requires at least one CCP to control significant hazards.', 25, startY + 35);

    return startY + 50;
  }

  // Add brief explanation
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);
  pdf.text(
    'This section details the Critical Control Points where control measures must be applied to prevent, eliminate, or reduce food safety hazards.',
    19,
    startY + 10
  );

  // Create table data with more aggressive truncation
  const tableData = ccpData.map(item => {
    const step = processSteps.find(s => s.id === item.hazard.processStepId);
    return [
      truncateText(step?.name || 'Unknown Step', 20),
      truncateText(item.hazard.description, 30),
      truncateText(item.ccp.criticalLimit || 'Not defined', 25),
      truncateText(item.ccp.monitoringProcedure || 'Not defined', 30),
      truncateText(item.ccp.monitoringFrequency || 'Not defined', 20),
      truncateText(item.ccp.correctiveAction || 'Not defined', 30),
      truncateText(item.ccp.verificationProcedure || 'Not defined', 25),
      truncateText(item.ccp.recordkeepingProcedure || 'Not defined', 20)
    ];
  });

  // Use our enhanced table function
  const headers = [
    'Process Step',
    'Hazard',
    'Critical Limits',
    'Monitoring Procedure',
    'Frequency',
    'Corrective Action',
    'Verification',
    'Records'
  ];

  // Set column widths proportionally
  const columnWidths = {
    0: { cellWidth: '10%' }, // Process Step
    1: { cellWidth: '15%' }, // Hazard
    2: { cellWidth: '12%' }, // Critical Limits
    3: { cellWidth: '15%' }, // Monitoring Procedure
    4: { cellWidth: '10%' }, // Frequency
    5: { cellWidth: '15%' }, // Corrective Action
    6: { cellWidth: '13%' }, // Verification
    7: { cellWidth: '10%' }  // Records
  };

  // Add color coding for different columns
  const columnStyles = {
    2: { // Critical Limits
      fillColor: [255, 249, 230]
    },
    5: { // Corrective Action
      fillColor: [255, 235, 238]
    }
  };

  return createEnhancedTable(pdf, headers, tableData, startY + 15, {
    columnStyles: columnWidths,
    alternateRowStyles: {
      fillColor: [250, 250, 250]
    },
    headStyles: {
      fillColor: [COLORS.primary.r, COLORS.primary.g, COLORS.primary.b],
      textColor: [255, 255, 255],
      fontSize: 10,
      cellPadding: 6,
      halign: 'center',
      fontStyle: 'bold'
    }
  });
};

/**
 * Add enhanced verification procedures section
 */
const addEnhancedVerificationProcedures = (
  pdf: jsPDF,
  startY: number
): number => {
  // Section title with improved styling
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('5. Verification Procedures', 19, startY);

  // Add section background
  pdf.setFillColor(COLORS.background.light.r, COLORS.background.light.g, COLORS.background.light.b);
  pdf.rect(19, startY + 10, pdf.internal.pageSize.getWidth() - 38, 80, 'F');

  // Add verification procedures with improved styling
  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(COLORS.text.dark.r, COLORS.text.dark.g, COLORS.text.dark.b);

  const procedures = [
    {
      title: 'Daily Verification',
      items: [
        'Calibration of thermometers and other measuring devices',
        'Visual inspection of CCPs',
        'Review of monitoring records'
      ]
    },
    {
      title: 'Weekly Verification',
      items: [
        'Review of corrective action records',
        'Verification of monitoring activities'
      ]
    },
    {
      title: 'Monthly Verification',
      items: [
        'Microbiological testing of product samples',
        'Environmental monitoring',
        'Review of customer complaints'
      ]
    },
    {
      title: 'Annual Verification',
      items: [
        'Complete HACCP system review',
        'Internal audits',
        'Validation of critical limits'
      ]
    }
  ];

  let yPos = startY + 20;

  procedures.forEach(procedure => {
    // Add procedure title
    pdf.setFont('helvetica', 'bold');
    pdf.setFontSize(11);
    pdf.text(procedure.title, 25, yPos);
    yPos += 7;

    // Add procedure items
    pdf.setFont('helvetica', 'normal');
    pdf.setFontSize(10);

    procedure.items.forEach(item => {
      pdf.text(`• ${item}`, 35, yPos);
      yPos += 6;
    });

    yPos += 5;
  });

  // Return the new Y position
  return yPos + 10;
};

/**
 * Add enhanced approvals section
 */
const addEnhancedApprovals = (
  pdf: jsPDF,
  currentPlan: HACCPPlan | null,
  startY: number
): number => {
  // Section title with improved styling
  pdf.setFontSize(16);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(COLORS.primary.r, COLORS.primary.g, COLORS.primary.b);
  pdf.text('6. Approvals', 19, startY);

  // Add section background
  pdf.setFillColor(COLORS.background.light.r, COLORS.background.light.g, COLORS.background.light.b);
  pdf.rect(19, startY + 10, pdf.internal.pageSize.getWidth() - 38, 100, 'F');

  // Approval information with improved styling
  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(COLORS.text.dark.r, COLORS.text.dark.g, COLORS.text.dark.b);

  let yPos = startY + 20;

  // Approval status
  const status = currentPlan?.status
    ? currentPlan.status.charAt(0).toUpperCase() + currentPlan.status.slice(1).replace('_', ' ')
    : 'Draft';

  // Status badge
  const badgeWidth = 80;
  const badgeHeight = 20;
  const badgeX = 100;
  const badgeY = yPos - 15;

  // Set badge color based on status
  if (status === 'Approved') {
    pdf.setFillColor(39, 174, 96); // Green
  } else if (status === 'Under Review') {
    pdf.setFillColor(242, 153, 74); // Orange
  } else {
    pdf.setFillColor(149, 165, 166); // Gray for Draft
  }

  pdf.roundedRect(badgeX, badgeY, badgeWidth, badgeHeight, 3, 3, 'F');

  pdf.setFontSize(10);
  pdf.setTextColor(255, 255, 255);
  pdf.text(status, badgeX + badgeWidth / 2, badgeY + badgeHeight / 2 + 3, { align: 'center' });

  // Reset text color
  pdf.setTextColor(COLORS.text.dark.r, COLORS.text.dark.g, COLORS.text.dark.b);

  pdf.text(`Plan Status:`, 25, yPos);
  yPos += 15;

  // Approval date
  if (currentPlan?.approvedDate) {
    pdf.text(`Approved on: ${new Date(currentPlan.approvedDate).toLocaleDateString()}`, 25, yPos);
    yPos += 10;
  }

  // Approved by
  if (currentPlan?.approvedBy) {
    pdf.text(`Approved by: ${currentPlan.approvedBy}`, 25, yPos);
    yPos += 10;
  }

  // Signature boxes with improved styling
  yPos += 5;
  pdf.setFont('helvetica', 'bold');
  pdf.text('Required Signatures:', 25, yPos);
  yPos += 10;

  const signatures = [
    { title: 'Quality Manager', role: 'Plan creation and initial approval' },
    { title: 'Food Safety Team', role: 'Technical review and hazard validation' },
    { title: 'Operations Manager', role: 'Implementation feasibility' },
    { title: 'Site Director / PCQI', role: 'Final approval and authorization' }
  ];

  signatures.forEach(sig => {
    // Signature box
    pdf.setDrawColor(220, 220, 220);
    pdf.setFillColor(255, 255, 255);
    pdf.roundedRect(100, yPos - 5, 80, 20, 2, 2, 'FD');

    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.setTextColor(COLORS.text.dark.r, COLORS.text.dark.g, COLORS.text.dark.b);
    pdf.text(sig.title, 25, yPos);

    pdf.setFontSize(9);
    pdf.setFont('helvetica', 'normal');
    pdf.setTextColor(COLORS.text.medium.r, COLORS.text.medium.g, COLORS.text.medium.b);
    pdf.text(sig.role, 25, yPos + 5);

    // Date line
    pdf.setDrawColor(0);
    pdf.text('Date:', 100, yPos - 10);

    yPos += 25;
  });

  // Return the new Y position
  return yPos + 10;
};

/**
 * Generate an enhanced PDF for a HACCP plan with improved UI
 */
export const generateEnhancedHACCPPlanPDF = (
  planName: string,
  planVersion: string,
  productId: string,
  currentPlan: HACCPPlan | null,
  productDetails: Product | null,
  ccpData: any[],
  hazardData: any[],
  options: PDFExportOptions = defaultOptions
): jsPDF => {
  try {
    console.log('Starting enhanced PDF generation...');

    // Create a new PDF document
    const pdf = new jsPDF();
    const processSteps = getProcessSteps();

    console.log('PDF object created:', pdf);

    // Set up document properties
    pdf.setProperties({
      title: planName,
      subject: 'HACCP Plan',
      author: 'HACCP Plan Pilot',
      keywords: 'HACCP, food safety, plan',
      creator: 'HACCP Plan Pilot'
    });

    // Add cover page if option is enabled
    if (options.includeCoverPage) {
      addCoverPage(
        pdf,
        planName,
        planVersion,
        productDetails?.name || 'Unknown Product',
        currentPlan,
        ccpData,
        options.companyName || 'HACCP Plan Pilot'
      );

      // Add a new page after the cover
      pdf.addPage();
    }

    // Start position for content
    let yPos = 35; // Starting position after header

    // Add header to the first content page
    addEnhancedHeader(
      pdf,
      planName,
      planVersion,
      productDetails?.name || 'Unknown Product',
      currentPlan
    );

    // Add product description section
    if (options.includeProductDescription) {
      yPos = addEnhancedProductDescription(pdf, productDetails, yPos);
      yPos = addSectionDivider(pdf, yPos);
    }

    // Add process flow section
    if (options.includeProcessFlow) {
      // Check if we need a new page
      if (yPos > 180) {
        pdf.addPage();
        addEnhancedHeader(
          pdf,
          planName,
          planVersion,
          productDetails?.name || 'Unknown Product',
          currentPlan,
          'Process Flow Diagram'
        );
        yPos = 35;
      }

      yPos = addEnhancedProcessFlow(pdf, processSteps, ccpData, yPos);

      // Add section divider if not at the end of a page
      if (yPos < pdf.internal.pageSize.getHeight() - 30) {
        yPos = addSectionDivider(pdf, yPos);
      }
    }

    // Add hazard analysis section
    if (options.includeHazardAnalysis) {
      // Always start hazard analysis on a new page in landscape orientation for more width
      pdf.addPage('a4', 'landscape');

      // Add header to the hazard analysis page
      addEnhancedHeader(
        pdf,
        planName,
        planVersion,
        productDetails?.name || 'Unknown Product',
        currentPlan,
        'Hazard Analysis'
      );

      yPos = 35;
      yPos = addEnhancedHazardAnalysis(pdf, hazardData, processSteps, yPos);

      // Add section divider if not at the end of a page
      if (yPos < pdf.internal.pageSize.getHeight() - 30) {
        yPos = addSectionDivider(pdf, yPos);
      }
    }

    // Add CCPs section
    if (options.includeCCPs) {
      // Always start CCPs on a new page in landscape orientation for more width
      pdf.addPage('a4', 'landscape');

      // Add header to the CCPs page
      addEnhancedHeader(
        pdf,
        planName,
        planVersion,
        productDetails?.name || 'Unknown Product',
        currentPlan,
        'Critical Control Points'
      );

      yPos = 35;
      yPos = addEnhancedCCPs(pdf, ccpData, processSteps, yPos);

      // Add section divider if not at the end of a page
      if (yPos < pdf.internal.pageSize.getHeight() - 30) {
        yPos = addSectionDivider(pdf, yPos);
      }
    }

    // Add verification procedures section
    if (options.includeVerification) {
      // Switch back to portrait for verification procedures
      pdf.addPage('a4', 'portrait');

      // Add header to the verification page
      addEnhancedHeader(
        pdf,
        planName,
        planVersion,
        productDetails?.name || 'Unknown Product',
        currentPlan,
        'Verification Procedures'
      );

      yPos = 35;
      yPos = addEnhancedVerificationProcedures(pdf, yPos);

      // Add section divider if not at the end of a page and if approvals will follow
      if (options.includeApprovals && yPos < pdf.internal.pageSize.getHeight() - 30) {
        yPos = addSectionDivider(pdf, yPos);
      }
    }

    // Add approvals section
    if (options.includeApprovals) {
      // Check if we need a new page
      if (yPos > 180 || !options.includeVerification) {
        pdf.addPage('a4', 'portrait');

        // Add header to the approvals page
        addEnhancedHeader(
          pdf,
          planName,
          planVersion,
          productDetails?.name || 'Unknown Product',
          currentPlan,
          'Approvals'
        );

        yPos = 35;
      }

      yPos = addEnhancedApprovals(pdf, currentPlan, yPos);
    }

    // Add footer to all pages
    const pageCount = pdf.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);

      // Skip footer on cover page
      if (options.includeCoverPage && i === 1) {
        continue;
      }

      // Add footer
      addEnhancedFooter(pdf, i, pageCount, planName, planVersion);
    }

    return pdf;
  } catch (error) {
    console.error('Error generating enhanced PDF:', error);

    // Create a simple error PDF to return instead of throwing
    try {
      const errorPdf = new jsPDF();
      errorPdf.setFontSize(16);
      errorPdf.setTextColor(255, 0, 0);
      errorPdf.text('Error Generating HACCP Plan PDF', 105, 20, { align: 'center' });

      errorPdf.setFontSize(12);
      errorPdf.setTextColor(0, 0, 0);
      errorPdf.text('An error occurred while generating the PDF.', 14, 40);
      errorPdf.text('Please check the browser console for details.', 14, 50);

      if (error instanceof Error) {
        errorPdf.text(`Error: ${error.message}`, 14, 70);
      }

      return errorPdf;
    } catch (fallbackError) {
      console.error('Failed to create error PDF:', fallbackError);
      // If even the error PDF fails, create the most basic PDF possible
      const basicPdf = new jsPDF();
      basicPdf.text('PDF Generation Failed', 10, 10);
      return basicPdf;
    }
  }
};
