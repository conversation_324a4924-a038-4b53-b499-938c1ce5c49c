
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, UserRole } from '@/models/types';

interface AuthContextType {
  currentUser: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<User>;
  logout: () => Promise<void>;
  hasPermission: (requiredRole: UserRole) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock user data (in a real app, this would come from your backend)
const MOCK_USERS: User[] = [
  {
    id: 'user1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin'
  },
  {
    id: 'user2',
    name: 'QA Manager',
    email: '<EMAIL>',
    role: 'qa'
  },
  {
    id: 'user3',
    name: 'Food Safety Auditor',
    email: '<EMAIL>',
    role: 'auditor'
  },
  {
    id: 'user4',
    name: 'View-only User',
    email: '<EMAIL>',
    role: 'viewer'
  }
];

// Role hierarchy for permission checks
const ROLE_HIERARCHY: Record<UserRole, number> = {
  'admin': 4,
  'qa': 3,
  'auditor': 2,
  'viewer': 1
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Check for stored user on mount
  useEffect(() => {
    const storedUser = localStorage.getItem('haccp_user');
    if (storedUser) {
      try {
        setCurrentUser(JSON.parse(storedUser));
      } catch (e) {
        console.error('Failed to parse stored user:', e);
      }
    }
    setLoading(false);
  }, []);
  
  // Save user to localStorage when it changes
  useEffect(() => {
    if (currentUser) {
      localStorage.setItem('haccp_user', JSON.stringify(currentUser));
    } else {
      localStorage.removeItem('haccp_user');
    }
  }, [currentUser]);
  
  const login = async (email: string, password: string): Promise<User> => {
    // In a real app, validate credentials with your backend
    const user = MOCK_USERS.find(u => u.email === email);
    
    if (!user) {
      throw new Error('Invalid email or password');
    }
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));
    setCurrentUser(user);
    return user;
  };
  
  const logout = async () => {
    setCurrentUser(null);
  };
  
  // Check if user has sufficient role permissions
  const hasPermission = (requiredRole: UserRole): boolean => {
    if (!currentUser) return false;
    
    const userRoleLevel = ROLE_HIERARCHY[currentUser.role] || 0;
    const requiredRoleLevel = ROLE_HIERARCHY[requiredRole] || 0;
    
    return userRoleLevel >= requiredRoleLevel;
  };
  
  const value = {
    currentUser,
    loading,
    login,
    logout,
    hasPermission
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
