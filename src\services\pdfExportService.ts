// 1. Import jsPDF core
import js<PERSON><PERSON> from 'jspdf';
import { HACCPPlan, Product, ProcessStep } from '@/models/types';
import { getProcessSteps } from './planService';
// 2. Import jspdf-autotable for side effects
import 'jspdf-autotable';

// Add type declaration for jsPDF with autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
    previousAutoTable: {
      finalY: number;
    };
  }
  interface jsPDFAPI { }
}

/**
 * Helper function to truncate text to a maximum length
 * More aggressive truncation for tables
 */
const truncateText = (text: string, maxLength: number = 100): string => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

/**
 * Helper function to create a table with auto width
 * This approach completely avoids the width issues
 */
const createAutoWidthTable = (
  pdf: jsPDF,
  headers: string[],
  data: string[][],
  startY: number,
  options: any = {}
): number => {
  try {
    // Calculate available width based on page orientation
    const pageWidth = pdf.internal.pageSize.getWidth();
    const margin = 10;
    const availableWidth = pageWidth - (margin * 2);

    // Set default options
    const defaultOptions = {
      startY: startY,
      head: [headers],
      body: data,
      headStyles: {
        fillColor: [0, 70, 127],
        textColor: [255, 255, 255],
        fontSize: 6.5,
        cellPadding: 1,
        halign: 'center'
      },
      bodyStyles: {
        fontSize: 6.5,
        cellPadding: 1
      },
      styles: {
        overflow: 'linebreak',
        cellWidth: 'auto',
        minCellWidth: 5,
        halign: 'left',
        valign: 'middle',
        fontSize: 6.5
      },
      margin: { top: margin, right: margin, bottom: margin, left: margin },
      theme: 'grid',
      tableWidth: availableWidth,
      horizontalPageBreak: true,
      horizontalPageBreakRepeat: 0,
      didParseCell: function(data: any) {
        // Reduce font size for cells with long content
        const text = data.cell.text || '';
        if (typeof text === 'string' && text.length > 20) {
          data.cell.styles.fontSize = 6;
        }
        if (typeof text === 'string' && text.length > 40) {
          data.cell.styles.fontSize = 5.5;
        }
      },
      willDrawCell: function(data: any) {
        // Ensure text fits in cell by reducing font size if needed
        if (data.cell.raw && data.cell.raw.length > 0) {
          const cellWidth = data.cell.width;
          const text = data.cell.raw;
          const fontSize = data.cell.styles.fontSize;

          // Approximate width calculation
          const textWidth = text.length * (fontSize * 0.5);

          if (textWidth > cellWidth) {
            // Reduce font size to fit
            const newFontSize = Math.max(5, fontSize * (cellWidth / textWidth));
            data.cell.styles.fontSize = newFontSize;
          }
        }
      }
    };

    // Merge default options with provided options
    const mergedOptions = { ...defaultOptions, ...options };

    // Create the table
    pdf.autoTable(mergedOptions);

    // Return the final Y position
    try {
      return pdf.previousAutoTable.finalY + 5;
    } catch (error) {
      console.error('Error accessing table position:', error);
      return startY + 100; // Provide a reasonable default
    }
  } catch (error) {
    console.error('Error creating auto-width table:', error);
    // Fallback to simple text if autoTable fails
    pdf.setFontSize(10);
    pdf.text('Error creating table. See console for details.', 14, startY + 15);
    return startY + 30;
  }
};

// Interface for PDF export options
interface PDFExportOptions {
  includeProductDescription?: boolean;
  includeProcessFlow?: boolean;
  includeHazardAnalysis?: boolean;
  includeCCPs?: boolean;
  includeVerification?: boolean;
  includeApprovals?: boolean;
}

// Default export options
const defaultOptions: PDFExportOptions = {
  includeProductDescription: true,
  includeProcessFlow: true,
  includeHazardAnalysis: true,
  includeCCPs: true,
  includeVerification: true,
  includeApprovals: true,
};

/**
 * Add header to the PDF
 */
const addHeader = (
  pdf: jsPDF,
  planName: string,
  planVersion: string,
  productName: string,
  currentPlan: HACCPPlan | null
): void => {
  // No logo for now to avoid errors

  // Add title
  pdf.setFontSize(18);
  pdf.setFont('helvetica', 'bold');
  pdf.text('HACCP PLAN', 105, 20, { align: 'center' });

  // Add plan details
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Plan Name: ${planName}`, 48, 30); // Adjust position
  pdf.text(`Version: ${planVersion}`, 48, 37); // Adjust position
  pdf.text(`Product: ${productName}`, 48, 44); // Adjust position
  pdf.text(`Date: ${new Date().toLocaleDateString()}`, 48, 51); // Adjust position

  // Add status
  const status = currentPlan?.status
    ? currentPlan.status.charAt(0).toUpperCase() + currentPlan.status.slice(1).replace('_', ' ')
    : 'Draft';
  pdf.text(`Status: ${status}`, 150, 30); // Adjust position

  // Add horizontal line
  pdf.setDrawColor(200, 200, 200);
  pdf.line(14, 55, 196, 55);
};

/**
 * Add footer to the PDF
 */
const addFooter = (pdf: jsPDF, currentPage: number, pageCount: number): void => {
  pdf.setFontSize(10);
  pdf.setTextColor(50, 50, 50);

  // Get page orientation and dimensions
  let pageOrientation = 'portrait';
  let centerX = 105;
  let bottomY = 285;

  try {
    // Try to determine orientation from page info
    const pageInfo = pdf.getPageInfo(currentPage);
    if (pageInfo && pageInfo.pageContext && pageInfo.pageContext.mediaBox) {
      pageOrientation = pageInfo.pageContext.mediaBox.topRightX >
                        pageInfo.pageContext.mediaBox.topRightY ?
                        'landscape' : 'portrait';

      // Center position depends on orientation
      centerX = pageOrientation === 'landscape' ? 148 : 105;
      bottomY = pageOrientation === 'landscape' ? 200 : 285;
    }
  } catch (error) {
    console.warn('Could not determine page orientation, using portrait defaults', error);
  }

  pdf.text(
    `Page ${currentPage} of ${pageCount}`,
    centerX,
    bottomY,
    { align: 'center' }
  );
  pdf.text(
    `Generated on ${new Date().toLocaleDateString()} by HACCP Plan Pilot`,
    centerX,
    bottomY + 5,
    { align: 'center' }
  );
};

/**
 * Add product description section
 */
const addProductDescription = (
  pdf: jsPDF,
  productDetails: Product | null,
  startY: number
): number => {
  // Section title
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.text('1. Product Description', 14, startY);

  // Product description
  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');

  const description = productDetails?.description || 'No product description available.';

  // Split long text into multiple lines
  const textLines = pdf.splitTextToSize(description, 180);
  pdf.text(textLines, 14, startY + 10);

  // Return the new Y position
  return startY + 10 + (textLines.length * 7);
};

/**
 * Add process flow section with enhanced visual design
 */
const addProcessFlow = (
  pdf: jsPDF,
  processSteps: ProcessStep[],
  ccpData: any[],
  startY: number
): number => {
  // Section title
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.text('2. Process Flow Diagram', 14, startY);

  // Start position for the flow diagram
  let yPos = startY + 10;
  const xCenter = 105;
  const boxWidth = 90;
  const boxHeight = 16;
  const arrowHeight = 12;
  const arrowWidth = 6;

  // Draw process flow boxes
  if (processSteps.length > 0) {
    // Sort steps by order
    const sortedSteps = [...processSteps].sort((a, b) => a.order - b.order);

    sortedSteps.forEach((step, index) => {
      // Determine if this step is a CCP
      const isCCP = ccpData.some(ccp => ccp.hazard.processStepId === step.id);

      // Draw box with enhanced styling
      if (isCCP) {
        pdf.setDrawColor(230, 180, 0);
      } else {
        pdf.setDrawColor(180, 180, 180);
      }
      pdf.setLineWidth(isCCP ? 0.7 : 0.3);

      // Fill color based on CCP status
      if (isCCP) {
        pdf.setFillColor(255, 245, 200); // Yellow for CCPs
      } else {
        pdf.setFillColor(245, 245, 245); // Light gray for regular steps
      }

      // Draw rounded rectangle
      pdf.roundedRect(
        xCenter - (boxWidth / 2),
        yPos,
        boxWidth,
        boxHeight,
        3,
        3,
        'FD'
      );

      // Add step name with enhanced styling
      pdf.setFontSize(isCCP ? 11 : 10);
      pdf.setFont('helvetica', isCCP ? 'bold' : 'normal');
      pdf.setTextColor(0, 0, 0);
      pdf.text(
        step.name,
        xCenter,
        yPos + 8,
        { align: 'center' }
      );

      // Add step number
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(100, 100, 100);
      pdf.text(
        `Step ${step.order}`,
        xCenter,
        yPos + 14,
        { align: 'center' }
      );

      // Add CCP label if applicable
      if (isCCP) {
        // Draw CCP badge
        const badgeWidth = 24;
        const badgeHeight = 10;
        const badgeX = xCenter + (boxWidth / 2) - badgeWidth - 5;
        const badgeY = yPos + 3;

        pdf.setFillColor(230, 180, 0);
        pdf.setDrawColor(200, 150, 0);
        pdf.roundedRect(
          badgeX,
          badgeY,
          badgeWidth,
          badgeHeight,
          2,
          2,
          'FD'
        );

        pdf.setFontSize(7);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(255, 255, 255);
        pdf.text(
          'CCP',
          badgeX + (badgeWidth / 2),
          badgeY + 7,
          { align: 'center' }
        );
      }

      // Add arrow if not the last step
      if (index < sortedSteps.length - 1) {
        yPos += boxHeight + 5;

        // Enhanced arrow styling
        pdf.setDrawColor(120, 120, 120);
        pdf.setLineWidth(0.5);

        // Arrow line
        pdf.line(xCenter, yPos, xCenter, yPos + arrowHeight);

        // Arrow head with enhanced styling
        pdf.setFillColor(120, 120, 120);

        // Draw arrow head as a filled triangle
        pdf.triangle(
          xCenter - arrowWidth, yPos + arrowHeight - arrowWidth,
          xCenter + arrowWidth, yPos + arrowHeight - arrowWidth,
          xCenter, yPos + arrowHeight,
          'F'
        );

        yPos += arrowHeight + 8;
      }
    });
  } else {
    // Default flow if no process steps defined
    const defaultSteps = [
      'Raw Material Reception',
      'Storage',
      'Preparation',
      'Cooking',
      'Cooling',
      'Packaging',
      'Storage & Distribution'
    ];

    defaultSteps.forEach((stepName, index) => {
      // Draw box with standard styling
      pdf.setDrawColor(180, 180, 180);
      pdf.setLineWidth(0.3);
      pdf.setFillColor(245, 245, 245);
      pdf.roundedRect(
        xCenter - (boxWidth / 2),
        yPos,
        boxWidth,
        boxHeight,
        3,
        3,
        'FD'
      );

      // Add step name
      pdf.setFontSize(10);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(0, 0, 0);
      pdf.text(
        stepName,
        xCenter,
        yPos + 8,
        { align: 'center' }
      );

      // Add step number
      pdf.setFontSize(8);
      pdf.setFont('helvetica', 'normal');
      pdf.setTextColor(100, 100, 100);
      pdf.text(
        `Step ${index + 1}`,
        xCenter,
        yPos + 14,
        { align: 'center' }
      );

      // Add arrow if not the last step
      if (index < defaultSteps.length - 1) {
        yPos += boxHeight + 5;

        // Enhanced arrow styling
        pdf.setDrawColor(120, 120, 120);
        pdf.setLineWidth(0.5);

        // Arrow line
        pdf.line(xCenter, yPos, xCenter, yPos + arrowHeight);

        // Arrow head with enhanced styling
        pdf.setFillColor(120, 120, 120);

        // Draw arrow head as a filled triangle
        pdf.triangle(
          xCenter - arrowWidth, yPos + arrowHeight - arrowWidth,
          xCenter + arrowWidth, yPos + arrowHeight - arrowWidth,
          xCenter, yPos + arrowHeight,
          'F'
        );

        yPos += arrowHeight + 8;
      }
    });
  }

  // Return the new Y position
  return yPos + boxHeight + 10;
};

/**
 * Add hazard analysis section
 */
const addHazardAnalysis = (
  pdf: jsPDF,
  hazardData: any[],
  processSteps: ProcessStep[],
  startY: number
): number => {
  // Section title
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.text('3. Hazard Analysis', 14, startY);

  // If no hazards, show message
  if (!hazardData || hazardData.length === 0) {
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'italic');
    pdf.text('No hazards have been identified for this product.', 14, startY + 10);
    return startY + 20;
  }

  // Create table data with more aggressive truncation
  const tableData = hazardData.map(hazard => {
    const step = processSteps.find(s => s.id === hazard.processStepId);
    return [
      truncateText(step?.name || 'Unknown Step', 20),
      truncateText(hazard.description, 30),
      truncateText(hazard.type, 10),
      `${hazard.severity} x ${hazard.likelihood}`,
      truncateText(hazard.controlMeasures, 40),
      hazard.isCCP ? 'Yes' : 'No'
    ];
  });

  // Use our new auto-width table function
  const headers = ['Process Step', 'Hazard', 'Type', 'Risk', 'Control Measures', 'CCP?'];

  // Set column widths proportionally
  const columnWidths = {
    0: { cellWidth: '15%' }, // Process Step
    1: { cellWidth: '25%' }, // Hazard
    2: { cellWidth: '10%' }, // Type
    3: { cellWidth: '10%' }, // Risk
    4: { cellWidth: '30%' }, // Control Measures
    5: { cellWidth: '10%' }  // CCP?
  };

  return createAutoWidthTable(pdf, headers, tableData, startY + 10, {
    columnStyles: columnWidths
  });
};

/**
 * Add CCPs section
 */
const addCCPs = (
  pdf: jsPDF,
  ccpData: any[],
  processSteps: ProcessStep[],
  startY: number
): number => {
  // Section title
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.text('4. Critical Control Points', 14, startY);

  // If no CCPs, show message
  if (!ccpData || ccpData.length === 0) {
    pdf.setFontSize(11);
    pdf.setFont('helvetica', 'italic');
    pdf.text('No Critical Control Points have been defined for this product.', 14, startY + 10);
    return startY + 20;
  }

  // Create table data with more aggressive truncation
  const tableData = ccpData.map(item => {
    const step = processSteps.find(s => s.id === item.hazard.processStepId);
    return [
      truncateText(step?.name || 'Unknown Step', 20),
      truncateText(item.hazard.description, 30),
      truncateText(item.ccp.criticalLimit || 'Not defined', 25),
      truncateText(item.ccp.monitoringProcedure || 'Not defined', 30),
      truncateText(item.ccp.monitoringFrequency || 'Not defined', 20),
      truncateText(item.ccp.correctiveAction || 'Not defined', 30),
      truncateText(item.ccp.verificationProcedure || 'Not defined', 25),
      truncateText(item.ccp.recordkeepingProcedure || 'Not defined', 20)
    ];
  });

  // Use our new auto-width table function
  const headers = [
    'Process Step',
    'Hazard',
    'Critical Limits',
    'Monitoring Procedure',
    'Frequency',
    'Corrective Action',
    'Verification',
    'Records'
  ];

  // Set column widths proportionally
  const columnWidths = {
    0: { cellWidth: '10%' }, // Process Step
    1: { cellWidth: '15%' }, // Hazard
    2: { cellWidth: '12%' }, // Critical Limits
    3: { cellWidth: '15%' }, // Monitoring Procedure
    4: { cellWidth: '10%' }, // Frequency
    5: { cellWidth: '15%' }, // Corrective Action
    6: { cellWidth: '13%' }, // Verification
    7: { cellWidth: '10%' }  // Records
  };

  return createAutoWidthTable(pdf, headers, tableData, startY + 10, {
    columnStyles: columnWidths
  });
};

/**
 * Add verification procedures section
 */
const addVerificationProcedures = (
  pdf: jsPDF,
  startY: number
): number => {
  // Section title
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.text('5. Verification Procedures', 14, startY);

  // Verification procedures
  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');

  const procedures = [
    'Daily calibration of thermometers',
    'Weekly review of monitoring records',
    'Monthly microbiological testing',
    'Annual HACCP system review',
    'Internal audits every 6 months'
  ];

  let yPos = startY + 10;
  procedures.forEach(procedure => {
    pdf.text(`• ${procedure}`, 20, yPos);
    yPos += 7;
  });

  // Return the new Y position
  return yPos + 5;
};

/**
 * Add approvals section
 */
const addApprovals = (
  pdf: jsPDF,
  currentPlan: HACCPPlan | null,
  startY: number
): number => {
  // Section title
  pdf.setFontSize(14);
  pdf.setFont('helvetica', 'bold');
  pdf.text('6. Approvals', 14, startY);

  // Approval information
  pdf.setFontSize(11);
  pdf.setFont('helvetica', 'normal');

  let yPos = startY + 10;

  // Approval status
  const status = currentPlan?.status
    ? currentPlan.status.charAt(0).toUpperCase() + currentPlan.status.slice(1).replace('_', ' ')
    : 'Draft';

  pdf.text(`Plan Status: ${status}`, 14, yPos);
  yPos += 10;

  // Approval date
  if (currentPlan?.approvedDate) {
    pdf.text(`Approved on: ${new Date(currentPlan.approvedDate).toLocaleDateString()}`, 14, yPos);
    yPos += 7;
  }

  // Approved by
  if (currentPlan?.approvedBy) {
    pdf.text(`Approved by: ${currentPlan.approvedBy}`, 14, yPos);
    yPos += 7;
  }

  // Signature boxes
  yPos += 10;
  pdf.text('Required Signatures:', 14, yPos);
  yPos += 10;

  const signatures = [
    { title: 'Quality Manager', role: 'Plan creation and initial approval' },
    { title: 'Food Safety Team', role: 'Technical review and hazard validation' },
    { title: 'Operations Manager', role: 'Implementation feasibility' },
    { title: 'Site Director / PCQI', role: 'Final approval and authorization' }
  ];

  signatures.forEach(sig => {
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'bold');
    pdf.text(sig.title, 20, yPos);

    pdf.setFontSize(9);
    pdf.setFont('helvetica', 'normal');
    pdf.text(sig.role, 20, yPos + 5);

    // Signature line
    pdf.setDrawColor(0);
    pdf.line(100, yPos + 3, 180, yPos + 3);

    // Date line
    pdf.text('Date:', 100, yPos - 2);

    yPos += 15;
  });

  // Return the new Y position
  return yPos;
};

/**
 * Generate a PDF for a HACCP plan
 */
export const generateHACCPPlanPDF = (
  planName: string,
  planVersion: string,
  _productId: string, // Underscore prefix indicates unused parameter
  currentPlan: HACCPPlan | null,
  productDetails: Product | null,
  ccpData: any[],
  hazardData: any[],
  options: PDFExportOptions = defaultOptions
): jsPDF => {
  try {
    console.log('Starting PDF generation...');

    // Create a new PDF document
    const pdf = new jsPDF();
    const processSteps = getProcessSteps();

    console.log('PDF object created:', pdf);

    // Set up document properties
    pdf.setProperties({
      title: planName,
      subject: 'HACCP Plan',
      author: 'HACCP Plan Pilot',
      keywords: 'HACCP, food safety, plan',
      creator: 'HACCP Plan Pilot'
    });

    // Add header
    addHeader(pdf, planName, planVersion, productDetails?.name || 'Unknown Product', currentPlan);

    let yPos = 60; // Starting position after header

    // Add product description section
    if (options.includeProductDescription) {
      yPos = addProductDescription(pdf, productDetails, yPos);
    }

    // Add process flow section
    if (options.includeProcessFlow) {
      // Check if we need a new page
      if (yPos > 230) {
        pdf.addPage();
        yPos = 20;
      }
      yPos = addProcessFlow(pdf, processSteps, ccpData, yPos);
    }

    // Add hazard analysis section
    if (options.includeHazardAnalysis) {
      // Always start hazard analysis on a new page in landscape orientation for more width
      pdf.addPage('a4', 'landscape');

      // Add section title at the top of the page
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(0, 0, 0);
      pdf.text('HACCP Plan - Hazard Analysis', 148, 10, { align: 'center' });

      yPos = 20;
      yPos = addHazardAnalysis(pdf, hazardData, processSteps, yPos);

      // Switch back to portrait for subsequent pages if CCPs are not included
      if (!options.includeCCPs && (options.includeVerification || options.includeApprovals)) {
        pdf.addPage('a4', 'portrait');
        yPos = 20;
      }
    }

    // Add CCPs section
    if (options.includeCCPs) {
      // Always start CCPs on a new page in landscape orientation for more width
      pdf.addPage('a4', 'landscape');

      // Add section title at the top of the page
      pdf.setFontSize(12);
      pdf.setFont('helvetica', 'bold');
      pdf.setTextColor(0, 0, 0);
      pdf.text('HACCP Plan - Critical Control Points', 148, 10, { align: 'center' });

      yPos = 20;
      yPos = addCCPs(pdf, ccpData, processSteps, yPos);

      // Switch back to portrait for subsequent pages
      if (options.includeVerification || options.includeApprovals) {
        pdf.addPage('a4', 'portrait');
        yPos = 20;
      }
    }

    // Add verification procedures section
    if (options.includeVerification) {
      // Check if we need a new page
      if (yPos > 200) {
        pdf.addPage();

        // Add section title at the top of the page
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(0, 0, 0);
        pdf.text('HACCP Plan - Verification Procedures', 105, 10, { align: 'center' });

        yPos = 20;
      }
      yPos = addVerificationProcedures(pdf, yPos);
    }

    // Add approvals section
    if (options.includeApprovals) {
      // Check if we need a new page
      if (yPos > 200) {
        pdf.addPage();

        // Add section title at the top of the page
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'bold');
        pdf.setTextColor(0, 0, 0);
        pdf.text('HACCP Plan - Approvals', 105, 10, { align: 'center' });

        yPos = 20;
      }
      yPos = addApprovals(pdf, currentPlan, yPos);
    }

    // Add header and footer to all pages
    const pageCount = pdf.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);

      // We now have specific section titles on each page, so no need for generic headers

      // Add footer
      addFooter(pdf, i, pageCount);
    }

    return pdf;
  } catch (error) {
    console.error('Error generating PDF:', error);

    // Create a simple error PDF to return instead of throwing
    try {
      const errorPdf = new jsPDF();
      errorPdf.setFontSize(16);
      errorPdf.setTextColor(255, 0, 0);
      errorPdf.text('Error Generating HACCP Plan PDF', 105, 20, { align: 'center' });

      errorPdf.setFontSize(12);
      errorPdf.setTextColor(0, 0, 0);
      errorPdf.text('An error occurred while generating the PDF.', 14, 40);
      errorPdf.text('Please check the browser console for details.', 14, 50);

      if (error instanceof Error) {
        errorPdf.text(`Error: ${error.message}`, 14, 70);
      }

      return errorPdf;
    } catch (fallbackError) {
      console.error('Failed to create error PDF:', fallbackError);
      // If even the error PDF fails, create the most basic PDF possible
      const basicPdf = new jsPDF();
      basicPdf.text('PDF Generation Failed', 10, 10);
      return basicPdf;
    }
  }
};
