import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DocumentFormData } from '@/models/document';

interface ApprovalStepProps {
  form: UseFormReturn<DocumentFormData>;
}

const ApprovalStep: React.FC<ApprovalStepProps> = ({ form }) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Approval & Revision Details</h3>
        <p className="text-sm text-muted-foreground mb-6">
          Document validation information and revision history. All fields in this section are optional.
        </p>
      </div>

      {/* Approval Information */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-primary">Validation Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="approvalDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Validation Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="approvedByFunction"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Validated By (Function)</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Directeur qualité" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Revision Information */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-primary">Revision Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="revisionDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Revision Date</FormLabel>
                <FormControl>
                  <Input type="date" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="previousReferenceNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Previous Reference Number</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., PR.ND.01" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="revisionReason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Revision Reason</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Describe the reason for this revision..."
                  className="min-h-[80px]"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>

      {/* Distribution Information */}
      <div className="space-y-4">
        <h4 className="text-md font-medium text-primary">Distribution Information</h4>
        <FormField
          control={form.control}
          name="distributionMethod"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Distribution Method</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Email et affichage" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
};

export default ApprovalStep;
