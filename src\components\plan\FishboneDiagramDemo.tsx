import React, { useState } from 'react';
import { FishboneDiagram } from './FishboneDiagram';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface FishboneCategory {
  name: string;
  causes: string[];
  position: 'top' | 'bottom';
  angle: number;
}

export const FishboneDiagramDemo: React.FC = () => {
  const [selectedExample, setSelectedExample] = useState<'food-defense' | 'haccp' | 'quality'>('food-defense');

  // Food Defense example (from your image)
  const foodDefenseCategories: FishboneCategory[] = [
    {
      name: 'Stocks',
      position: 'top',
      angle: -45,
      causes: [
        'Sûreté des stocks',
        'Gestion des stocks',
        'Inventaires'
      ]
    },
    {
      name: 'Flux',
      position: 'top',
      angle: -30,
      causes: [
        'Véhicules',
        'Produits chimiques et biologiques dangereux',
        'Personnes',
        'Marchandises',
        'Eau',
        'Laboratoires d\'analyses chimiques / bio'
      ]
    },
    {
      name: '<PERSON>cc<PERSON>',
      position: 'top',
      angle: -15,
      causes: [
        'Prévention et détection d\'intrusion dans les installations',
        'Accès aux stocks',
        'Protection physique des accès aux bâtiments, installations et lieux de stockage',
        'Protection physique périphérique'
      ]
    },
    {
      name: 'Suppléments spécifiques',
      position: 'bottom',
      angle: 45,
      causes: [
        'Collecte',
        'Restauration',
        'Importateurs',
        'Transport / logistique',
        'Sécurité informatique'
      ]
    },
    {
      name: 'Process',
      position: 'bottom',
      angle: 30,
      causes: [
        'Fournisseurs agréés',
        'Vêtements et locaux',
        'Mise en œuvre',
        'Intégrité des produits',
        'Quarantaine des produits suspects'
      ]
    },
    {
      name: 'Personnel',
      position: 'bottom',
      angle: 15,
      causes: [
        'Accueil des salariés',
        'Vêtements et locaux',
        'Formation',
        'Recrutement des salariés et collaborateurs internes',
        'Comportements hors normes',
        'Règlement intérieur'
      ]
    }
  ];

  // HACCP example
  const haccpCategories: FishboneCategory[] = [
    {
      name: 'People',
      position: 'top',
      angle: -45,
      causes: [
        'Inadequate training',
        'Poor hygiene practices',
        'Lack of supervision',
        'High staff turnover'
      ]
    },
    {
      name: 'Methods',
      position: 'top',
      angle: -30,
      causes: [
        'Inadequate procedures',
        'Poor documentation',
        'Lack of standardization',
        'Insufficient monitoring'
      ]
    },
    {
      name: 'Machines',
      position: 'top',
      angle: -15,
      causes: [
        'Equipment malfunction',
        'Poor maintenance',
        'Calibration issues',
        'Design flaws'
      ]
    },
    {
      name: 'Materials',
      position: 'bottom',
      angle: 45,
      causes: [
        'Contaminated raw materials',
        'Poor supplier quality',
        'Inadequate storage',
        'Cross-contamination'
      ]
    },
    {
      name: 'Measurement',
      position: 'bottom',
      angle: 30,
      causes: [
        'Inaccurate instruments',
        'Poor calibration',
        'Human error',
        'Inadequate monitoring frequency'
      ]
    },
    {
      name: 'Environment',
      position: 'bottom',
      angle: 15,
      causes: [
        'Poor facility design',
        'Inadequate ventilation',
        'Temperature fluctuations',
        'Pest control issues'
      ]
    }
  ];

  // Quality Issues example
  const qualityCategories: FishboneCategory[] = [
    {
      name: 'Raw Materials',
      position: 'top',
      angle: -40,
      causes: [
        'Supplier quality issues',
        'Contamination',
        'Specification deviations',
        'Storage problems'
      ]
    },
    {
      name: 'Process',
      position: 'top',
      angle: -20,
      causes: [
        'Temperature control',
        'Time deviations',
        'Equipment settings',
        'Process variations'
      ]
    },
    {
      name: 'Packaging',
      position: 'bottom',
      angle: 40,
      causes: [
        'Material defects',
        'Seal integrity',
        'Labeling errors',
        'Storage conditions'
      ]
    },
    {
      name: 'Testing',
      position: 'bottom',
      angle: 20,
      causes: [
        'Sampling errors',
        'Test method issues',
        'Equipment problems',
        'Human error'
      ]
    }
  ];

  const getCategories = () => {
    switch (selectedExample) {
      case 'food-defense':
        return foodDefenseCategories;
      case 'haccp':
        return haccpCategories;
      case 'quality':
        return qualityCategories;
      default:
        return foodDefenseCategories;
    }
  };

  const getTitle = () => {
    switch (selectedExample) {
      case 'food-defense':
        return 'Figure 1. Domaines de prévention en matière de Food Defense';
      case 'haccp':
        return 'HACCP Hazard Analysis - Root Cause Analysis';
      case 'quality':
        return 'Quality Issue Analysis - Product Defects';
      default:
        return 'Fishbone Diagram Analysis';
    }
  };

  const getProblemStatement = () => {
    switch (selectedExample) {
      case 'food-defense':
        return 'Protection de la chaîne alimentaire contre les risques d\'actions malveillantes, criminelles ou terroristes';
      case 'haccp':
        return 'Food Safety Hazard Occurrence';
      case 'quality':
        return 'Product Quality Defects';
      default:
        return 'Problem Statement';
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Fishbone Diagram Examples</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedExample} onValueChange={(value) => setSelectedExample(value as any)}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="food-defense">Food Defense</TabsTrigger>
              <TabsTrigger value="haccp">HACCP Analysis</TabsTrigger>
              <TabsTrigger value="quality">Quality Issues</TabsTrigger>
            </TabsList>

            <TabsContent value={selectedExample} className="mt-4">
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  <strong>Problem Statement:</strong> {getProblemStatement()}
                </div>

                <div className="h-[700px] border rounded-lg bg-white">
                  <FishboneDiagram
                    processSteps={[]}
                    hazards={[]}
                    ccpData={[]}
                    title={getTitle()}
                    customCategories={getCategories()}
                    problemStatement={getProblemStatement()}
                  />
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};
