import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { CalendarIcon, CheckCircle, ClipboardList, FileText, PenLine, Plus, Search, Trash2 } from 'lucide-react';
import { PrerequisiteProgram, PrerequisiteCategory, PrerequisiteStatus, PREREQUISITE_CATEGORIES, MOCK_PREREQUISITE_PROGRAMS } from '@/models/prerequisite';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useLocalStorage } from '@/hooks/use-local-storage';

export default function PrerequisitePrograms() {
  const [activeTab, setActiveTab] = useState<PrerequisiteCategory | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [prerequisites, setPrerequisites] = useLocalStorage<PrerequisiteProgram[]>('haccp_prerequisites', MOCK_PREREQUISITE_PROGRAMS);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedPrerequisite, setSelectedPrerequisite] = useState<PrerequisiteProgram | null>(null);
  const [statusFilter, setStatusFilter] = useState<PrerequisiteStatus | 'all'>('all');
  const [sortOption, setSortOption] = useState<string>('name_asc');

  // Form state
  const [formValues, setFormValues] = useState<Partial<PrerequisiteProgram>>({
    category: undefined,
    name: '',
    description: '',
    status: 'planned',
    responsiblePerson: '',
    documentReference: '',
    lastReviewDate: new Date(),
    nextReviewDate: new Date(),
    verificationActivities: ''
  });

  // Filter prerequisites based on active tab, status filter, and search query
  const filteredPrerequisites = prerequisites.filter(prp => {
    const matchesTab = activeTab === 'all' || prp.category === activeTab;
    const matchesStatus = statusFilter === 'all' || prp.status === statusFilter;
    const matchesSearch =
      prp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prp.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prp.responsiblePerson.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesStatus && matchesSearch;
  });

  // Sort prerequisites based on selected sort option
  const sortedPrerequisites = [...filteredPrerequisites].sort((a, b) => {
    switch (sortOption) {
      case 'name_asc':
        return a.name.localeCompare(b.name);
      case 'name_desc':
        return b.name.localeCompare(a.name);
      case 'review_date':
        return new Date(a.nextReviewDate).getTime() - new Date(b.nextReviewDate).getTime();
      case 'status':
        return a.status.localeCompare(b.status);
      default:
        return 0;
    }
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    if (date) {
      setFormValues(prev => ({ ...prev, [name]: date }));
    }
  };

  // Open add dialog
  const openAddDialog = () => {
    setFormValues({
      category: undefined,
      name: '',
      description: '',
      status: 'planned',
      responsiblePerson: '',
      documentReference: '',
      lastReviewDate: new Date(),
      nextReviewDate: new Date(),
      verificationActivities: ''
    });
    setIsAddDialogOpen(true);
  };

  // Open edit dialog
  const openEditDialog = (prerequisite: PrerequisiteProgram) => {
    setSelectedPrerequisite(prerequisite);
    setFormValues({
      category: prerequisite.category,
      name: prerequisite.name,
      description: prerequisite.description,
      status: prerequisite.status,
      responsiblePerson: prerequisite.responsiblePerson,
      documentReference: prerequisite.documentReference,
      lastReviewDate: prerequisite.lastReviewDate,
      nextReviewDate: prerequisite.nextReviewDate,
      verificationActivities: prerequisite.verificationActivities
    });
    setIsEditDialogOpen(true);
  };

  // Handle form submission for adding a new prerequisite
  const handleAddPrerequisite = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.category || !formValues.name || !formValues.description) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    const newPrerequisite: PrerequisiteProgram = {
      id: `prp${prerequisites.length + 1}`,
      category: formValues.category as PrerequisiteCategory,
      name: formValues.name,
      description: formValues.description,
      status: formValues.status as PrerequisiteStatus,
      responsiblePerson: formValues.responsiblePerson || '',
      documentReference: formValues.documentReference || '',
      lastReviewDate: formValues.lastReviewDate || new Date(),
      nextReviewDate: formValues.nextReviewDate || new Date(),
      verificationActivities: formValues.verificationActivities || '',
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };

    setPrerequisites([...prerequisites, newPrerequisite]);
    setIsAddDialogOpen(false);

    toast({
      title: 'Success',
      description: 'Prerequisite program added successfully'
    });
  };

  // Handle form submission for editing a prerequisite
  const handleEditPrerequisite = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedPrerequisite) return;

    if (!formValues.category || !formValues.name || !formValues.description) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    const updatedPrerequisites = prerequisites.map(prp =>
      prp.id === selectedPrerequisite.id
        ? {
            ...prp,
            category: formValues.category as PrerequisiteCategory,
            name: formValues.name,
            description: formValues.description,
            status: formValues.status as PrerequisiteStatus,
            responsiblePerson: formValues.responsiblePerson || '',
            documentReference: formValues.documentReference || '',
            lastReviewDate: formValues.lastReviewDate || new Date(),
            nextReviewDate: formValues.nextReviewDate || new Date(),
            verificationActivities: formValues.verificationActivities || '',
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : prp
    );

    setPrerequisites(updatedPrerequisites);
    setIsEditDialogOpen(false);

    toast({
      title: 'Success',
      description: 'Prerequisite program updated successfully'
    });
  };

  // Delete a prerequisite
  const handleDeletePrerequisite = (id: string) => {
    const updatedPrerequisites = prerequisites.filter(prp => prp.id !== id);
    setPrerequisites(updatedPrerequisites);

    toast({
      title: 'Success',
      description: 'Prerequisite program deleted successfully'
    });
  };

  // Render status badge
  const renderStatusBadge = (status: PrerequisiteStatus) => {
    switch (status) {
      case 'implemented':
        return <Badge className="bg-green-500">Implemented</Badge>;
      case 'in_progress':
        return <Badge className="bg-blue-500">In Progress</Badge>;
      case 'planned':
        return <Badge className="bg-yellow-500">Planned</Badge>;
      case 'not_applicable':
        return <Badge className="bg-gray-500">Not Applicable</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Prerequisite Programs</h1>
          <p className="text-muted-foreground mt-1">Manage your food safety prerequisite programs</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <Button variant="outline" size="sm" className="gap-1">
            <FileText className="h-4 w-4" />
            <span>Export</span>
          </Button>
          <Button onClick={openAddDialog}>
            <Plus className="h-4 w-4 mr-2" />
            Add Prerequisite
          </Button>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <div className="flex items-center space-x-2 w-full sm:max-w-sm">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search prerequisites..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-2 ml-auto">
          <Select
            value={statusFilter}
            onValueChange={(value) => setStatusFilter(value as PrerequisiteStatus | 'all')}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="implemented">Implemented</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="planned">Planned</SelectItem>
              <SelectItem value="not_applicable">Not Applicable</SelectItem>
            </SelectContent>
          </Select>
          <Select
            value={sortOption}
            onValueChange={setSortOption}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name_asc">Name (A-Z)</SelectItem>
              <SelectItem value="name_desc">Name (Z-A)</SelectItem>
              <SelectItem value="review_date">Next Review Date</SelectItem>
              <SelectItem value="status">Status</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => setActiveTab(value as PrerequisiteCategory | 'all')}>
        <div className="mb-4 border-b sticky top-0 bg-background z-10 pb-2">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-medium">Categories</h2>
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={() => setActiveTab('all')}>
                Reset Filters
              </Button>
            </div>
          </div>
          <TabsList className="mb-2 flex flex-wrap h-auto p-2 bg-muted/50">
            <TabsTrigger value="all" className="flex items-center gap-2 h-9">
              <span>All Programs</span>
              <Badge variant="secondary" className="ml-1">{prerequisites.length}</Badge>
            </TabsTrigger>
            {Object.entries(PREREQUISITE_CATEGORIES).map(([key, { title }]) => {
              const count = prerequisites.filter(p => p.category === key).length;
              return (
                <TabsTrigger key={key} value={key} className="flex items-center gap-2 h-9">
                  <span>{title}</span>
                  {count > 0 && <Badge variant="secondary" className="ml-1">{count}</Badge>}
                </TabsTrigger>
              );
            })}
          </TabsList>
        </div>

        <TabsContent value={activeTab} className="mt-0">
          {sortedPrerequisites.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 px-4">
              <div className="bg-muted/30 p-6 rounded-full mb-6">
                <ClipboardList className="h-16 w-16 text-primary/60" />
              </div>
              <h3 className="text-xl font-semibold mb-2">No Prerequisite Programs Found</h3>
              <p className="text-center text-muted-foreground max-w-md mb-6">
                {searchQuery
                  ? 'No programs match your search criteria. Try a different search term or clear your filters.'
                  : activeTab !== 'all'
                    ? <>No programs found for <span className="font-medium">{PREREQUISITE_CATEGORIES[activeTab as PrerequisiteCategory].title}</span>. Add one to get started or select a different category.</>
                    : 'No prerequisite programs have been added yet. Prerequisite programs are foundational to your food safety management system.'}
              </p>

              {activeTab !== 'all' && (
                <div className="mb-4 p-4 bg-muted/30 rounded-lg max-w-md">
                  <h4 className="font-medium mb-2">About {PREREQUISITE_CATEGORIES[activeTab as PrerequisiteCategory].title}</h4>
                  <p className="text-sm text-muted-foreground">
                    {PREREQUISITE_CATEGORIES[activeTab as PrerequisiteCategory].description}
                  </p>
                </div>
              )}

              <div className="flex gap-3">
                {(searchQuery || activeTab !== 'all') && (
                  <Button variant="outline" onClick={() => {
                    setSearchQuery('');
                    setActiveTab('all');
                  }}>
                    Clear Filters
                  </Button>
                )}
                <Button onClick={openAddDialog}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Prerequisite Program
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {sortedPrerequisites.map((prerequisite) => {
                // Calculate if review is upcoming (within 30 days)
                const isReviewUpcoming = new Date(prerequisite.nextReviewDate).getTime() - new Date().getTime() < 30 * 24 * 60 * 60 * 1000;

                return (
                  <Card
                    key={prerequisite.id}
                    className="group border overflow-hidden transition-all duration-200 hover:shadow-md hover:border-primary/20"
                  >
                    <div className={`h-1 w-full ${
                      prerequisite.status === 'implemented' ? 'bg-green-500' :
                      prerequisite.status === 'in_progress' ? 'bg-blue-500' :
                      prerequisite.status === 'planned' ? 'bg-yellow-500' :
                      'bg-gray-500'
                    }`}></div>
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg group-hover:text-primary transition-colors">{prerequisite.name}</CardTitle>
                          <CardDescription className="flex items-center gap-2">
                            {PREREQUISITE_CATEGORIES[prerequisite.category].title}
                            {renderStatusBadge(prerequisite.status)}
                          </CardDescription>
                        </div>
                        <div className="flex flex-col items-end gap-1">
                          {isReviewUpcoming && (
                            <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                              Review Soon
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="pb-2">
                      <p className="text-sm text-muted-foreground mb-4 line-clamp-2">{prerequisite.description}</p>
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <span className="font-medium">Responsible</span>
                          </div>
                          <div>{prerequisite.responsiblePerson}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <span className="font-medium">Document</span>
                          </div>
                          <div>{prerequisite.documentReference}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <span className="font-medium">Last Review</span>
                          </div>
                          <div>{format(prerequisite.lastReviewDate, 'MMM d, yyyy')}</div>
                        </div>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2 text-muted-foreground">
                            <span className="font-medium">Next Review</span>
                          </div>
                          <div className={isReviewUpcoming ? 'text-amber-600 font-medium' : ''}>
                            {format(prerequisite.nextReviewDate, 'MMM d, yyyy')}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="flex justify-end gap-2 bg-muted/10 border-t">
                      <Button variant="ghost" size="sm" onClick={() => openEditDialog(prerequisite)}>
                        <PenLine className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive hover:bg-destructive/10" onClick={() => handleDeletePrerequisite(prerequisite.id)}>
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                    </CardFooter>
                  </Card>
                );
              })}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Add Prerequisite Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl" maxHeight="70vh">
          <DialogHeader>
            <DialogTitle>Add Prerequisite Program</DialogTitle>
            <DialogDescription>
              Create a new prerequisite program for your food safety management system.
            </DialogDescription>
          </DialogHeader>

            <form onSubmit={handleAddPrerequisite} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formValues.category}
                  onValueChange={(value) => handleSelectChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(PREREQUISITE_CATEGORIES).map(([key, { title }]) => (
                      <SelectItem key={key} value={key}>{title}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formValues.status}
                  onValueChange={(value) => handleSelectChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="implemented">Implemented</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="planned">Planned</SelectItem>
                    <SelectItem value="not_applicable">Not Applicable</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">Program Name *</Label>
              <Input
                id="name"
                name="name"
                value={formValues.name}
                onChange={handleInputChange}
                placeholder="e.g., Master Cleaning Schedule"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues.description}
                onChange={handleInputChange}
                placeholder="Describe the prerequisite program..."
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="responsiblePerson">Responsible Person</Label>
                <Input
                  id="responsiblePerson"
                  name="responsiblePerson"
                  value={formValues.responsiblePerson}
                  onChange={handleInputChange}
                  placeholder="e.g., QA Manager"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="documentReference">Document Reference</Label>
                <Input
                  id="documentReference"
                  name="documentReference"
                  value={formValues.documentReference}
                  onChange={handleInputChange}
                  placeholder="e.g., SOP-CL-001"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Last Review Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.lastReviewDate ? format(formValues.lastReviewDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.lastReviewDate}
                      onSelect={(date) => handleDateChange('lastReviewDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Next Review Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.nextReviewDate ? format(formValues.nextReviewDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.nextReviewDate}
                      onSelect={(date) => handleDateChange('nextReviewDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="verificationActivities">Verification Activities</Label>
              <Textarea
                id="verificationActivities"
                name="verificationActivities"
                value={formValues.verificationActivities}
                onChange={handleInputChange}
                placeholder="Describe how this program is verified..."
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Add Prerequisite
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Prerequisite Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl" maxHeight="70vh">
          <DialogHeader>
            <DialogTitle>Edit Prerequisite Program</DialogTitle>
            <DialogDescription>
              Update the details of this prerequisite program.
            </DialogDescription>
          </DialogHeader>

            <form onSubmit={handleEditPrerequisite} className="space-y-4">
            {/* Same form fields as Add Dialog */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select
                  value={formValues.category}
                  onValueChange={(value) => handleSelectChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(PREREQUISITE_CATEGORIES).map(([key, { title }]) => (
                      <SelectItem key={key} value={key}>{title}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formValues.status}
                  onValueChange={(value) => handleSelectChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="implemented">Implemented</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="planned">Planned</SelectItem>
                    <SelectItem value="not_applicable">Not Applicable</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">Program Name *</Label>
              <Input
                id="name"
                name="name"
                value={formValues.name}
                onChange={handleInputChange}
                placeholder="e.g., Master Cleaning Schedule"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues.description}
                onChange={handleInputChange}
                placeholder="Describe the prerequisite program..."
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="responsiblePerson">Responsible Person</Label>
                <Input
                  id="responsiblePerson"
                  name="responsiblePerson"
                  value={formValues.responsiblePerson}
                  onChange={handleInputChange}
                  placeholder="e.g., QA Manager"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="documentReference">Document Reference</Label>
                <Input
                  id="documentReference"
                  name="documentReference"
                  value={formValues.documentReference}
                  onChange={handleInputChange}
                  placeholder="e.g., SOP-CL-001"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Last Review Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.lastReviewDate ? format(formValues.lastReviewDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.lastReviewDate}
                      onSelect={(date) => handleDateChange('lastReviewDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Next Review Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.nextReviewDate ? format(formValues.nextReviewDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.nextReviewDate}
                      onSelect={(date) => handleDateChange('nextReviewDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="verificationActivities">Verification Activities</Label>
              <Textarea
                id="verificationActivities"
                name="verificationActivities"
                value={formValues.verificationActivities}
                onChange={handleInputChange}
                placeholder="Describe how this program is verified..."
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Prerequisite
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
