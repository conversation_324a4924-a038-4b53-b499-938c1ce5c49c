import React, { useMemo } from 'react';
import { useDocuments } from '../context/DocumentContext';
import { Document } from '../types';
import DocumentRow from './DocumentRow';
import './DocumentTable.css';

const DocumentTable: React.FC = () => {
  const { 
    documents, 
    searchFilters, 
    sortConfig, 
    setSortConfig 
  } = useDocuments();

  // Filter and sort documents
  const filteredAndSortedDocuments = useMemo(() => {
    let filtered = documents.filter(doc => {
      const matchesSearch = !searchFilters.searchTerm ||
        doc.designation.toLowerCase().includes(searchFilters.searchTerm.toLowerCase()) ||
        doc.referenceId.toLowerCase().includes(searchFilters.searchTerm.toLowerCase());

      const matchesType = !searchFilters.typeFilter || doc.type === searchFilters.typeFilter;
      const matchesStatus = !searchFilters.statusFilter || doc.status === searchFilters.statusFilter;

      return matchesSearch && matchesType && matchesStatus;
    });

    // Apply sorting
    if (sortConfig.column) {
      filtered.sort((a, b) => {
        const aVal = a[sortConfig.column!];
        const bVal = b[sortConfig.column!];

        // Handle date sorting
        if (sortConfig.column === 'effectiveDate') {
          const aDate = new Date(aVal as string);
          const bDate = new Date(bVal as string);
          return sortConfig.direction === 'asc' 
            ? aDate.getTime() - bDate.getTime()
            : bDate.getTime() - aDate.getTime();
        }

        // Handle string sorting
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          const aStr = aVal.toLowerCase();
          const bStr = bVal.toLowerCase();
          
          if (aStr < bStr) return sortConfig.direction === 'asc' ? -1 : 1;
          if (aStr > bStr) return sortConfig.direction === 'asc' ? 1 : -1;
          return 0;
        }

        return 0;
      });
    }

    return filtered;
  }, [documents, searchFilters, sortConfig]);

  const handleSort = (column: keyof Document) => {
    const direction = 
      sortConfig.column === column && sortConfig.direction === 'asc' 
        ? 'desc' 
        : 'asc';
    
    setSortConfig({ column, direction });
  };

  const getSortIcon = (column: keyof Document) => {
    if (sortConfig.column !== column) {
      return 'fas fa-sort';
    }
    return sortConfig.direction === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
  };

  return (
    <section className="section table-section">
      <div className="section-header">
        <h2>
          <i className="fas fa-table"></i>
          Document List
        </h2>
        <div className="table-controls">
          <span className="document-count">
            {filteredAndSortedDocuments.length === documents.length
              ? `${documents.length} document${documents.length !== 1 ? 's' : ''}`
              : `${filteredAndSortedDocuments.length} of ${documents.length} document${documents.length !== 1 ? 's' : ''}`
            }
          </span>
        </div>
      </div>

      <div className="table-container">
        {filteredAndSortedDocuments.length === 0 ? (
          <div className="empty-state">
            <i className="fas fa-inbox"></i>
            <h3>No documents found</h3>
            <p>
              {documents.length === 0 
                ? 'Start by adding your first document.'
                : 'No documents match your search criteria. Try adjusting your filters.'
              }
            </p>
          </div>
        ) : (
          <table role="table">
            <thead>
              <tr>
                <th 
                  role="columnheader" 
                  tabIndex={0} 
                  className="sortable" 
                  onClick={() => handleSort('type')}
                >
                  <i className="fas fa-tag"></i> Type
                  <i className={`sort-icon ${getSortIcon('type')}`}></i>
                </th>
                <th 
                  role="columnheader" 
                  tabIndex={0} 
                  className="sortable" 
                  onClick={() => handleSort('designation')}
                >
                  <i className="fas fa-file-signature"></i> Designation
                  <i className={`sort-icon ${getSortIcon('designation')}`}></i>
                </th>
                <th 
                  role="columnheader" 
                  tabIndex={0} 
                  className="sortable" 
                  onClick={() => handleSort('referenceId')}
                >
                  <i className="fas fa-hashtag"></i> Reference ID
                  <i className={`sort-icon ${getSortIcon('referenceId')}`}></i>
                </th>
                <th 
                  role="columnheader" 
                  tabIndex={0} 
                  className="sortable" 
                  onClick={() => handleSort('format')}
                >
                  <i className="fas fa-file-code"></i> Format
                  <i className={`sort-icon ${getSortIcon('format')}`}></i>
                </th>
                <th 
                  role="columnheader" 
                  tabIndex={0} 
                  className="sortable" 
                  onClick={() => handleSort('effectiveDate')}
                >
                  <i className="fas fa-calendar-alt"></i> Effective Date
                  <i className={`sort-icon ${getSortIcon('effectiveDate')}`}></i>
                </th>
                <th 
                  role="columnheader" 
                  tabIndex={0} 
                  className="sortable" 
                  onClick={() => handleSort('version')}
                >
                  <i className="fas fa-code-branch"></i> Version
                  <i className={`sort-icon ${getSortIcon('version')}`}></i>
                </th>
                <th 
                  role="columnheader" 
                  tabIndex={0} 
                  className="sortable" 
                  onClick={() => handleSort('status')}
                >
                  <i className="fas fa-info-circle"></i> Status
                  <i className={`sort-icon ${getSortIcon('status')}`}></i>
                </th>
                <th role="columnheader">
                  <i className="fas fa-paperclip"></i> Attachment
                </th>
                <th role="columnheader">
                  <i className="fas fa-cogs"></i> Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredAndSortedDocuments.map(document => (
                <DocumentRow key={document.id} document={document} />
              ))}
            </tbody>
          </table>
        )}
      </div>
    </section>
  );
};

export default DocumentTable;
