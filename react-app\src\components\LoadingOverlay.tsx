import React from 'react';
import { useDocuments } from '../context/DocumentContext';

const LoadingOverlay: React.FC = () => {
  const { isLoading } = useDocuments();

  if (!isLoading) {
    return null;
  }

  return (
    <div className="loading-overlay">
      <div className="loading-spinner">
        <i className="fas fa-spinner fa-spin"></i>
        <p>Processing...</p>
      </div>
    </div>
  );
};

export default LoadingOverlay;
