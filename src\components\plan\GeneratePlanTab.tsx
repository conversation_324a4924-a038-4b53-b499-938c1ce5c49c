
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ArrowRight, Check, FilePlus, AlertCircle, Info } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { getProducts, getHazardsForProduct, getCCPsForProduct, validatePlan } from '@/services/planService';
import { Product } from '@/models/types';

interface GeneratePlanTabProps {
  planName: string;
  setPlanName: (value: string) => void;
  planVersion: string;
  setPlanVersion: (value: string) => void;
  selectedProduct: string;
  setSelectedProduct: (value: string) => void;
  handleGeneratePlan: () => void;
}

export const GeneratePlanTab: React.FC<GeneratePlanTabProps> = ({
  planName,
  setPlanName,
  planVersion,
  setPlanVersion,
  selectedProduct,
  setSelectedProduct,
  handleGeneratePlan,
}) => {
  const [products, setProducts] = useState<Product[]>([]);
  const [hazardCount, setHazardCount] = useState(0);
  const [ccpCount, setCcpCount] = useState(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [selectedProductDetails, setSelectedProductDetails] = useState<Product | null>(null);

  // Load products on component mount
  useEffect(() => {
    const loadedProducts = getProducts();
    setProducts(loadedProducts);

    // If a product is already selected, load its details
    if (selectedProduct) {
      const productDetails = loadedProducts.find(p => p.id === selectedProduct);
      setSelectedProductDetails(productDetails || null);

      // Load hazards and CCPs for the selected product
      const hazards = getHazardsForProduct(selectedProduct);
      setHazardCount(hazards.length);

      const ccps = getCCPsForProduct(selectedProduct);
      setCcpCount(ccps.length);
    }
  }, [selectedProduct]);

  // Handle product selection change
  const handleProductChange = (productId: string) => {
    setSelectedProduct(productId);

    // Find the selected product details
    const productDetails = products.find(p => p.id === productId);
    setSelectedProductDetails(productDetails || null);

    // Load hazards and CCPs for the selected product
    const hazards = getHazardsForProduct(productId);
    setHazardCount(hazards.length);

    const ccps = getCCPsForProduct(productId);
    setCcpCount(ccps.length);

    // Update plan name based on product
    if (productDetails) {
      setPlanName(`${productDetails.name} HACCP Plan`);
    }
  };

  // Validate the plan before generation
  const validateAndGeneratePlan = () => {
    const { isValid, errors } = validatePlan({
      name: planName,
      version: planVersion,
      productId: selectedProduct,
    });

    setValidationErrors(errors);

    if (isValid) {
      handleGeneratePlan();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Generate HACCP Plan</CardTitle>
        <CardDescription>
          Create a comprehensive HACCP plan based on your hazard analysis and critical control points
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Validation Errors</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 mt-2">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="planName">Plan Name *</Label>
            <Input
              id="planName"
              value={planName}
              onChange={(e) => setPlanName(e.target.value)}
              placeholder="e.g., Chicken Soup HACCP Plan"
              required
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="planVersion">Version *</Label>
            <Input
              id="planVersion"
              value={planVersion}
              onChange={(e) => setPlanVersion(e.target.value)}
              placeholder="e.g., 1.0"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="product">Product *</Label>
          <Select value={selectedProduct} onValueChange={handleProductChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a product" />
            </SelectTrigger>
            <SelectContent>
              {products.map((product) => (
                <SelectItem key={product.id} value={product.id}>
                  {product.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {selectedProductDetails && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>{selectedProductDetails.name}</AlertTitle>
            <AlertDescription>
              {selectedProductDetails.description}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <Label>Plan Components</Label>
          <div className="space-y-2">
            <div className="flex items-center p-3 rounded-md bg-green-50 border border-green-100">
              <Check className="h-5 w-5 text-green-600 mr-3" />
              <div className="flex-1">
                <p className="font-medium">Product Description &amp; Intended Use</p>
              </div>
              <span className="text-sm text-muted-foreground">Complete</span>
            </div>
            <div className="flex items-center p-3 rounded-md bg-green-50 border border-green-100">
              <Check className="h-5 w-5 text-green-600 mr-3" />
              <div className="flex-1">
                <p className="font-medium">Process Flow Diagram</p>
              </div>
              <span className="text-sm text-muted-foreground">7 steps defined</span>
            </div>
            <div className={`flex items-center p-3 rounded-md ${hazardCount > 0 ? 'bg-green-50 border border-green-100' : 'bg-yellow-50 border border-yellow-100'}`}>
              {hazardCount > 0 ? (
                <Check className="h-5 w-5 text-green-600 mr-3" />
              ) : (
                <AlertCircle className="h-5 w-5 text-yellow-600 mr-3" />
              )}
              <div className="flex-1">
                <p className="font-medium">Hazard Analysis</p>
              </div>
              <span className="text-sm text-muted-foreground">
                {hazardCount > 0 ? `${hazardCount} hazards identified` : 'No hazards identified'}
              </span>
            </div>
            <div className={`flex items-center p-3 rounded-md ${ccpCount > 0 ? 'bg-green-50 border border-green-100' : 'bg-yellow-50 border border-yellow-100'}`}>
              {ccpCount > 0 ? (
                <Check className="h-5 w-5 text-green-600 mr-3" />
              ) : (
                <AlertCircle className="h-5 w-5 text-yellow-600 mr-3" />
              )}
              <div className="flex-1">
                <p className="font-medium">Critical Control Points</p>
              </div>
              <span className="text-sm text-muted-foreground">
                {ccpCount > 0 ? `${ccpCount} CCPs defined` : 'No CCPs defined'}
              </span>
            </div>
            <div className="flex items-center p-3 rounded-md bg-yellow-50 border border-yellow-100">
              <ArrowRight className="h-5 w-5 text-yellow-600 mr-3" />
              <div className="flex-1">
                <p className="font-medium">Verification Procedures</p>
              </div>
              <span className="text-sm text-muted-foreground">Partially complete</span>
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button onClick={validateAndGeneratePlan}>
            <FilePlus className="h-4 w-4 mr-2" />
            Generate Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
