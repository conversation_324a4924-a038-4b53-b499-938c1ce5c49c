
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Check, FileText, Lock, AlertCircle, AlertTriangle, Calendar } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { HACCPPlan, Product } from '@/models/types';
import { getProducts } from '@/services/planService';

interface ApprovePlanTabProps {
  planName: string;
  planVersion: string;
  selectedProduct: string;
  currentPlan: HACCPPlan | null;
  ccpData: any[];
  canApprove: boolean;
  setActiveTab: (tab: string) => void;
  handleApprovePlan: () => void;
}

export const ApprovePlanTab: React.FC<ApprovePlanTabProps> = ({
  planName,
  planVersion,
  selectedProduct,
  currentPlan,
  ccpData,
  canApprove,
  setActiveTab,
  handleApprovePlan
}) => {
  const [approvalNotes, setApprovalNotes] = useState('');
  const [approvalDate, setApprovalDate] = useState(new Date().toISOString().split('T')[0]);
  const [reviewDate, setReviewDate] = useState(new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString().split('T')[0]);
  const [productDetails, setProductDetails] = useState<Product | null>(null);
  const [validationWarnings, setValidationWarnings] = useState<string[]>([]);

  // Load product details and validate plan on component mount
  React.useEffect(() => {
    // Get product details
    const products = getProducts();
    const product = products.find(p => p.id === selectedProduct);
    setProductDetails(product || null);

    // Validate plan
    const warnings: string[] = [];

    if (ccpData.length === 0) {
      warnings.push('No Critical Control Points (CCPs) have been defined. A valid HACCP plan requires at least one CCP.');
    }

    // Check for incomplete CCPs
    const incompleteCCPs = ccpData.filter(item =>
      !item.ccp.criticalLimit ||
      !item.ccp.monitoringProcedure ||
      !item.ccp.correctiveAction
    );

    if (incompleteCCPs.length > 0) {
      warnings.push(`${incompleteCCPs.length} CCP(s) have incomplete information (missing critical limits, monitoring procedures, or corrective actions).`);
    }

    setValidationWarnings(warnings);
  }, [selectedProduct, ccpData]);

  // Get plan status
  const getPlanStatus = () => {
    if (!currentPlan) return 'Draft';
    return currentPlan.status.charAt(0).toUpperCase() + currentPlan.status.slice(1).replace('_', ' ');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Approve &amp; Finalize HACCP Plan</CardTitle>
        <CardDescription>
          Review and approve the HACCP plan to make it official and ready for implementation
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {validationWarnings.length > 0 && (
          <Alert variant="warning">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Plan Validation Warnings</AlertTitle>
            <AlertDescription>
              <ul className="list-disc pl-5 mt-2">
                {validationWarnings.map((warning, index) => (
                  <li key={index}>{warning}</li>
                ))}
              </ul>
              <p className="mt-2">
                It is recommended to address these issues before approving the plan.
              </p>
            </AlertDescription>
          </Alert>
        )}

        <div className="flex items-center p-4 bg-muted/30 rounded-md border border-dashed">
          <div className="mr-4">
            <FileText className="h-12 w-12 text-primary" />
          </div>
          <div>
            <h3 className="font-medium">{planName}</h3>
            <p className="text-sm text-muted-foreground">
              Version {planVersion} • Created on {new Date().toLocaleDateString()} •
              Status: <span className="font-medium">{getPlanStatus()}</span>
            </p>
            <p className="text-sm text-muted-foreground mt-1">
              Product: {productDetails?.name || 'Unknown Product'}
            </p>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="approval_date">Approval Date</Label>
            <div className="flex">
              <Calendar className="h-4 w-4 mr-2 mt-3 text-muted-foreground" />
              <Input
                id="approval_date"
                type="date"
                value={approvalDate}
                onChange={(e) => setApprovalDate(e.target.value)}
                disabled={!canApprove}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="review_date">Next Review Date</Label>
            <div className="flex">
              <Calendar className="h-4 w-4 mr-2 mt-3 text-muted-foreground" />
              <Input
                id="review_date"
                type="date"
                value={reviewDate}
                onChange={(e) => setReviewDate(e.target.value)}
                disabled={!canApprove}
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="approval_notes">Approval Notes</Label>
          <Textarea
            id="approval_notes"
            placeholder="Add any notes for approval (optional)"
            value={approvalNotes}
            onChange={(e) => setApprovalNotes(e.target.value)}
            disabled={!canApprove}
            className="min-h-[100px]"
          />
        </div>

        <div className="space-y-2">
          <Label>Required Approvals</Label>
          <div className="space-y-2">
            <div className="flex items-center p-3 rounded-md border">
              <div className="flex-1">
                <p className="font-medium">Quality Manager</p>
                <p className="text-sm text-muted-foreground">Plan creation and initial approval</p>
              </div>
              <div className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">
                Approved
              </div>
            </div>
            <div className="flex items-center p-3 rounded-md border">
              <div className="flex-1">
                <p className="font-medium">Food Safety Team</p>
                <p className="text-sm text-muted-foreground">Technical review and hazard validation</p>
              </div>
              <div className="bg-green-100 text-green-800 text-xs px-3 py-1 rounded-full">
                Approved
              </div>
            </div>
            <div className="flex items-center p-3 rounded-md border">
              <div className="flex-1">
                <p className="font-medium">Operations Manager</p>
                <p className="text-sm text-muted-foreground">Implementation feasibility</p>
              </div>
              <div className="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">
                Pending
              </div>
            </div>
            <div className="flex items-center p-3 rounded-md border">
              <div className="flex-1">
                <p className="font-medium">Site Director / PCQI</p>
                <p className="text-sm text-muted-foreground">Final approval and authorization</p>
              </div>
              {canApprove ? (
                <div className="bg-yellow-100 text-yellow-800 text-xs px-3 py-1 rounded-full">
                  Awaiting Approval
                </div>
              ) : (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Lock className="h-3 w-3" />
                  <span>Admin only</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setActiveTab('preview')}>
            Back to Preview
          </Button>
          <Button
            onClick={handleApprovePlan}
            disabled={!canApprove}
          >
            {canApprove ? (
              <>
                <Check className="h-4 w-4 mr-2" />
                Approve &amp; Finalize
              </>
            ) : (
              <>
                <Lock className="h-4 w-4 mr-2" />
                Requires Admin Approval
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
