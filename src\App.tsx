
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import FixDialogIssues from "@/components/ui/fix-dialog-issues";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import Index from "./pages/Index";
import Login from "./pages/Login";
import Dashboard from "./pages/Dashboard";
import HazardAnalysis from "./pages/HazardAnalysis";
import CCPManagement from "./pages/CCPManagement";
import PlanGenerator from "./pages/PlanGenerator";
import Settings from "./pages/Settings";
import NotFound from "./pages/NotFound";
import PrerequisitePrograms from "./pages/PrerequisitePrograms";
import FoodSafetyCulture from "./pages/FoodSafetyCulture";
import AllergenManagement from "./pages/AllergenManagement";
import CAPA from "./pages/CAPA";
import DocumentManagement from "./pages/DocumentManagement";

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const storedUser = localStorage.getItem('haccp_user');
  if (!storedUser) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <ThemeProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <FixDialogIssues />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<Login />} />

              {/* Protected routes with dashboard layout */}
              <Route element={<ProtectedRoute><DashboardLayout /></ProtectedRoute>}>
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/prerequisite-programs" element={<PrerequisitePrograms />} />
                <Route path="/food-safety-culture" element={<FoodSafetyCulture />} />
                <Route path="/allergen-management" element={<AllergenManagement />} />
                <Route path="/capa" element={<CAPA />} />
                <Route path="/document-management" element={<DocumentManagement />} />
                <Route path="/hazard-analysis" element={<HazardAnalysis />} />
                <Route path="/ccp-management" element={<CCPManagement />} />
                <Route path="/plan-generator" element={<PlanGenerator />} />
                <Route path="/settings" element={<Settings />} />
              </Route>

              {/* Catch-all route */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </ThemeProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
