export interface Document {
  id: string;
  type: DocumentType;
  designation: string;
  referenceId: string;
  format: DocumentFormat;
  effectiveDate: string;
  version: string;
  status: DocumentStatus;
  fileName?: string;
  fileData?: string;
  createdAt: string;
  updatedAt: string;
}

export type DocumentType = 
  | 'Processus'
  | 'Procédure'
  | 'Enregistrement'
  | 'Manuel'
  | 'Logiciel';

export type DocumentFormat = 
  | 'Word'
  | 'Excel'
  | 'PPT'
  | 'PDF'
  | 'Other';

export type DocumentStatus = 
  | 'RAS'
  | 'Modifier'
  | 'Créer'
  | 'Annuler';

export interface DocumentFormData {
  type: DocumentType | '';
  designation: string;
  referenceId: string;
  format: DocumentFormat | '';
  effectiveDate: string;
  version: string;
  status: DocumentStatus | '';
}

export interface SearchFilters {
  searchTerm: string;
  typeFilter: DocumentType | '';
  statusFilter: DocumentStatus | '';
}

export interface SortConfig {
  column: keyof Document | null;
  direction: 'asc' | 'desc';
}

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface Notification {
  id: string;
  type: NotificationType;
  message: string;
  duration?: number;
}

export interface AppState {
  documents: Document[];
  currentEditId: string | null;
  searchFilters: SearchFilters;
  sortConfig: SortConfig;
  isLoading: boolean;
  hasSeenWelcome: boolean;
}
