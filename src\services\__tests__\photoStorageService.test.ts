import * as photoService from '../photoStorageService';
import { PhotoFile } from '@/components/ui/photo-upload';

// Mock File constructor for testing
class MockFile extends File {
  constructor(bits: BlobPart[], name: string, options?: FilePropertyBag) {
    super(bits, name, options);
  }
}

// Helper to create mock PhotoFile
const createMockPhotoFile = (
  name: string = 'test.jpg',
  size: number = 1024 * 1024, // 1MB
  type: string = 'image/jpeg'
): PhotoFile => {
  const file = new MockFile(['test content'], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  
  return {
    id: photoService.generatePhotoId(),
    file,
    url: 'blob:test-url',
    name,
    size,
    uploading: false,
    uploadProgress: 0
  };
};

describe('PhotoStorageService', () => {
  beforeEach(() => {
    // Clear all photos before each test
    photoService.clearAllPhotos();
  });

  describe('Configuration Management', () => {
    it('should update service configuration', () => {
      const newConfig = {
        maxFileSize: 10 * 1024 * 1024, // 10MB
        maxFiles: 20
      };

      photoService.updateServiceConfig(newConfig);
      const config = photoService.getServiceConfig();

      expect(config.maxFileSize).toBe(newConfig.maxFileSize);
      expect(config.maxFiles).toBe(newConfig.maxFiles);
    });

    it('should maintain default values for unspecified config', () => {
      photoService.updateServiceConfig({ maxFiles: 15 });
      const config = photoService.getServiceConfig();

      expect(config.maxFiles).toBe(15);
      expect(config.enableCompression).toBe(true); // Default value
    });
  });

  describe('File Validation', () => {
    it('should validate correct file types', () => {
      const file = new MockFile(['test'], 'test.jpg', { type: 'image/jpeg' });
      const result = photoService.validatePhotoFile(file);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject invalid file types', () => {
      const file = new MockFile(['test'], 'test.txt', { type: 'text/plain' });
      const result = photoService.validatePhotoFile(file);

      expect(result.isValid).toBe(false);
      expect(result.error?.type).toBe(photoService.PhotoErrorType.INVALID_FILE_TYPE);
    });

    it('should reject files that are too large', () => {
      const file = new MockFile(['test'], 'large.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 10 * 1024 * 1024 }); // 10MB

      const result = photoService.validatePhotoFile(file);

      expect(result.isValid).toBe(false);
      expect(result.error?.type).toBe(photoService.PhotoErrorType.FILE_TOO_LARGE);
    });

    it('should provide warnings for edge cases', () => {
      const file = new MockFile(['test'], 'small.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 500 }); // Very small file

      const result = photoService.validatePhotoFile(file);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('File size is very small, image quality may be poor');
    });
  });

  describe('Photo Upload', () => {
    it('should upload photo successfully', async () => {
      const photoFile = createMockPhotoFile();
      const claimId = 'test-claim-1';

      const storedPhoto = await photoService.uploadPhoto(photoFile, claimId);

      expect(storedPhoto.id).toBe(photoFile.id);
      expect(storedPhoto.claimId).toBe(claimId);
      expect(storedPhoto.metadata).toBeDefined();
      expect(storedPhoto.checksum).toBeDefined();
    });

    it('should retry failed uploads', async () => {
      const photoFile = createMockPhotoFile();
      
      // Mock Math.random to force initial failures then success
      const originalRandom = Math.random;
      let callCount = 0;
      Math.random = jest.fn(() => {
        callCount++;
        return callCount <= 2 ? 0.01 : 0.1; // Fail first 2 attempts, succeed on 3rd
      });

      try {
        const storedPhoto = await photoService.uploadPhoto(photoFile);
        expect(storedPhoto).toBeDefined();
      } finally {
        Math.random = originalRandom;
      }
    });

    it('should handle batch upload', async () => {
      const photoFiles = [
        createMockPhotoFile('photo1.jpg'),
        createMockPhotoFile('photo2.jpg'),
        createMockPhotoFile('photo3.jpg')
      ];
      const claimId = 'test-claim-batch';

      const storedPhotos = await photoService.uploadPhotos(photoFiles, claimId);

      expect(storedPhotos).toHaveLength(3);
      expect(photoService.getPhotoCountForClaim(claimId)).toBe(3);
    });
  });

  describe('Photo Retrieval', () => {
    it('should retrieve photos for specific claim', async () => {
      const claimId = 'test-claim-retrieve';
      const photoFile = createMockPhotoFile();

      await photoService.uploadPhoto(photoFile, claimId);
      const photos = photoService.getPhotosForClaim(claimId);

      expect(photos).toHaveLength(1);
      expect(photos[0].claimId).toBe(claimId);
    });

    it('should return empty array for claim with no photos', () => {
      const photos = photoService.getPhotosForClaim('non-existent-claim');
      expect(photos).toHaveLength(0);
    });

    it('should get photo count efficiently', async () => {
      const claimId = 'test-claim-count';
      const photoFiles = [
        createMockPhotoFile('photo1.jpg'),
        createMockPhotoFile('photo2.jpg')
      ];

      await photoService.uploadPhotos(photoFiles, claimId);
      const count = photoService.getPhotoCountForClaim(claimId);

      expect(count).toBe(2);
    });
  });

  describe('Photo Deletion', () => {
    it('should delete photo successfully', async () => {
      const photoFile = createMockPhotoFile();
      const claimId = 'test-claim-delete';

      const storedPhoto = await photoService.uploadPhoto(photoFile, claimId);
      const success = await photoService.deletePhoto(storedPhoto.id);

      expect(success).toBe(true);
      expect(photoService.getPhoto(storedPhoto.id)).toBeUndefined();
      expect(photoService.getPhotoCountForClaim(claimId)).toBe(0);
    });

    it('should delete all photos for a claim', async () => {
      const claimId = 'test-claim-delete-all';
      const photoFiles = [
        createMockPhotoFile('photo1.jpg'),
        createMockPhotoFile('photo2.jpg'),
        createMockPhotoFile('photo3.jpg')
      ];

      await photoService.uploadPhotos(photoFiles, claimId);
      const deletedCount = await photoService.deletePhotosForClaim(claimId);

      expect(deletedCount).toBe(3);
      expect(photoService.getPhotoCountForClaim(claimId)).toBe(0);
    });
  });

  describe('Statistics and Health', () => {
    it('should provide accurate statistics', async () => {
      const photoFiles = [
        createMockPhotoFile('photo1.jpg', 1024, 'image/jpeg'),
        createMockPhotoFile('photo2.png', 2048, 'image/png')
      ];

      await photoService.uploadPhotos(photoFiles, 'test-claim-stats');
      const stats = photoService.getPhotoStatistics();

      expect(stats.totalPhotos).toBe(2);
      expect(stats.totalSize).toBe(3072);
      expect(stats.typeDistribution['image/jpeg']).toBe(1);
      expect(stats.typeDistribution['image/png']).toBe(1);
    });

    it('should provide service health information', async () => {
      const health = photoService.getServiceHealth();

      expect(health.status).toBeDefined();
      expect(health.memoryUsage).toBeDefined();
      expect(health.storage).toBeDefined();
      expect(health.config).toBeDefined();
    });
  });

  describe('Utility Functions', () => {
    it('should format file sizes correctly', () => {
      expect(photoService.formatFileSize(0)).toBe('0 Bytes');
      expect(photoService.formatFileSize(1024)).toBe('1 KB');
      expect(photoService.formatFileSize(1024 * 1024)).toBe('1 MB');
      expect(photoService.formatFileSize(1536)).toBe('1.5 KB');
    });

    it('should generate unique photo IDs', () => {
      const id1 = photoService.generatePhotoId();
      const id2 = photoService.generatePhotoId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^photo_\d+_[a-z0-9]+$/);
    });

    it('should generate consistent checksums', () => {
      const input = 'test-input';
      const checksum1 = photoService.generateChecksum(input);
      const checksum2 = photoService.generateChecksum(input);

      expect(checksum1).toBe(checksum2);
      expect(checksum1).toMatch(/^[a-f0-9]+$/);
    });
  });

  describe('Memory Management', () => {
    it('should track and cleanup URLs', () => {
      const url = 'blob:test-url';
      
      // Mock URL.revokeObjectURL
      const mockRevoke = jest.fn();
      global.URL.revokeObjectURL = mockRevoke;

      photoService.cleanupPhotoUrl(url);
      expect(mockRevoke).toHaveBeenCalledWith(url);
    });

    it('should clear all storage properly', () => {
      const mockRevoke = jest.fn();
      global.URL.revokeObjectURL = mockRevoke;

      // Add some test data
      const photoFile = createMockPhotoFile();
      photoService.uploadPhoto(photoFile, 'test-claim');

      photoService.clearAllPhotos();
      
      const stats = photoService.getPhotoStatistics();
      expect(stats.totalPhotos).toBe(0);
      expect(stats.claimsWithPhotos).toBe(0);
    });
  });
});
