import React from 'react';
import toast from 'react-hot-toast';
import { useDocuments } from '../context/DocumentContext';
import { Document } from '../types';
import './ActionButtons.css';

interface ActionButtonsProps {
  document: Document;
  onView: () => void;
  onEdit: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ document, onView, onEdit }) => {
  const { deleteDocument, setLoading } = useDocuments();

  const handleDelete = async () => {
    const confirmMessage = `Are you sure you want to delete this document?\n\nDocument: ${document.designation}\nReference: ${document.referenceId}\n\nThis action cannot be undone.`;

    if (window.confirm(confirmMessage)) {
      try {
        setLoading(true);
        deleteDocument(document.id);
        toast.success('Document deleted successfully');
      } catch (error) {
        toast.error('Error deleting document. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="action-buttons">
      <button 
        className="action-btn view-btn" 
        onClick={onView}
        title="View document"
        type="button"
      >
        <i className="fas fa-eye"></i>
      </button>
      <button 
        className="action-btn edit-btn" 
        onClick={onEdit}
        title="Edit document"
        type="button"
      >
        <i className="fas fa-edit"></i>
      </button>
      <button 
        className="action-btn delete-btn" 
        onClick={handleDelete}
        title="Delete document"
        type="button"
      >
        <i className="fas fa-trash"></i>
      </button>
    </div>
  );
};

export default ActionButtons;
