import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { ProcessStepCard } from './ProcessStepCard';
import { ProcessStep, Hazard } from '@/models/types';
import { GripVertical } from 'lucide-react';
import { cn } from '@/lib/utils';

interface SortableProcessStepProps {
  step: ProcessStep;
  isCCP: boolean;
  hasHazards: boolean;
  hazards?: Hazard[];
  onClick?: () => void;
  disabled?: boolean;
}

export const SortableProcessStep: React.FC<SortableProcessStepProps> = ({
  step,
  isCCP,
  hasHazards,
  hazards = [],
  onClick,
  disabled = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: step.id,
    disabled,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'relative flex items-center',
        isDragging ? 'z-10' : '',
        disabled ? '' : 'hover:cursor-grab active:cursor-grabbing'
      )}
    >
      {!disabled && (
        <div
          className="absolute left-0 top-1/2 -translate-y-1/2 -ml-6 opacity-50 hover:opacity-100 cursor-grab"
          {...attributes}
          {...listeners}
        >
          <GripVertical className="h-5 w-5 text-muted-foreground" />
        </div>
      )}
      <ProcessStepCard
        step={step}
        isCCP={isCCP}
        hasHazards={hasHazards}
        hazards={hazards}
        isDragging={isDragging}
        onClick={onClick}
      />
    </div>
  );
};

export default SortableProcessStep;
