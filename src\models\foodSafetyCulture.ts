// Food Safety Culture Types for FSSC 22000 v6.0 compliance

export type FoodSafetyCultureStatus = 'implemented' | 'in_progress' | 'planned' | 'not_implemented';

export type FoodSafetyCultureCategory = 
  | 'communication' 
  | 'training' 
  | 'feedback' 
  | 'performance_measurement'
  | 'leadership'
  | 'resources'
  | 'awareness';

export interface FoodSafetyCultureElement {
  id: string;
  category: FoodSafetyCultureCategory;
  name: string;
  description: string;
  status: FoodSafetyCultureStatus;
  responsiblePerson: string;
  implementationDate?: Date;
  lastAssessmentDate?: Date;
  nextAssessmentDate?: Date;
  evidenceOfImplementation: string;
  improvementActions: string;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Category information with descriptions
export const FOOD_SAFETY_CULTURE_CATEGORIES: Record<FoodSafetyCultureCategory, { title: string; description: string }> = {
  communication: {
    title: 'Communication',
    description: 'Open, clear, and ongoing dialogue about food safety practices and expectations'
  },
  training: {
    title: 'Training',
    description: 'Comprehensive and continuous food safety training and education programs'
  },
  feedback: {
    title: 'Employee Feedback',
    description: 'Mechanisms for staff to voice concerns and contribute ideas'
  },
  performance_measurement: {
    title: 'Performance Measurement',
    description: 'Metrics to assess and improve food safety activities'
  },
  leadership: {
    title: 'Leadership Commitment',
    description: 'Visible and active support from top management for food safety'
  },
  resources: {
    title: 'Resource Allocation',
    description: 'Provision of adequate resources for food safety activities'
  },
  awareness: {
    title: 'Food Safety Awareness',
    description: 'Programs to maintain high awareness of food safety importance'
  }
};

// Mock data for food safety culture elements
export const MOCK_FOOD_SAFETY_CULTURE_ELEMENTS: FoodSafetyCultureElement[] = [
  {
    id: 'fsc1',
    category: 'communication',
    name: 'Food Safety Communication Plan',
    description: 'Regular communication of food safety objectives, performance, and expectations to all employees',
    status: 'implemented',
    responsiblePerson: 'Food Safety Team Leader',
    implementationDate: new Date('2023-06-15'),
    lastAssessmentDate: new Date('2024-01-10'),
    nextAssessmentDate: new Date('2024-07-10'),
    evidenceOfImplementation: 'Monthly food safety newsletters, daily huddle meetings, digital signage in production areas',
    improvementActions: 'Implement QR codes linking to food safety resources, translate materials into additional languages',
    createdBy: 'user1',
    createdAt: new Date('2023-05-01'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-01-10')
  },
  {
    id: 'fsc2',
    category: 'training',
    name: 'Food Safety Training Program',
    description: 'Comprehensive training program covering all aspects of food safety for all employees',
    status: 'implemented',
    responsiblePerson: 'Training Manager',
    implementationDate: new Date('2023-03-20'),
    lastAssessmentDate: new Date('2023-12-15'),
    nextAssessmentDate: new Date('2024-06-15'),
    evidenceOfImplementation: 'Training records, competency assessments, refresher training schedule',
    improvementActions: 'Develop advanced training modules for supervisors, implement gamification elements',
    createdBy: 'user1',
    createdAt: new Date('2023-02-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-12-15')
  },
  {
    id: 'fsc3',
    category: 'feedback',
    name: 'Food Safety Suggestion System',
    description: 'System for employees to submit food safety concerns and improvement suggestions',
    status: 'in_progress',
    responsiblePerson: 'QA Manager',
    implementationDate: new Date('2023-11-01'),
    lastAssessmentDate: undefined,
    nextAssessmentDate: new Date('2024-05-01'),
    evidenceOfImplementation: 'Suggestion boxes, digital submission form, monthly review meetings',
    improvementActions: 'Implement recognition program for valuable suggestions, improve feedback response time',
    createdBy: 'user1',
    createdAt: new Date('2023-09-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-11-01')
  }
];
