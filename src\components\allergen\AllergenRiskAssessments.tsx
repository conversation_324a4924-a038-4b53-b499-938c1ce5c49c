import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from '@/hooks/use-toast';
import { CalendarIcon, CheckCircle, FileText, Plus, Search, Trash2, PenLine } from 'lucide-react';
import { AllergenRiskAssessment, MOCK_ALLERGEN_RISK_ASSESSMENTS } from '@/models/allergen';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface Product {
  id: string;
  name: string;
  description: string;
}

interface AllergenRiskAssessmentsProps {
  riskAssessments?: AllergenRiskAssessment[];
  setRiskAssessments?: React.Dispatch<React.SetStateAction<AllergenRiskAssessment[]>>;
  products: Product[];
}

export function AllergenRiskAssessments({ 
  riskAssessments = MOCK_ALLERGEN_RISK_ASSESSMENTS, 
  setRiskAssessments,
  products
}: AllergenRiskAssessmentsProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedAssessment, setSelectedAssessment] = useState<AllergenRiskAssessment | null>(null);
  
  // Form state
  const [formValues, setFormValues] = useState<Partial<AllergenRiskAssessment>>({
    productId: '',
    assessmentDate: new Date(),
    nextReviewDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
    conductedBy: '',
    approvedBy: '',
    crossContactRisks: '',
    controlMeasureEffectiveness: '',
    correctiveActions: ''
  });
  
  // Filter risk assessments based on search query
  const filteredAssessments = riskAssessments.filter(assessment => {
    const product = products.find(p => p.id === assessment.productId);
    return (
      (product && product.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      assessment.conductedBy.toLowerCase().includes(searchQuery.toLowerCase()) ||
      assessment.approvedBy.toLowerCase().includes(searchQuery.toLowerCase())
    );
  });
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    if (date) {
      setFormValues(prev => ({ ...prev, [name]: date }));
    }
  };
  
  // Open add dialog
  const openAddDialog = () => {
    setFormValues({
      productId: products[0]?.id || '',
      assessmentDate: new Date(),
      nextReviewDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      conductedBy: '',
      approvedBy: '',
      crossContactRisks: '',
      controlMeasureEffectiveness: '',
      correctiveActions: ''
    });
    setIsAddDialogOpen(true);
  };
  
  // Open edit dialog
  const openEditDialog = (assessment: AllergenRiskAssessment) => {
    setSelectedAssessment(assessment);
    setFormValues({
      productId: assessment.productId,
      assessmentDate: assessment.assessmentDate,
      nextReviewDate: assessment.nextReviewDate,
      conductedBy: assessment.conductedBy,
      approvedBy: assessment.approvedBy,
      crossContactRisks: assessment.crossContactRisks,
      controlMeasureEffectiveness: assessment.controlMeasureEffectiveness,
      correctiveActions: assessment.correctiveActions
    });
    setIsEditDialogOpen(true);
  };
  
  // Open view dialog
  const openViewDialog = (assessment: AllergenRiskAssessment) => {
    setSelectedAssessment(assessment);
    setIsViewDialogOpen(true);
  };
  
  // Handle form submission for adding a new risk assessment
  const handleAddAssessment = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formValues.productId || !formValues.conductedBy || !formValues.crossContactRisks) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }
    
    const newAssessment: AllergenRiskAssessment = {
      id: `ara${riskAssessments.length + 1}`,
      productId: formValues.productId,
      assessmentDate: formValues.assessmentDate || new Date(),
      nextReviewDate: formValues.nextReviewDate || new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
      conductedBy: formValues.conductedBy,
      approvedBy: formValues.approvedBy || '',
      crossContactRisks: formValues.crossContactRisks,
      controlMeasureEffectiveness: formValues.controlMeasureEffectiveness || '',
      correctiveActions: formValues.correctiveActions || '',
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };
    
    if (setRiskAssessments) {
      setRiskAssessments([...riskAssessments, newAssessment]);
    }
    
    setIsAddDialogOpen(false);
    
    toast({
      title: 'Success',
      description: 'Risk assessment added successfully'
    });
  };
  
  // Handle form submission for editing a risk assessment
  const handleEditAssessment = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedAssessment) return;
    
    if (!formValues.productId || !formValues.conductedBy || !formValues.crossContactRisks) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }
    
    if (setRiskAssessments) {
      const updatedAssessments = riskAssessments.map(assessment => 
        assessment.id === selectedAssessment.id 
          ? {
              ...assessment,
              productId: formValues.productId!,
              assessmentDate: formValues.assessmentDate || new Date(),
              nextReviewDate: formValues.nextReviewDate || new Date(new Date().setFullYear(new Date().getFullYear() + 1)),
              conductedBy: formValues.conductedBy!,
              approvedBy: formValues.approvedBy || '',
              crossContactRisks: formValues.crossContactRisks!,
              controlMeasureEffectiveness: formValues.controlMeasureEffectiveness || '',
              correctiveActions: formValues.correctiveActions || '',
              updatedBy: 'user1',
              updatedAt: new Date()
            }
          : assessment
      );
      
      setRiskAssessments(updatedAssessments);
    }
    
    setIsEditDialogOpen(false);
    
    toast({
      title: 'Success',
      description: 'Risk assessment updated successfully'
    });
  };
  
  // Delete a risk assessment
  const handleDeleteAssessment = (id: string) => {
    if (setRiskAssessments) {
      const updatedAssessments = riskAssessments.filter(assessment => assessment.id !== id);
      setRiskAssessments(updatedAssessments);
      
      toast({
        title: 'Success',
        description: 'Risk assessment deleted successfully'
      });
    }
  };
  
  // Get product name by ID
  const getProductName = (productId: string) => {
    const product = products.find(p => p.id === productId);
    return product ? product.name : 'Unknown Product';
  };
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Allergen Risk Assessments</CardTitle>
              <CardDescription>
                Manage allergen risk assessments for your products
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Risk Assessment
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search risk assessments..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Product</TableHead>
                  <TableHead>Assessment Date</TableHead>
                  <TableHead>Next Review</TableHead>
                  <TableHead>Conducted By</TableHead>
                  <TableHead>Approved By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredAssessments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No risk assessments found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredAssessments.map((assessment) => (
                    <TableRow key={assessment.id}>
                      <TableCell className="font-medium">{getProductName(assessment.productId)}</TableCell>
                      <TableCell>{format(assessment.assessmentDate, 'MMM d, yyyy')}</TableCell>
                      <TableCell>{format(assessment.nextReviewDate, 'MMM d, yyyy')}</TableCell>
                      <TableCell>{assessment.conductedBy}</TableCell>
                      <TableCell>{assessment.approvedBy}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" size="sm" onClick={() => openViewDialog(assessment)}>
                            <FileText className="h-4 w-4" />
                            <span className="sr-only">View</span>
                          </Button>
                          <Button variant="outline" size="sm" onClick={() => openEditDialog(assessment)}>
                            <PenLine className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteAssessment(assessment.id)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      
      {/* Add Risk Assessment Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Risk Assessment</DialogTitle>
            <DialogDescription>
              Add a new allergen risk assessment for a product.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleAddAssessment} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="productId">Product *</Label>
              <Select 
                value={formValues.productId} 
                onValueChange={(value) => handleSelectChange('productId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>{product.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Assessment Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.assessmentDate ? format(formValues.assessmentDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.assessmentDate}
                      onSelect={(date) => handleDateChange('assessmentDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label>Next Review Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.nextReviewDate ? format(formValues.nextReviewDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.nextReviewDate}
                      onSelect={(date) => handleDateChange('nextReviewDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="conductedBy">Conducted By *</Label>
                <Input
                  id="conductedBy"
                  name="conductedBy"
                  value={formValues.conductedBy}
                  onChange={handleInputChange}
                  placeholder="e.g., Food Safety Team"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="approvedBy">Approved By</Label>
                <Input
                  id="approvedBy"
                  name="approvedBy"
                  value={formValues.approvedBy}
                  onChange={handleInputChange}
                  placeholder="e.g., QA Manager"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="crossContactRisks">Cross-Contact Risks *</Label>
              <Textarea
                id="crossContactRisks"
                name="crossContactRisks"
                value={formValues.crossContactRisks}
                onChange={handleInputChange}
                placeholder="Describe potential cross-contact risks..."
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="controlMeasureEffectiveness">Control Measure Effectiveness</Label>
              <Textarea
                id="controlMeasureEffectiveness"
                name="controlMeasureEffectiveness"
                value={formValues.controlMeasureEffectiveness}
                onChange={handleInputChange}
                placeholder="Evaluate the effectiveness of current control measures..."
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="correctiveActions">Corrective Actions</Label>
              <Textarea
                id="correctiveActions"
                name="correctiveActions"
                value={formValues.correctiveActions}
                onChange={handleInputChange}
                placeholder="Describe any corrective actions needed..."
              />
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Add Risk Assessment
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Edit Risk Assessment Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Risk Assessment</DialogTitle>
            <DialogDescription>
              Update allergen risk assessment details.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleEditAssessment} className="space-y-4">
            {/* Same form fields as Add Dialog */}
            <div className="space-y-2">
              <Label htmlFor="productId">Product *</Label>
              <Select 
                value={formValues.productId} 
                onValueChange={(value) => handleSelectChange('productId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>{product.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Assessment Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.assessmentDate ? format(formValues.assessmentDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.assessmentDate}
                      onSelect={(date) => handleDateChange('assessmentDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label>Next Review Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.nextReviewDate ? format(formValues.nextReviewDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.nextReviewDate}
                      onSelect={(date) => handleDateChange('nextReviewDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="conductedBy">Conducted By *</Label>
                <Input
                  id="conductedBy"
                  name="conductedBy"
                  value={formValues.conductedBy}
                  onChange={handleInputChange}
                  placeholder="e.g., Food Safety Team"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="approvedBy">Approved By</Label>
                <Input
                  id="approvedBy"
                  name="approvedBy"
                  value={formValues.approvedBy}
                  onChange={handleInputChange}
                  placeholder="e.g., QA Manager"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="crossContactRisks">Cross-Contact Risks *</Label>
              <Textarea
                id="crossContactRisks"
                name="crossContactRisks"
                value={formValues.crossContactRisks}
                onChange={handleInputChange}
                placeholder="Describe potential cross-contact risks..."
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="controlMeasureEffectiveness">Control Measure Effectiveness</Label>
              <Textarea
                id="controlMeasureEffectiveness"
                name="controlMeasureEffectiveness"
                value={formValues.controlMeasureEffectiveness}
                onChange={handleInputChange}
                placeholder="Evaluate the effectiveness of current control measures..."
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="correctiveActions">Corrective Actions</Label>
              <Textarea
                id="correctiveActions"
                name="correctiveActions"
                value={formValues.correctiveActions}
                onChange={handleInputChange}
                placeholder="Describe any corrective actions needed..."
              />
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Risk Assessment
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* View Risk Assessment Dialog */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Risk Assessment Details</DialogTitle>
            <DialogDescription>
              {selectedAssessment && (
                <span>
                  For {getProductName(selectedAssessment.productId)} - Conducted on {format(selectedAssessment.assessmentDate, 'MMMM d, yyyy')}
                </span>
              )}
            </DialogDescription>
          </DialogHeader>
          
          {selectedAssessment && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium">Product</h4>
                  <p className="text-sm">{getProductName(selectedAssessment.productId)}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Assessment Date</h4>
                  <p className="text-sm">{format(selectedAssessment.assessmentDate, 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Next Review Date</h4>
                  <p className="text-sm">{format(selectedAssessment.nextReviewDate, 'MMMM d, yyyy')}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Conducted By</h4>
                  <p className="text-sm">{selectedAssessment.conductedBy}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Approved By</h4>
                  <p className="text-sm">{selectedAssessment.approvedBy || 'Not approved yet'}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium">Last Updated</h4>
                  <p className="text-sm">{format(selectedAssessment.updatedAt, 'MMMM d, yyyy')}</p>
                </div>
              </div>
              
              <div>
                <h4 className="text-sm font-medium">Cross-Contact Risks</h4>
                <p className="text-sm mt-1 p-3 bg-muted rounded-md">{selectedAssessment.crossContactRisks}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium">Control Measure Effectiveness</h4>
                <p className="text-sm mt-1 p-3 bg-muted rounded-md">{selectedAssessment.controlMeasureEffectiveness}</p>
              </div>
              
              <div>
                <h4 className="text-sm font-medium">Corrective Actions</h4>
                <p className="text-sm mt-1 p-3 bg-muted rounded-md">{selectedAssessment.correctiveActions}</p>
              </div>
              
              <DialogFooter>
                <Button onClick={() => setIsViewDialogOpen(false)}>Close</Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
