
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import { useLocalStorage } from '@/hooks/use-local-storage';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger
} from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import {
  UserRound,
  Bell,
  Settings2,
  KeyRound,
  CheckCircle,
  Moon,
  Sun,
  Monitor,
  Languages
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { Switch } from '@/components/ui/switch';

const Settings = () => {
  const { currentUser, updateUserInfo } = useAuth();
  const { theme, setTheme } = useTheme();

  // Use localStorage for all settings
  const [profileForm, setProfileForm] = useLocalStorage('haccp_profile', {
    name: currentUser?.name || '',
    email: currentUser?.email || '',
    company: 'Food Safety Inc.',
    bio: 'Quality Assurance Professional with 5+ years of experience in HACCP systems management.',
  });

  const [notifications, setNotifications] = useLocalStorage('haccp_notifications', {
    planApproval: true,
    hazardUpdates: true,
    ccpAlerts: true,
    emailNotifications: false,
  });

  const [appSettings, setAppSettings] = useLocalStorage('haccp_app_settings', {
    exportFormat: 'pdf',
    riskMethodology: 'standard',
    autoSaveDrafts: true,
    language: 'english'
  });

  const [activeTab, setActiveTab] = useState('profile');

  // Update profile form when user changes
  useEffect(() => {
    if (currentUser) {
      setProfileForm(prev => ({
        ...prev,
        name: currentUser.name,
        email: currentUser.email
      }));
    }
  }, [currentUser, setProfileForm]);

  const handleProfileSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would call an API to update the profile
    toast({
      title: "Profile Updated",
      description: "Your profile information has been updated successfully.",
    });
  };

  const handlePasswordSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, this would validate and update the password
    toast({
      title: "Password Changed",
      description: "Your password has been updated successfully.",
    });
  };

  const handleNotificationChange = (key: string) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key as keyof typeof notifications],
    }));

    toast({
      title: "Notification Preferences Updated",
      description: "Your notification settings have been saved.",
    });
  };

  const handleExportFormatChange = (format: string) => {
    setAppSettings(prev => ({
      ...prev,
      exportFormat: format
    }));
  };

  const handleRiskMethodologyChange = (methodology: string) => {
    setAppSettings(prev => ({
      ...prev,
      riskMethodology: methodology
    }));
  };

  const handleAutoSaveChange = () => {
    setAppSettings(prev => ({
      ...prev,
      autoSaveDrafts: !prev.autoSaveDrafts
    }));
  };

  const handleLanguageChange = (language: string) => {
    setAppSettings(prev => ({
      ...prev,
      language: language
    }));
  };

  const handleSaveSettings = () => {
    // All settings are already saved via useLocalStorage
    toast({
      title: "Settings Saved",
      description: "Your application settings have been updated successfully.",
    });
  };

  const handleResetDefaults = () => {
    setAppSettings({
      exportFormat: 'pdf',
      riskMethodology: 'standard',
      autoSaveDrafts: true,
      language: 'english'
    });

    toast({
      title: "Settings Reset",
      description: "Your application settings have been reset to defaults.",
    });
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Settings</h1>
        <p className="text-muted-foreground">
          Manage your account settings and preferences.
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <UserRound className="h-4 w-4" />
            <span className="hidden sm:inline">Profile</span>
          </TabsTrigger>
          <TabsTrigger value="password" className="flex items-center gap-2">
            <KeyRound className="h-4 w-4" />
            <span className="hidden sm:inline">Password</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            <span className="hidden sm:inline">Notifications</span>
          </TabsTrigger>
          <TabsTrigger value="appearance" className="flex items-center gap-2">
            <Settings2 className="h-4 w-4" />
            <span className="hidden sm:inline">App Settings</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <form onSubmit={handleProfileSubmit}>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update your personal and professional details.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name</Label>
                    <Input
                      id="name"
                      value={profileForm.name}
                      onChange={(e) => setProfileForm({...profileForm, name: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profileForm.email}
                      onChange={(e) => setProfileForm({...profileForm, email: e.target.value})}
                      disabled
                    />
                    <p className="text-xs text-muted-foreground">Email cannot be changed.</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="company">Company</Label>
                    <Input
                      id="company"
                      value={profileForm.company}
                      onChange={(e) => setProfileForm({...profileForm, company: e.target.value})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Input id="role" value={currentUser?.role || ''} disabled />
                    <p className="text-xs text-muted-foreground">Contact an administrator to change roles.</p>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bio">Bio / Professional Summary</Label>
                  <Textarea
                    id="bio"
                    value={profileForm.bio}
                    onChange={(e) => setProfileForm({...profileForm, bio: e.target.value})}
                    rows={4}
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit">Update Profile</Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="password" className="space-y-4">
          <Card>
            <form onSubmit={handlePasswordSubmit}>
              <CardHeader>
                <CardTitle>Change Password</CardTitle>
                <CardDescription>
                  Update your password to maintain account security.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="current-password">Current Password</Label>
                  <Input id="current-password" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-password">New Password</Label>
                  <Input id="new-password" type="password" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm New Password</Label>
                  <Input id="confirm-password" type="password" />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit">Change Password</Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification Preferences</CardTitle>
              <CardDescription>
                Choose which notifications you want to receive.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>HACCP Plan Approvals</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive notifications when a plan requires or receives approval.
                  </p>
                </div>
                <Switch
                  checked={notifications.planApproval}
                  onCheckedChange={() => handleNotificationChange('planApproval')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Hazard Analysis Updates</Label>
                  <p className="text-sm text-muted-foreground">
                    Get notified when hazards are updated or new ones are added.
                  </p>
                </div>
                <Switch
                  checked={notifications.hazardUpdates}
                  onCheckedChange={() => handleNotificationChange('hazardUpdates')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Critical Control Point Alerts</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive critical alerts for CCP limit breaches or monitoring updates.
                  </p>
                </div>
                <Switch
                  checked={notifications.ccpAlerts}
                  onCheckedChange={() => handleNotificationChange('ccpAlerts')}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Receive important notifications via email in addition to in-app alerts.
                  </p>
                </div>
                <Switch
                  checked={notifications.emailNotifications}
                  onCheckedChange={() => handleNotificationChange('emailNotifications')}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="appearance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Application Settings</CardTitle>
              <CardDescription>
                Customize how the HACCP Manager application works for you.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label>Data Export Format</Label>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.exportFormat === 'pdf' ? "bg-secondary" : ""}
                      onClick={() => handleExportFormatChange('pdf')}
                    >
                      PDF
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.exportFormat === 'excel' ? "bg-secondary" : ""}
                      onClick={() => handleExportFormatChange('excel')}
                    >
                      Excel
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.exportFormat === 'csv' ? "bg-secondary" : ""}
                      onClick={() => handleExportFormatChange('csv')}
                    >
                      CSV
                    </Button>
                  </div>
                </div>

                <div>
                  <Label>Risk Assessment Methodology</Label>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.riskMethodology === 'standard' ? "bg-secondary" : ""}
                      onClick={() => handleRiskMethodologyChange('standard')}
                    >
                      Standard
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.riskMethodology === 'conservative' ? "bg-secondary" : ""}
                      onClick={() => handleRiskMethodologyChange('conservative')}
                    >
                      Conservative
                    </Button>
                  </div>
                </div>

                <div>
                  <Label>Theme</Label>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className={theme === 'light' ? "bg-secondary" : ""}
                      onClick={() => setTheme('light')}
                    >
                      <Sun className="h-4 w-4 mr-2" />
                      Light
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={theme === 'dark' ? "bg-secondary" : ""}
                      onClick={() => setTheme('dark')}
                    >
                      <Moon className="h-4 w-4 mr-2" />
                      Dark
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={theme === 'system' ? "bg-secondary" : ""}
                      onClick={() => setTheme('system')}
                    >
                      <Monitor className="h-4 w-4 mr-2" />
                      System
                    </Button>
                  </div>
                </div>

                <div>
                  <Label>Language</Label>
                  <div className="flex gap-2 mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.language === 'english' ? "bg-secondary" : ""}
                      onClick={() => handleLanguageChange('english')}
                    >
                      <Languages className="h-4 w-4 mr-2" />
                      English
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.language === 'spanish' ? "bg-secondary" : ""}
                      onClick={() => handleLanguageChange('spanish')}
                    >
                      <Languages className="h-4 w-4 mr-2" />
                      Spanish
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className={appSettings.language === 'french' ? "bg-secondary" : ""}
                      onClick={() => handleLanguageChange('french')}
                    >
                      <Languages className="h-4 w-4 mr-2" />
                      French
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Auto-save Drafts</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically save drafts of plans and analyses.
                    </p>
                  </div>
                  <Switch
                    checked={appSettings.autoSaveDrafts}
                    onCheckedChange={handleAutoSaveChange}
                  />
                </div>

                <div className="pt-4">
                  <Button
                    variant="default"
                    className="mr-2"
                    onClick={handleSaveSettings}
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Save Settings
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleResetDefaults}
                  >
                    Reset to Defaults
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Settings;
