import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from '@/hooks/use-toast';
import { CheckCircle, Plus, Search, Trash2, PenLine } from 'lucide-react';
import { AllergenControlMeasure, MOCK_ALLERGEN_CONTROL_MEASURES } from '@/models/allergen';

interface AllergenControlMeasuresProps {
  controlMeasures?: AllergenControlMeasure[];
  setControlMeasures?: React.Dispatch<React.SetStateAction<AllergenControlMeasure[]>>;
}

export function AllergenControlMeasures({ 
  controlMeasures = MOCK_ALLERGEN_CONTROL_MEASURES, 
  setControlMeasures 
}: AllergenControlMeasuresProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedMeasure, setSelectedMeasure] = useState<AllergenControlMeasure | null>(null);
  
  // Form state
  const [formValues, setFormValues] = useState<Partial<AllergenControlMeasure>>({
    name: '',
    description: '',
    type: 'prevention',
    verificationMethod: '',
    frequency: '',
    responsiblePerson: ''
  });
  
  // Filter control measures based on search query
  const filteredMeasures = controlMeasures.filter(measure => 
    measure.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    measure.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    measure.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
    measure.responsiblePerson.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };
  
  // Open add dialog
  const openAddDialog = () => {
    setFormValues({
      name: '',
      description: '',
      type: 'prevention',
      verificationMethod: '',
      frequency: '',
      responsiblePerson: ''
    });
    setIsAddDialogOpen(true);
  };
  
  // Open edit dialog
  const openEditDialog = (measure: AllergenControlMeasure) => {
    setSelectedMeasure(measure);
    setFormValues({
      name: measure.name,
      description: measure.description,
      type: measure.type,
      verificationMethod: measure.verificationMethod,
      frequency: measure.frequency,
      responsiblePerson: measure.responsiblePerson
    });
    setIsEditDialogOpen(true);
  };
  
  // Handle form submission for adding a new control measure
  const handleAddMeasure = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formValues.name || !formValues.description || !formValues.type) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }
    
    const newMeasure: AllergenControlMeasure = {
      id: `acm${controlMeasures.length + 1}`,
      name: formValues.name,
      description: formValues.description,
      type: formValues.type as 'prevention' | 'segregation' | 'cleaning' | 'labeling' | 'training',
      verificationMethod: formValues.verificationMethod || '',
      frequency: formValues.frequency || '',
      responsiblePerson: formValues.responsiblePerson || '',
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };
    
    if (setControlMeasures) {
      setControlMeasures([...controlMeasures, newMeasure]);
    }
    
    setIsAddDialogOpen(false);
    
    toast({
      title: 'Success',
      description: 'Control measure added successfully'
    });
  };
  
  // Handle form submission for editing a control measure
  const handleEditMeasure = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedMeasure) return;
    
    if (!formValues.name || !formValues.description || !formValues.type) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }
    
    if (setControlMeasures) {
      const updatedMeasures = controlMeasures.map(measure => 
        measure.id === selectedMeasure.id 
          ? {
              ...measure,
              name: formValues.name!,
              description: formValues.description!,
              type: formValues.type as 'prevention' | 'segregation' | 'cleaning' | 'labeling' | 'training',
              verificationMethod: formValues.verificationMethod || '',
              frequency: formValues.frequency || '',
              responsiblePerson: formValues.responsiblePerson || '',
              updatedBy: 'user1',
              updatedAt: new Date()
            }
          : measure
      );
      
      setControlMeasures(updatedMeasures);
    }
    
    setIsEditDialogOpen(false);
    
    toast({
      title: 'Success',
      description: 'Control measure updated successfully'
    });
  };
  
  // Delete a control measure
  const handleDeleteMeasure = (id: string) => {
    if (setControlMeasures) {
      const updatedMeasures = controlMeasures.filter(measure => measure.id !== id);
      setControlMeasures(updatedMeasures);
      
      toast({
        title: 'Success',
        description: 'Control measure deleted successfully'
      });
    }
  };
  
  // Render type badge
  const renderTypeBadge = (type: string) => {
    switch (type) {
      case 'prevention':
        return <Badge className="bg-blue-500">Prevention</Badge>;
      case 'segregation':
        return <Badge className="bg-purple-500">Segregation</Badge>;
      case 'cleaning':
        return <Badge className="bg-green-500">Cleaning</Badge>;
      case 'labeling':
        return <Badge className="bg-amber-500">Labeling</Badge>;
      case 'training':
        return <Badge className="bg-indigo-500">Training</Badge>;
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Allergen Control Measures</CardTitle>
              <CardDescription>
                Manage control measures to prevent allergen cross-contamination
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Control Measure
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search control measures..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[200px]">Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Verification Method</TableHead>
                  <TableHead>Frequency</TableHead>
                  <TableHead>Responsible Person</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMeasures.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      No control measures found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredMeasures.map((measure) => (
                    <TableRow key={measure.id}>
                      <TableCell className="font-medium">
                        <div>
                          {measure.name}
                          <p className="text-xs text-muted-foreground truncate max-w-[200px]">
                            {measure.description}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>{renderTypeBadge(measure.type)}</TableCell>
                      <TableCell>{measure.verificationMethod}</TableCell>
                      <TableCell>{measure.frequency}</TableCell>
                      <TableCell>{measure.responsiblePerson}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" size="sm" onClick={() => openEditDialog(measure)}>
                            <PenLine className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteMeasure(measure.id)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      
      {/* Add Control Measure Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add Control Measure</DialogTitle>
            <DialogDescription>
              Add a new allergen control measure to prevent cross-contamination.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleAddMeasure} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Measure Name *</Label>
              <Input
                id="name"
                name="name"
                value={formValues.name}
                onChange={handleInputChange}
                placeholder="e.g., Production Scheduling"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues.description}
                onChange={handleInputChange}
                placeholder="e.g., Schedule production runs to minimize cross-contact..."
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="type">Type *</Label>
              <Select 
                value={formValues.type} 
                onValueChange={(value) => handleSelectChange('type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="prevention">Prevention</SelectItem>
                  <SelectItem value="segregation">Segregation</SelectItem>
                  <SelectItem value="cleaning">Cleaning</SelectItem>
                  <SelectItem value="labeling">Labeling</SelectItem>
                  <SelectItem value="training">Training</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="verificationMethod">Verification Method</Label>
                <Input
                  id="verificationMethod"
                  name="verificationMethod"
                  value={formValues.verificationMethod}
                  onChange={handleInputChange}
                  placeholder="e.g., Production schedule review"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Input
                  id="frequency"
                  name="frequency"
                  value={formValues.frequency}
                  onChange={handleInputChange}
                  placeholder="e.g., Daily"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="responsiblePerson">Responsible Person</Label>
              <Input
                id="responsiblePerson"
                name="responsiblePerson"
                value={formValues.responsiblePerson}
                onChange={handleInputChange}
                placeholder="e.g., Production Manager"
              />
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Add Control Measure
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Edit Control Measure Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Control Measure</DialogTitle>
            <DialogDescription>
              Update allergen control measure details.
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleEditMeasure} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Measure Name *</Label>
              <Input
                id="name"
                name="name"
                value={formValues.name}
                onChange={handleInputChange}
                placeholder="e.g., Production Scheduling"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues.description}
                onChange={handleInputChange}
                placeholder="e.g., Schedule production runs to minimize cross-contact..."
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="type">Type *</Label>
              <Select 
                value={formValues.type} 
                onValueChange={(value) => handleSelectChange('type', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="prevention">Prevention</SelectItem>
                  <SelectItem value="segregation">Segregation</SelectItem>
                  <SelectItem value="cleaning">Cleaning</SelectItem>
                  <SelectItem value="labeling">Labeling</SelectItem>
                  <SelectItem value="training">Training</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="verificationMethod">Verification Method</Label>
                <Input
                  id="verificationMethod"
                  name="verificationMethod"
                  value={formValues.verificationMethod}
                  onChange={handleInputChange}
                  placeholder="e.g., Production schedule review"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="frequency">Frequency</Label>
                <Input
                  id="frequency"
                  name="frequency"
                  value={formValues.frequency}
                  onChange={handleInputChange}
                  placeholder="e.g., Daily"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="responsiblePerson">Responsible Person</Label>
              <Input
                id="responsiblePerson"
                name="responsiblePerson"
                value={formValues.responsiblePerson}
                onChange={handleInputChange}
                placeholder="e.g., Production Manager"
              />
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Control Measure
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
