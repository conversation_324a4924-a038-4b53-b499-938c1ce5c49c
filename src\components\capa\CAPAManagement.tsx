import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { CalendarIcon, FileText, Plus, Search, Trash2, PenLine, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { CA<PERSON>, Claim } from '@/models/capa';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CAPADetailsModal } from './CAPADetailsModal';

interface CAPAManagementProps {
  capas: CAPA[];
  setCapas: React.Dispatch<React.SetStateAction<CAPA[]>>;
  claims: Claim[];
  products: { id: string; name: string; description: string }[];
}

export function CAPAManagement({
  capas,
  setCapas,
  claims,
  products
}: CAPAManagementProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedCAPA, setSelectedCAPA] = useState<CAPA | null>(null);
  const [isViewDetailsDialogOpen, setIsViewDetailsDialogOpen] = useState(false);

  // Form state
  const [formValues, setFormValues] = useState<Partial<CAPA>>({
    title: '',
    description: '',
    source: 'internal_audit',
    category: 'product_quality',
    priority: 'medium',
    status: 'open',
    dateIdentified: new Date(),
    targetCompletionDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
    responsiblePerson: '',
    productId: '',
    claimId: ''
  });

  // Filter CAPAs based on search query
  const filteredCAPAs = capas.filter(capa => {
    const product = products.find(p => p.id === capa.productId);
    return (
      capa.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      capa.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      capa.responsiblePerson.toLowerCase().includes(searchQuery.toLowerCase()) ||
      capa.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (product && product.name.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  });

  const handleFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    // Convert "none" value to empty string for optional fields
    const finalValue = (name === 'productId' || name === 'claimId') && value === 'none' ? '' : value;
    setFormValues(prev => ({ ...prev, [name]: finalValue }));
  };

  const handleDateChange = (name: string, date: Date | undefined) => {
    setFormValues(prev => ({ ...prev, [name]: date }));
  };

  const openAddDialog = () => {
    setFormValues({
      title: '',
      description: '',
      source: 'internal_audit',
      category: 'product_quality',
      priority: 'medium',
      status: 'open',
      dateIdentified: new Date(),
      targetCompletionDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
      responsiblePerson: '',
      productId: '',
      claimId: ''
    });
    setIsAddDialogOpen(true);
  };

  const openEditDialog = (capa: CAPA) => {
    setSelectedCAPA(capa);
    setFormValues({
      title: capa.title,
      description: capa.description,
      source: capa.source,
      category: capa.category,
      priority: capa.priority,
      status: capa.status,
      dateIdentified: capa.dateIdentified,
      targetCompletionDate: capa.targetCompletionDate,
      actualCompletionDate: capa.actualCompletionDate,
      responsiblePerson: capa.responsiblePerson,
      productId: capa.productId,
      claimId: capa.claimId
    });
    setIsEditDialogOpen(true);
  };

  const openViewDetailsModal = (capa: CAPA) => {
    setSelectedCAPA(capa);
    setIsViewDetailsDialogOpen(true);
  };

  const handleNavigateTab = (tab: string, capaId: string) => {
    setIsViewDetailsDialogOpen(false);

    // Dispatch custom event for tab navigation
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('navigate-tab', {
        detail: { tab, capaId }
      });
      window.dispatchEvent(event);
    }
  };

  // Add keyboard shortcut handler for Ctrl+E to edit CAPA
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (isViewDetailsDialogOpen && selectedCAPA && e.key === 'e' && (e.ctrlKey || e.metaKey)) {
        e.preventDefault();
        setIsViewDetailsDialogOpen(false);
        openEditDialog(selectedCAPA);
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isViewDetailsDialogOpen, selectedCAPA]);

  const handleAddCAPA = () => {
    if (!formValues.title || !formValues.responsiblePerson) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    const newCAPA: CAPA = {
      id: `capa${capas.length + 1}`,
      title: formValues.title!,
      description: formValues.description || '',
      source: formValues.source as any || 'internal_audit',
      category: formValues.category as any || 'product_quality',
      priority: formValues.priority as any || 'medium',
      status: formValues.status as any || 'open',
      dateIdentified: formValues.dateIdentified || new Date(),
      targetCompletionDate: formValues.targetCompletionDate || new Date(new Date().setMonth(new Date().getMonth() + 1)),
      responsiblePerson: formValues.responsiblePerson!,
      productId: formValues.productId,
      claimId: formValues.claimId,
      rootCauseAnalysisId: undefined,
      correctiveActionIds: [],
      preventiveActionIds: [],
      verificationIds: [],
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };

    setCapas([...capas, newCAPA]);
    setIsAddDialogOpen(false);

    toast({
      title: 'Success',
      description: 'CAPA added successfully'
    });
  };

  const handleEditCAPA = () => {
    if (!selectedCAPA) return;

    if (!formValues.title || !formValues.responsiblePerson) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    const updatedCAPAs = capas.map(capa =>
      capa.id === selectedCAPA.id
        ? {
            ...capa,
            title: formValues.title!,
            description: formValues.description || '',
            source: formValues.source as any,
            category: formValues.category as any,
            priority: formValues.priority as any,
            status: formValues.status as any,
            dateIdentified: formValues.dateIdentified || capa.dateIdentified,
            targetCompletionDate: formValues.targetCompletionDate || capa.targetCompletionDate,
            actualCompletionDate: formValues.actualCompletionDate,
            responsiblePerson: formValues.responsiblePerson!,
            productId: formValues.productId,
            claimId: formValues.claimId,
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : capa
    );

    setCapas(updatedCAPAs);
    setIsEditDialogOpen(false);

    toast({
      title: 'Success',
      description: 'CAPA updated successfully'
    });
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      case 'high':
        return <Badge variant="default" className="bg-orange-500">High</Badge>;
      case 'medium':
        return <Badge variant="default" className="bg-yellow-500">Medium</Badge>;
      case 'low':
        return <Badge variant="outline">Low</Badge>;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge variant="default" className="bg-blue-500">Open</Badge>;
      case 'investigation':
        return <Badge variant="default" className="bg-purple-500">Investigation</Badge>;
      case 'action_planned':
        return <Badge variant="default" className="bg-yellow-500">Action Planned</Badge>;
      case 'implementing':
        return <Badge variant="default" className="bg-orange-500">Implementing</Badge>;
      case 'verification':
        return <Badge variant="default" className="bg-green-500">Verification</Badge>;
      case 'closed':
        return <Badge variant="default" className="bg-green-700">Closed</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="border-red-500 text-red-500">Rejected</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>CAPA Management</CardTitle>
              <CardDescription>
                Manage Corrective and Preventive Actions
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add CAPA
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search CAPAs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {filteredCAPAs.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              No CAPAs found. Add a new CAPA to get started.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Responsible</TableHead>
                    <TableHead>Target Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCAPAs.map((capa) => {
                    // These variables are used for filtering but not in the render
                    // const product = products.find(p => p.id === capa.productId);
                    // const claim = claims.find(c => c.id === capa.claimId);

                    return (
                      <TableRow key={capa.id}>
                        <TableCell className="font-medium">{capa.title}</TableCell>
                        <TableCell className="capitalize">{capa.category.replace('_', ' ')}</TableCell>
                        <TableCell>{getPriorityBadge(capa.priority)}</TableCell>
                        <TableCell>{getStatusBadge(capa.status)}</TableCell>
                        <TableCell>{capa.responsiblePerson}</TableCell>
                        <TableCell>{format(capa.targetCompletionDate, 'MMM d, yyyy')}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="flex items-center gap-1"
                              onClick={() => openViewDetailsModal(capa)}
                              title="View CAPA Details"
                            >
                              <FileText className="h-4 w-4" />
                              <span className="hidden sm:inline">View</span>
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="flex items-center gap-1"
                              onClick={() => openEditDialog(capa)}
                              title="Edit CAPA"
                            >
                              <PenLine className="h-4 w-4" />
                              <span className="hidden sm:inline">Edit</span>
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add CAPA Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Add CAPA</DialogTitle>
            <DialogDescription>
              Create a new Corrective and Preventive Action plan.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4 max-h-[60vh] overflow-y-auto pr-1">
            <div className="space-y-2">
              <Label htmlFor="title">CAPA Title *</Label>
              <Input
                id="title"
                name="title"
                value={formValues.title || ''}
                onChange={handleFormChange}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues.description || ''}
                onChange={handleFormChange}
                rows={3}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="source">Source</Label>
                <Select
                  value={formValues.source || 'internal_audit'}
                  onValueChange={(value) => handleSelectChange('source', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select source" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="customer_claim">Customer Claim</SelectItem>
                    <SelectItem value="internal_audit">Internal Audit</SelectItem>
                    <SelectItem value="external_audit">External Audit</SelectItem>
                    <SelectItem value="monitoring">Monitoring</SelectItem>
                    <SelectItem value="testing">Testing</SelectItem>
                    <SelectItem value="employee_suggestion">Employee Suggestion</SelectItem>
                    <SelectItem value="management_review">Management Review</SelectItem>
                    <SelectItem value="regulatory_inspection">Regulatory Inspection</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select
                  value={formValues.category || 'product_quality'}
                  onValueChange={(value) => handleSelectChange('category', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="product_quality">Product Quality</SelectItem>
                    <SelectItem value="food_safety">Food Safety</SelectItem>
                    <SelectItem value="packaging">Packaging</SelectItem>
                    <SelectItem value="labeling">Labeling</SelectItem>
                    <SelectItem value="documentation">Documentation</SelectItem>
                    <SelectItem value="process_control">Process Control</SelectItem>
                    <SelectItem value="allergen_management">Allergen Management</SelectItem>
                    <SelectItem value="foreign_material">Foreign Material</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <Select
                  value={formValues.priority || 'medium'}
                  onValueChange={(value) => handleSelectChange('priority', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select
                  value={formValues.status || 'open'}
                  onValueChange={(value) => handleSelectChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="open">Open</SelectItem>
                    <SelectItem value="investigation">Investigation</SelectItem>
                    <SelectItem value="action_planned">Action Planned</SelectItem>
                    <SelectItem value="implementing">Implementing</SelectItem>
                    <SelectItem value="verification">Verification</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Date Identified</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.dateIdentified ? (
                        format(formValues.dateIdentified, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.dateIdentified}
                      onSelect={(date) => handleDateChange('dateIdentified', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Target Completion Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.targetCompletionDate ? (
                        format(formValues.targetCompletionDate, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.targetCompletionDate}
                      onSelect={(date) => handleDateChange('targetCompletionDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="responsiblePerson">Responsible Person *</Label>
                <Input
                  id="responsiblePerson"
                  name="responsiblePerson"
                  value={formValues.responsiblePerson || ''}
                  onChange={handleFormChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="productId">Related Product</Label>
                <Select
                  value={formValues.productId || 'none'}
                  onValueChange={(value) => handleSelectChange('productId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a product (optional)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="claimId">Related Claim</Label>
              <Select
                value={formValues.claimId || 'none'}
                onValueChange={(value) => handleSelectChange('claimId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a claim (optional)" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {claims.map((claim) => (
                    <SelectItem key={claim.id} value={claim.id}>
                      {claim.customerName} - {claim.description.substring(0, 30)}...
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="mt-4 border-t pt-4">
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddCAPA}>
              Add CAPA
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit CAPA Dialog - Similar to Add Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Edit CAPA</DialogTitle>
            <DialogDescription>
              Update the CAPA details.
            </DialogDescription>
          </DialogHeader>

          <div className="max-h-[60vh] overflow-y-auto pr-1 py-4">
            {/* Same form fields as Add Dialog */}
            {/* ... */}
          </div>

          <DialogFooter className="mt-4 border-t pt-4">
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditCAPA}>
              Update CAPA
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View CAPA Details Dialog */}
      <Dialog
        open={isViewDetailsDialogOpen}
        onOpenChange={setIsViewDetailsDialogOpen}
      >
        <DialogContent
          className="max-w-3xl max-h-[90vh]"
          onEscapeKeyDown={() => setIsViewDetailsDialogOpen(false)}
        >
          <DialogHeader>
            <DialogTitle>CAPA Details</DialogTitle>
            <DialogDescription>
              View complete information and navigate to related items
            </DialogDescription>
          </DialogHeader>

          {selectedCAPA && (
            <div className="space-y-6 max-h-[60vh] overflow-y-auto pr-1">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">ID</h3>
                  <p>{selectedCAPA.id}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Status</h3>
                  <p>{getStatusBadge(selectedCAPA.status)}</p>
                </div>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Title</h3>
                <p className="font-medium">{selectedCAPA.title}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                <p>{selectedCAPA.description}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Category</h3>
                  <p className="capitalize">{selectedCAPA.category.replace('_', ' ')}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Priority</h3>
                  <p>{getPriorityBadge(selectedCAPA.priority)}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Source</h3>
                  <p className="capitalize">{selectedCAPA.source.replace('_', ' ')}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Responsible Person</h3>
                  <p>{selectedCAPA.responsiblePerson}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Date Identified</h3>
                  <p>{format(selectedCAPA.dateIdentified, 'PPP')}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Target Completion Date</h3>
                  <p>{format(selectedCAPA.targetCompletionDate, 'PPP')}</p>
                </div>
              </div>

              {selectedCAPA.actualCompletionDate && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Actual Completion Date</h3>
                  <p>{format(selectedCAPA.actualCompletionDate, 'PPP')}</p>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Related Product</h3>
                  <p>
                    {selectedCAPA.productId
                      ? products.find(p => p.id === selectedCAPA.productId)?.name || 'Unknown Product'
                      : 'None'}
                  </p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Related Claim</h3>
                  <p>
                    {selectedCAPA.claimId
                      ? claims.find(c => c.id === selectedCAPA.claimId)?.customerName || 'Unknown Claim'
                      : 'None'}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Created By</h3>
                  <p>{selectedCAPA.createdBy} on {format(selectedCAPA.createdAt, 'PPP')}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Updated</h3>
                  <p>{selectedCAPA.updatedBy} on {format(selectedCAPA.updatedAt, 'PPP')}</p>
                </div>
              </div>

              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Related Items</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="p-4 bg-muted rounded-md flex flex-col">
                    <div className="flex justify-between items-center mb-2">
                      <p className="font-medium">Root Cause Analysis</p>
                      {selectedCAPA.rootCauseAnalysisId && (
                        <Badge variant="outline" className="ml-2">1</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {selectedCAPA.rootCauseAnalysisId ? '1 analysis conducted' : 'None conducted yet'}
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-auto"
                      onClick={() => {
                        setIsViewDetailsDialogOpen(false);
                        // Navigate to Root Cause tab
                        if (typeof window !== 'undefined') {
                          const event = new CustomEvent('navigate-tab', {
                            detail: { tab: 'root-cause', capaId: selectedCAPA.id }
                          });
                          window.dispatchEvent(event);
                        }
                      }}
                    >
                      {selectedCAPA.rootCauseAnalysisId ? 'View Analysis' : 'Add Analysis'}
                    </Button>
                  </div>

                  <div className="p-4 bg-muted rounded-md flex flex-col">
                    <div className="flex justify-between items-center mb-2">
                      <p className="font-medium">Corrective Actions</p>
                      {selectedCAPA.correctiveActionIds.length > 0 && (
                        <Badge variant="outline" className="ml-2">{selectedCAPA.correctiveActionIds.length}</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {selectedCAPA.correctiveActionIds.length} action(s) defined
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-auto"
                      onClick={() => {
                        setIsViewDetailsDialogOpen(false);
                        // Navigate to Corrective Actions tab
                        if (typeof window !== 'undefined') {
                          const event = new CustomEvent('navigate-tab', {
                            detail: { tab: 'corrective-actions', capaId: selectedCAPA.id }
                          });
                          window.dispatchEvent(event);
                        }
                      }}
                    >
                      {selectedCAPA.correctiveActionIds.length > 0 ? 'View Actions' : 'Add Action'}
                    </Button>
                  </div>

                  <div className="p-4 bg-muted rounded-md flex flex-col">
                    <div className="flex justify-between items-center mb-2">
                      <p className="font-medium">Preventive Actions</p>
                      {selectedCAPA.preventiveActionIds.length > 0 && (
                        <Badge variant="outline" className="ml-2">{selectedCAPA.preventiveActionIds.length}</Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground mb-3">
                      {selectedCAPA.preventiveActionIds.length} action(s) defined
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-auto"
                      onClick={() => {
                        setIsViewDetailsDialogOpen(false);
                        // Navigate to Preventive Actions tab
                        if (typeof window !== 'undefined') {
                          const event = new CustomEvent('navigate-tab', {
                            detail: { tab: 'preventive-actions', capaId: selectedCAPA.id }
                          });
                          window.dispatchEvent(event);
                        }
                      }}
                    >
                      {selectedCAPA.preventiveActionIds.length > 0 ? 'View Actions' : 'Add Action'}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-4 border-t pt-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button
                variant="outline"
                size="sm"
                className="mr-2"
                onClick={() => {
                  setIsViewDetailsDialogOpen(false);
                  openEditDialog(selectedCAPA!);
                }}
              >
                <PenLine className="h-4 w-4 mr-2" />
                Edit CAPA
              </Button>
              <div className="text-xs text-muted-foreground mt-1">
                Keyboard shortcuts: <kbd className="px-1 py-0.5 bg-muted rounded border">Esc</kbd> to close,
                <kbd className="px-1 py-0.5 bg-muted rounded border ml-1">{/Mac|iPhone|iPod|iPad/.test(navigator.userAgent) ? '⌘' : 'Ctrl'}+E</kbd> to edit
              </div>
            </div>
            <Button onClick={() => setIsViewDetailsDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}