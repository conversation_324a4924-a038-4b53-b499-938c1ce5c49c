
import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Hazard, CCP } from '@/models/types';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from '@/hooks/use-toast';
import { ClipboardCheck, FileDown, Pencil, ThermometerSnowflake } from 'lucide-react';
import { useLocalStorage } from '@/hooks/use-local-storage';

// Mock data - we'll filter only the CCPs from our hazards list
const mockHazards: Hazard[] = [
  {
    id: 'h4',
    processStepId: 'step4',
    description: 'Survival of pathogens due to inadequate cooking',
    type: 'Biological',
    severity: 5,
    likelihood: 3,
    controlMeasures: 'Time/temperature control, Staff training, Regular calibration of thermometers',
    isCCP: true,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h7',
    processStepId: 'step5',
    description: 'Growth of pathogens during cooling',
    type: 'Biological',
    severity: 5,
    likelihood: 4,
    controlMeasures: 'Rapid cooling, Temperature monitoring, Staff training',
    isCCP: true,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h9',
    processStepId: 'step6',
    description: 'Metal contamination from packaging equipment',
    type: 'Physical',
    severity: 4,
    likelihood: 2,
    controlMeasures: 'Metal detection, Regular maintenance, Staff training',
    isCCP: true,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
];

// Mock CCP data
const mockCCPs: CCP[] = [
  {
    id: 'ccp1',
    hazardId: 'h4',
    criticalLimit: 'Internal temperature must reach minimum 75°C for at least 15 seconds',
    monitoringProcedure: 'Measure core temperature with calibrated thermometer',
    monitoringFrequency: 'Every batch',
    correctiveAction: 'If temperature is below 75°C, continue cooking until temperature is reached. If product cannot reach temperature, reject the batch.',
    verificationProcedure: 'Daily calibration of thermometers, Weekly review of monitoring records',
    recordkeepingProcedure: 'Cooking temperature log, Corrective action records, Thermometer calibration records',
  },
  {
    id: 'ccp2',
    hazardId: 'h7',
    criticalLimit: 'Product must be cooled from 60°C to 21°C within 2 hours and from 21°C to 4°C within an additional 4 hours',
    monitoringProcedure: 'Measure product temperature with calibrated thermometer',
    monitoringFrequency: 'Every batch at specified time intervals',
    correctiveAction: 'If cooling rate is too slow, transfer product to smaller containers, use ice bath, or discard product if time limits are exceeded',
    verificationProcedure: 'Weekly review of cooling records, Monthly verification of cooling equipment',
    recordkeepingProcedure: 'Cooling temperature logs, Corrective action records, Equipment maintenance records',
  },
];

// Process steps reference for displaying process step names
const mockProcessSteps = {
  'step1': 'Raw Material Reception',
  'step2': 'Storage',
  'step3': 'Preparation',
  'step4': 'Cooking',
  'step5': 'Cooling',
  'step6': 'Packaging',
  'step7': 'Storage & Distribution',
};

export function CCPManagement() {
  const { hasPermission } = useAuth();
  const [ccps, setCCPs] = useLocalStorage<CCP[]>('haccp_ccps', mockCCPs);
  const [selectedHazard, setSelectedHazard] = useState<Hazard | null>(null);
  const [selectedCCP, setSelectedCCP] = useState<CCP | null>(null);
  const [isEditingCCP, setIsEditingCCP] = useState(false);
  const [formValues, setFormValues] = useState<Partial<CCP>>({});

  const canEdit = hasPermission('qa');

  // Get all hazards that are CCPs
  const ccpHazards = mockHazards.filter(h => h.isCCP);

  const openAddCCPForm = (hazard: Hazard) => {
    // Check if this hazard already has a CCP
    const existingCCP = ccps.find(c => c.hazardId === hazard.id);
    if (existingCCP) {
      setSelectedCCP(existingCCP);
      setFormValues(existingCCP);
      setIsEditingCCP(true);
    } else {
      setSelectedHazard(hazard);
      setFormValues({
        hazardId: hazard.id,
        criticalLimit: '',
        monitoringProcedure: '',
        monitoringFrequency: '',
        correctiveAction: '',
        verificationProcedure: '',
        recordkeepingProcedure: '',
      });
      setIsEditingCCP(false);
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmitCCP = (e: React.FormEvent) => {
    e.preventDefault();

    if (isEditingCCP && selectedCCP) {
      // Update existing CCP
      const updatedCCPs = ccps.map(c =>
        c.id === selectedCCP.id ? { ...c, ...formValues } : c
      );
      setCCPs(updatedCCPs);
      toast({
        title: 'Success',
        description: 'CCP has been updated',
      });
    } else {
      // Create new CCP
      const newCCP: CCP = {
        id: `ccp${ccps.length + 1}`,
        ...formValues,
      } as CCP;

      setCCPs([...ccps, newCCP]);
      toast({
        title: 'Success',
        description: 'New CCP has been created',
      });
    }

    setSelectedHazard(null);
    setSelectedCCP(null);
    setFormValues({});
  };

  // Find CCP for a specific hazard
  const findCCPForHazard = (hazardId: string) => {
    return ccps.find(ccp => ccp.hazardId === hazardId);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">Critical Control Points</h1>
        <Button variant="outline" size="sm">
          <FileDown className="h-4 w-4 mr-2" />
          Export CCPs
        </Button>
      </div>

      {ccpHazards.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <ClipboardCheck className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold mb-2">No Critical Control Points</h3>
            <p className="text-center text-muted-foreground max-w-md mb-4">
              No hazards have been identified as Critical Control Points yet.
              Identify CCPs in the Hazard Analysis section using the decision tree.
            </p>
            <Button onClick={() => window.location.href = '/hazard-analysis'}>
              Go to Hazard Analysis
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6">
          {ccpHazards.map((hazard) => {
            const ccp = findCCPForHazard(hazard.id);
            return (
              <Card key={hazard.id} className="overflow-hidden">
                <CardHeader className="bg-yellow-50 border-b border-yellow-100">
                  <div className="flex justify-between items-start">
                    <div>
                      <div className="flex items-center gap-2">
                        <ClipboardCheck className="h-5 w-5 text-yellow-600" />
                        <CardTitle>
                          CCP: {mockProcessSteps[hazard.processStepId as keyof typeof mockProcessSteps]}
                        </CardTitle>
                      </div>
                      <CardDescription className="mt-1">
                        {hazard.description}
                      </CardDescription>
                    </div>
                    <div className="bg-yellow-100 px-3 py-1 rounded-full text-xs font-medium text-yellow-800">
                      {hazard.type}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="pt-6">
                  {ccp ? (
                    <div className="space-y-6">
                      <div className="grid gap-6 md:grid-cols-2">
                        <div>
                          <h4 className="text-sm font-semibold mb-2">Critical Limits</h4>
                          <p className="text-sm">{ccp.criticalLimit}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold mb-2">Monitoring Procedure</h4>
                          <p className="text-sm">{ccp.monitoringProcedure}</p>
                        </div>
                      </div>

                      <Separator />

                      <div className="grid gap-6 md:grid-cols-2">
                        <div>
                          <h4 className="text-sm font-semibold mb-2">Monitoring Frequency</h4>
                          <p className="text-sm">{ccp.monitoringFrequency}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold mb-2">Corrective Action</h4>
                          <p className="text-sm">{ccp.correctiveAction}</p>
                        </div>
                      </div>

                      <Separator />

                      <div className="grid gap-6 md:grid-cols-2">
                        <div>
                          <h4 className="text-sm font-semibold mb-2">Verification Procedure</h4>
                          <p className="text-sm">{ccp.verificationProcedure}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold mb-2">Record-keeping Procedure</h4>
                          <p className="text-sm">{ccp.recordkeepingProcedure}</p>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <ThermometerSnowflake className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                      <h4 className="text-lg font-medium mb-2">CCP Details Not Defined</h4>
                      <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                        This hazard has been identified as a Critical Control Point, but monitoring and control details haven't been defined yet.
                      </p>
                      {canEdit && (
                        <Button onClick={() => openAddCCPForm(hazard)}>
                          Define CCP Details
                        </Button>
                      )}
                    </div>
                  )}
                </CardContent>
                {canEdit && ccp && (
                  <CardFooter className="bg-muted/20 border-t">
                    <Button variant="outline" size="sm" onClick={() => openAddCCPForm(hazard)}>
                      <Pencil className="h-4 w-4 mr-2" />
                      Edit CCP
                    </Button>
                  </CardFooter>
                )}
              </Card>
            );
          })}
        </div>
      )}

      {/* Add/Edit CCP Dialog */}
      <Dialog
        open={!!selectedHazard || !!selectedCCP}
        onOpenChange={(open) => !open && (setSelectedHazard(null), setSelectedCCP(null))}
      >
        <DialogContent className="max-w-3xl overflow-y-auto max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>
              {isEditingCCP ? 'Edit Critical Control Point' : 'Define Critical Control Point'}
            </DialogTitle>
          </DialogHeader>
          <div className="pr-1">

          <form onSubmit={handleSubmitCCP} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="criticalLimit">Critical Limits</Label>
                <Textarea
                  id="criticalLimit"
                  name="criticalLimit"
                  value={formValues.criticalLimit || ''}
                  onChange={handleFormChange}
                  placeholder="Specific measurable parameters that must be met to control the hazard"
                  className="h-20"
                  required
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="monitoringProcedure">Monitoring Procedure</Label>
                  <Textarea
                    id="monitoringProcedure"
                    name="monitoringProcedure"
                    value={formValues.monitoringProcedure || ''}
                    onChange={handleFormChange}
                    placeholder="How the critical limits will be measured and monitored"
                    className="h-20"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="monitoringFrequency">Monitoring Frequency</Label>
                  <Input
                    id="monitoringFrequency"
                    name="monitoringFrequency"
                    value={formValues.monitoringFrequency || ''}
                    onChange={handleFormChange}
                    placeholder="How often monitoring will occur"
                    required
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="correctiveAction">Corrective Actions</Label>
                <Textarea
                  id="correctiveAction"
                  name="correctiveAction"
                  value={formValues.correctiveAction || ''}
                  onChange={handleFormChange}
                  placeholder="Actions to be taken when critical limits are not met"
                  className="h-20"
                  required
                />
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <Label htmlFor="verificationProcedure">Verification Procedures</Label>
                  <Textarea
                    id="verificationProcedure"
                    name="verificationProcedure"
                    value={formValues.verificationProcedure || ''}
                    onChange={handleFormChange}
                    placeholder="Activities to verify the CCP is functioning as intended"
                    className="h-20"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="recordkeepingProcedure">Record-keeping Procedures</Label>
                  <Textarea
                    id="recordkeepingProcedure"
                    name="recordkeepingProcedure"
                    value={formValues.recordkeepingProcedure || ''}
                    onChange={handleFormChange}
                    placeholder="Documentation to maintain for this CCP"
                    className="h-20"
                    required
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setSelectedHazard(null);
                  setSelectedCCP(null);
                }}
              >
                Cancel
              </Button>
              <Button type="submit">
                {isEditingCCP ? 'Update CCP' : 'Save CCP'}
              </Button>
            </div>
          </form>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default CCPManagement;
