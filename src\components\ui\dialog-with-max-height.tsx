import * as React from "react"
import { DialogContent } from "@/components/ui/dialog"

interface DialogWithMaxHeightProps extends React.ComponentPropsWithoutRef<typeof DialogContent> {
  maxHeight?: string;
}

/**
 * A DialogContent component that supports a maxHeight prop
 * This is a workaround for the issue with the maxHeight prop in DialogContent
 */
export const DialogWithMaxHeight = React.forwardRef<
  React.ElementRef<typeof DialogContent>,
  DialogWithMaxHeightProps
>(({ className, children, maxHeight = "70vh", ...props }, ref) => {
  const combinedClassName = `${className || ""} max-h-[${maxHeight}] overflow-y-auto`;
  
  return (
    <DialogContent className={combinedClassName} {...props} ref={ref}>
      {children}
    </DialogContent>
  );
});

DialogWithMaxHeight.displayName = "DialogWithMaxHeight";
