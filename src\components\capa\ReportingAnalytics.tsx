import React, { useState, useEffect } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { CAPA, Claim, CorrectiveAction, PreventiveAction, VerificationActivity } from '@/models/capa';
import { Calendar, FileDown, FileText, Filter, Printer, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Line<PERSON>hart, Activity } from 'lucide-react';
import {
  ResponsiveContainer,
  LineChart as RechartsLineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as RechartsB<PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';

interface ReportingAnalyticsProps {
  capas: CAPA[];
  claims: Claim[];
  correctiveActions: CorrectiveAction[];
  preventiveActions: PreventiveAction[];
  verificationActivities: VerificationActivity[];
}

export function ReportingAnalytics({
  capas,
  claims,
  correctiveActions,
  preventiveActions,
  verificationActivities
}: ReportingAnalyticsProps) {
  // State for UI controls
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [reportType, setReportType] = useState('capaTrends');
  const [activeTab, setActiveTab] = useState('reports');
  const [chartData, setChartData] = useState<any[]>([]);

  // Filter CAPAs by date range
  const getFilteredCapas = () => {
    return capas.filter(capa => {
      if (!startDate && !endDate) return true;
      const capaDate = new Date(capa.dateIdentified);
      if (startDate && !endDate) return capaDate >= startDate;
      if (!startDate && endDate) return capaDate <= endDate;
      return capaDate >= (startDate as Date) && capaDate <= (endDate as Date);
    });
  };

  // Helper functions for trend analysis
  const getCapaVolumeData = () => {
    const filteredCapas = getFilteredCapas();
    const capasByMonth: Record<string, { count: number, closed: number }> = {};

    // Group by month
    filteredCapas.forEach(capa => {
      const date = new Date(capa.dateIdentified);
      const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

      if (!capasByMonth[monthYear]) {
        capasByMonth[monthYear] = { count: 0, closed: 0 };
      }
      capasByMonth[monthYear].count++;

      if (capa.status === 'closed') {
        capasByMonth[monthYear].closed++;
      }
    });

    // Convert to chart data format
    return Object.entries(capasByMonth)
      .map(([monthYear, data]) => ({
        month: monthYear,
        count: data.count,
        closed: data.closed
      }))
      .sort((a, b) => {
        const [aMonth, aYear] = a.month.split('/').map(Number);
        const [bMonth, bYear] = b.month.split('/').map(Number);

        if (aYear !== bYear) return aYear - bYear;
        return aMonth - bMonth;
      });
  };

  const getCapaTrend = (): number => {
    const data = getCapaVolumeData();
    if (data.length < 2) return 0;

    const current = data[data.length - 1].count;
    const previous = data[data.length - 2].count;

    if (previous === 0) return 100; // Avoid division by zero
    return Math.round(((current - previous) / previous) * 100);
  };

  const getResolutionTimeData = () => {
    const filteredCapas = getFilteredCapas().filter(capa => capa.status === 'closed' && capa.actualCompletionDate);
    const resolutionByMonth: Record<string, { totalDays: number, count: number }> = {};

    // Calculate resolution time and group by month
    filteredCapas.forEach(capa => {
      if (!capa.actualCompletionDate) return;

      const startDate = new Date(capa.dateIdentified);
      const endDate = new Date(capa.actualCompletionDate);
      const resolutionDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      const monthYear = `${startDate.getMonth() + 1}/${startDate.getFullYear()}`;

      if (!resolutionByMonth[monthYear]) {
        resolutionByMonth[monthYear] = { totalDays: 0, count: 0 };
      }

      resolutionByMonth[monthYear].totalDays += resolutionDays;
      resolutionByMonth[monthYear].count++;
    });

    // Convert to chart data format with average resolution time
    return Object.entries(resolutionByMonth)
      .map(([monthYear, data]) => ({
        month: monthYear,
        avgDays: Math.round(data.totalDays / data.count)
      }))
      .sort((a, b) => {
        const [aMonth, aYear] = a.month.split('/').map(Number);
        const [bMonth, bYear] = b.month.split('/').map(Number);

        if (aYear !== bYear) return aYear - bYear;
        return aMonth - bMonth;
      });
  };

  const getAvgResolutionTime = (): number => {
    const filteredCapas = getFilteredCapas().filter(capa => capa.status === 'closed' && capa.actualCompletionDate);
    if (filteredCapas.length === 0) return 0;

    let totalDays = 0;
    filteredCapas.forEach(capa => {
      if (!capa.actualCompletionDate) return;

      const startDate = new Date(capa.dateIdentified);
      const endDate = new Date(capa.actualCompletionDate);
      const resolutionDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      totalDays += resolutionDays;
    });

    return Math.round(totalDays / filteredCapas.length);
  };

  const getFastestResolutionTime = (): number => {
    const filteredCapas = getFilteredCapas().filter(capa => capa.status === 'closed' && capa.actualCompletionDate);
    if (filteredCapas.length === 0) return 0;

    let fastest = Number.MAX_SAFE_INTEGER;
    filteredCapas.forEach(capa => {
      if (!capa.actualCompletionDate) return;

      const startDate = new Date(capa.dateIdentified);
      const endDate = new Date(capa.actualCompletionDate);
      const resolutionDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      if (resolutionDays < fastest) {
        fastest = resolutionDays;
      }
    });

    return fastest === Number.MAX_SAFE_INTEGER ? 0 : fastest;
  };

  const getSlowestResolutionTime = (): number => {
    const filteredCapas = getFilteredCapas().filter(capa => capa.status === 'closed' && capa.actualCompletionDate);
    if (filteredCapas.length === 0) return 0;

    let slowest = 0;
    filteredCapas.forEach(capa => {
      if (!capa.actualCompletionDate) return;

      const startDate = new Date(capa.dateIdentified);
      const endDate = new Date(capa.actualCompletionDate);
      const resolutionDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

      if (resolutionDays > slowest) {
        slowest = resolutionDays;
      }
    });

    return slowest;
  };

  const getResolutionTrend = (): number => {
    const data = getResolutionTimeData();
    if (data.length < 2) return 0;

    const current = data[data.length - 1].avgDays;
    const previous = data[data.length - 2].avgDays;

    if (previous === 0) return 0; // Avoid division by zero
    return Math.round(((current - previous) / previous) * 100);
  };

  const getCategoryTrendData = () => {
    const filteredCapas = getFilteredCapas();
    if (filteredCapas.length === 0) return [];

    // Find the midpoint date to split into current and previous periods
    const dates = filteredCapas.map(capa => new Date(capa.dateIdentified).getTime()).sort((a, b) => a - b);
    const midpointIndex = Math.floor(dates.length / 2);
    const midpointDate = new Date(dates[midpointIndex]);

    // Group by category for current and previous periods
    const categoryData: Record<string, { current: number, previous: number }> = {};

    filteredCapas.forEach(capa => {
      const capaDate = new Date(capa.dateIdentified);
      const category = capa.category;

      if (!categoryData[category]) {
        categoryData[category] = { current: 0, previous: 0 };
      }

      if (capaDate >= midpointDate) {
        categoryData[category].current++;
      } else {
        categoryData[category].previous++;
      }
    });

    // Convert to chart data format
    return Object.entries(categoryData)
      .map(([category, data]) => ({
        category: category.replace(/_/g, ' '),
        current: data.current,
        previous: data.previous
      }))
      .sort((a, b) => b.current - a.current); // Sort by current period count
  };

  const getTopCategories = () => {
    const filteredCapas = getFilteredCapas();
    const categoryCounts: Record<string, number> = {};

    filteredCapas.forEach(capa => {
      const category = capa.category;

      if (!categoryCounts[category]) {
        categoryCounts[category] = 0;
      }

      categoryCounts[category]++;
    });

    // Convert to array and sort
    return Object.entries(categoryCounts)
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => b.count - a.count);
  };

  const getSourceTrendData = () => {
    const filteredCapas = getFilteredCapas();
    const sourceCounts: Record<string, number> = {};

    filteredCapas.forEach(capa => {
      const source = capa.source;

      if (!sourceCounts[source]) {
        sourceCounts[source] = 0;
      }

      sourceCounts[source]++;
    });

    // Colors for sources
    const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#d53e4f'];

    // Convert to chart data format
    return Object.entries(sourceCounts)
      .map(([source, count], index) => ({
        name: source.replace(/_/g, ' '),
        value: count,
        color: COLORS[index % COLORS.length]
      }))
      .sort((a, b) => b.value - a.value); // Sort by count
  };

  const getMostChangedSource = () => {
    const filteredCapas = getFilteredCapas();
    if (filteredCapas.length === 0) return { name: 'None', change: 0 };

    // Find the midpoint date to split into current and previous periods
    const dates = filteredCapas.map(capa => new Date(capa.dateIdentified).getTime()).sort((a, b) => a - b);
    const midpointIndex = Math.floor(dates.length / 2);
    const midpointDate = new Date(dates[midpointIndex]);

    // Group by source for current and previous periods
    const sourceData: Record<string, { current: number, previous: number }> = {};

    filteredCapas.forEach(capa => {
      const capaDate = new Date(capa.dateIdentified);
      const source = capa.source;

      if (!sourceData[source]) {
        sourceData[source] = { current: 0, previous: 0 };
      }

      if (capaDate >= midpointDate) {
        sourceData[source].current++;
      } else {
        sourceData[source].previous++;
      }
    });

    // Calculate percentage change for each source
    let mostChangedSource = '';
    let biggestChange = 0;

    Object.entries(sourceData).forEach(([source, data]) => {
      if (data.previous === 0) return; // Skip if no previous data

      const change = Math.round(((data.current - data.previous) / data.previous) * 100);

      if (Math.abs(change) > Math.abs(biggestChange)) {
        biggestChange = change;
        mostChangedSource = source;
      }
    });

    return {
      name: mostChangedSource.replace(/_/g, ' '),
      change: biggestChange
    };
  };

  // Filter verifications by date range
  const getFilteredVerifications = () => {
    return verificationActivities.filter(verification => {
      if (!startDate && !endDate) return true;
      const verificationDate = new Date(verification.date);
      if (startDate && !endDate) return verificationDate >= startDate;
      if (!startDate && endDate) return verificationDate <= endDate;
      return verificationDate >= (startDate as Date) && verificationDate <= (endDate as Date);
    });
  };

  // Calculate action implementation rate
  const getActionImplementationRate = (): number => {
    const totalActions = correctiveActions.length + preventiveActions.length;
    if (totalActions === 0) return 0;

    const completedActions =
      correctiveActions.filter(action => action.status === 'completed' || action.status === 'verified').length +
      preventiveActions.filter(action => action.status === 'completed' || action.status === 'verified').length;

    return Math.round((completedActions / totalActions) * 100);
  };

  // Calculate corrective action completion rate
  const getCorrectiveActionCompletionRate = (): number => {
    if (correctiveActions.length === 0) return 0;

    const completedActions = correctiveActions.filter(
      action => action.status === 'completed' || action.status === 'verified'
    ).length;

    return Math.round((completedActions / correctiveActions.length) * 100);
  };

  // Calculate preventive action completion rate
  const getPreventiveActionCompletionRate = (): number => {
    if (preventiveActions.length === 0) return 0;

    const completedActions = preventiveActions.filter(
      action => action.status === 'completed' || action.status === 'verified'
    ).length;

    return Math.round((completedActions / preventiveActions.length) * 100);
  };

  // Calculate verification success rate
  const getVerificationSuccessRate = (): number => {
    const filteredVerifications = getFilteredVerifications();
    if (filteredVerifications.length === 0) return 0;

    const passedVerifications = filteredVerifications.filter(
      verification => verification.result === 'pass'
    ).length;

    return Math.round((passedVerifications / filteredVerifications.length) * 100);
  };

  // Get CAPA status distribution for pie chart
  const getCAPAStatusDistribution = () => {
    const filteredCapas = getFilteredCapas();
    const statusCounts: Record<string, number> = {};

    filteredCapas.forEach(capa => {
      const status = capa.status;
      if (!statusCounts[status]) {
        statusCounts[status] = 0;
      }
      statusCounts[status]++;
    });

    // Status colors
    const statusColors: Record<string, string> = {
      open: '#3b82f6', // blue
      investigation: '#8b5cf6', // purple
      action_planned: '#f59e0b', // amber
      implementing: '#10b981', // emerald
      verification: '#6366f1', // indigo
      closed: '#22c55e', // green
      rejected: '#ef4444', // red
    };

    return Object.entries(statusCounts).map(([status, count]) => ({
      name: status.replace(/_/g, ' '),
      value: count,
      color: statusColors[status] || '#6b7280' // gray as fallback
    }));
  };

  // Get action completion by type (corrective vs preventive)
  const getActionCompletionByType = () => {
    const completedCA = correctiveActions.filter(
      action => action.status === 'completed' || action.status === 'verified'
    ).length;

    const pendingCA = correctiveActions.length - completedCA;

    const completedPA = preventiveActions.filter(
      action => action.status === 'completed' || action.status === 'verified'
    ).length;

    const pendingPA = preventiveActions.length - completedPA;

    return [
      {
        name: 'Corrective Actions',
        completed: completedCA,
        pending: pendingCA
      },
      {
        name: 'Preventive Actions',
        completed: completedPA,
        pending: pendingPA
      }
    ];
  };

  // Get timeliness data (on-time vs delayed completion)
  const getTimelinessData = () => {
    const filteredCapas = getFilteredCapas().filter(capa => capa.status === 'closed' && capa.actualCompletionDate);
    const timelinessData: Record<string, { onTime: number, delayed: number }> = {};

    filteredCapas.forEach(capa => {
      if (!capa.actualCompletionDate || !capa.targetCompletionDate) return;

      const actualDate = new Date(capa.actualCompletionDate);
      const targetDate = new Date(capa.targetCompletionDate);
      const monthYear = `${actualDate.getMonth() + 1}/${actualDate.getFullYear()}`;

      if (!timelinessData[monthYear]) {
        timelinessData[monthYear] = { onTime: 0, delayed: 0 };
      }

      if (actualDate <= targetDate) {
        timelinessData[monthYear].onTime++;
      } else {
        timelinessData[monthYear].delayed++;
      }
    });

    return Object.entries(timelinessData)
      .map(([month, data]) => ({
        month,
        onTime: data.onTime,
        delayed: data.delayed
      }))
      .sort((a, b) => {
        const [aMonth, aYear] = a.month.split('/').map(Number);
        const [bMonth, bYear] = b.month.split('/').map(Number);

        if (aYear !== bYear) return aYear - bYear;
        return aMonth - bMonth;
      });
  };

  // Get resolution time by priority
  const getResolutionTimeByPriority = () => {
    const filteredCapas = getFilteredCapas().filter(capa => capa.status === 'closed' && capa.actualCompletionDate);
    const priorityData: Record<string, { totalDays: number, count: number }> = {
      critical: { totalDays: 0, count: 0 },
      high: { totalDays: 0, count: 0 },
      medium: { totalDays: 0, count: 0 },
      low: { totalDays: 0, count: 0 }
    };

    // Target days by priority
    const targetDays = {
      critical: 7,
      high: 14,
      medium: 30,
      low: 60
    };

    filteredCapas.forEach(capa => {
      if (!capa.actualCompletionDate) return;

      const startDate = new Date(capa.dateIdentified);
      const endDate = new Date(capa.actualCompletionDate);
      const resolutionDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
      const priority = capa.priority;

      priorityData[priority].totalDays += resolutionDays;
      priorityData[priority].count++;
    });

    return Object.entries(priorityData)
      .filter(([_, data]) => data.count > 0)
      .map(([priority, data]) => ({
        name: priority.charAt(0).toUpperCase() + priority.slice(1),
        avgDays: Math.round(data.totalDays / data.count),
        target: targetDays[priority as keyof typeof targetDays]
      }))
      .sort((a, b) => {
        const priorityOrder = { Critical: 0, High: 1, Medium: 2, Low: 3 };
        return priorityOrder[a.name as keyof typeof priorityOrder] - priorityOrder[b.name as keyof typeof priorityOrder];
      });
  };

  // Get verification results over time
  const getVerificationResultsOverTime = () => {
    const filteredVerifications = getFilteredVerifications();
    const resultsByMonth: Record<string, { pass: number, fail: number }> = {};

    filteredVerifications.forEach(verification => {
      const date = new Date(verification.date);
      const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

      if (!resultsByMonth[monthYear]) {
        resultsByMonth[monthYear] = { pass: 0, fail: 0 };
      }

      if (verification.result === 'pass') {
        resultsByMonth[monthYear].pass++;
      } else {
        resultsByMonth[monthYear].fail++;
      }
    });

    return Object.entries(resultsByMonth)
      .map(([month, data]) => ({
        month,
        pass: data.pass,
        fail: data.fail
      }))
      .sort((a, b) => {
        const [aMonth, aYear] = a.month.split('/').map(Number);
        const [bMonth, bYear] = b.month.split('/').map(Number);

        if (aYear !== bYear) return aYear - bYear;
        return aMonth - bMonth;
      });
  };

  // Get recurring issues by category
  const getRecurringIssuesByCategory = () => {
    const filteredCapas = getFilteredCapas();
    const categoryCount: Record<string, number> = {};

    // Count CAPAs by category
    filteredCapas.forEach(capa => {
      const category = capa.category;
      if (!categoryCount[category]) {
        categoryCount[category] = 0;
      }
      categoryCount[category]++;
    });

    // Consider categories with more than 2 CAPAs as potentially recurring
    return Object.entries(categoryCount)
      .filter(([_, count]) => count > 2)
      .map(([category, count]) => ({
        name: category.replace(/_/g, ' '),
        count
      }))
      .sort((a, b) => b.count - a.count);
  };

  // Process data for charts based on report type
  useEffect(() => {
    const filteredCapas = getFilteredCapas();

    if (reportType === 'capaTrends') {
      // Group CAPAs by month
      const capasByMonth: Record<string, number> = {};

      // Group by month
      filteredCapas.forEach(capa => {
        const date = new Date(capa.dateIdentified);
        const monthYear = `${date.getMonth() + 1}/${date.getFullYear()}`;

        if (!capasByMonth[monthYear]) {
          capasByMonth[monthYear] = 0;
        }
        capasByMonth[monthYear]++;
      });

      // Convert to chart data format
      const data = Object.entries(capasByMonth)
        .map(([monthYear, count]) => ({
          month: monthYear,
          count
        }))
        .sort((a, b) => {
          const [aMonth, aYear] = a.month.split('/').map(Number);
          const [bMonth, bYear] = b.month.split('/').map(Number);

          if (aYear !== bYear) return aYear - bYear;
          return aMonth - bMonth;
        });

      setChartData(data);
    }
    else if (reportType === 'categoryDistribution') {
      // Group CAPAs by category
      const capasByCategory: Record<string, number> = {};

      filteredCapas.forEach(capa => {
        const category = capa.category;

        if (!capasByCategory[category]) {
          capasByCategory[category] = 0;
        }
        capasByCategory[category]++;
      });

      // Convert to chart data format with colors
      const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658'];

      const data = Object.entries(capasByCategory)
        .map(([category, count], index) => ({
          name: category.replace(/_/g, ' '),
          value: count,
          color: COLORS[index % COLORS.length]
        }));

      setChartData(data);
    }
    else if (reportType === 'statusBreakdown') {
      // Group CAPAs by status
      const capasByStatus: Record<string, number> = {};

      filteredCapas.forEach(capa => {
        const status = capa.status;

        if (!capasByStatus[status]) {
          capasByStatus[status] = 0;
        }
        capasByStatus[status]++;
      });

      // Status colors
      const statusColors: Record<string, string> = {
        open: '#3b82f6', // blue
        investigation: '#8b5cf6', // purple
        action_planned: '#f59e0b', // amber
        implementing: '#10b981', // emerald
        verification: '#6366f1', // indigo
        closed: '#22c55e', // green
        rejected: '#ef4444', // red
      };

      // Convert to chart data format
      const data = Object.entries(capasByStatus)
        .map(([status, count]) => ({
          name: status.replace(/_/g, ' '),
          value: count,
          fill: statusColors[status] || '#6b7280' // gray as fallback
        }));

      setChartData(data);
    }
  }, [capas, reportType, startDate, endDate]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">CAPA Reporting & Analytics</h2>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Export PDF
          </Button>
          <Button variant="outline" size="sm">
            <FileDown className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button variant="outline" size="sm">
            <Printer className="h-4 w-4 mr-2" />
            Print
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <BarChart className="h-4 w-4" />
            <span>Standard Reports</span>
          </TabsTrigger>
          <TabsTrigger value="trends" className="flex items-center gap-2">
            <LineChart className="h-4 w-4" />
            <span>Trend Analysis</span>
          </TabsTrigger>
          <TabsTrigger value="metrics" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            <span>Performance Metrics</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="reports" className="space-y-4 mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Report Configuration</CardTitle>
              <CardDescription>
                Configure your report parameters
              </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <label
                    htmlFor="date"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Date Range
                  </label>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <DatePicker
                      selectsRange
                      startDate={startDate}
                      endDate={endDate}
                      onChange={(update: [Date | null, Date | null]) => {
                        setStartDate(update[0]);
                        setEndDate(update[1]);
                      }}
                      isClearable
                      placeholderText="Select Date Range"
                      className="w-full"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Report Type</label>
                  <Select value={reportType} onValueChange={setReportType}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select Report Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="capaTrends">CAPA Trends</SelectItem>
                      <SelectItem value="categoryDistribution">Category Distribution</SelectItem>
                      <SelectItem value="statusBreakdown">Status Breakdown</SelectItem>
                      <SelectItem value="priorityBreakdown">Priority Breakdown</SelectItem>
                      <SelectItem value="effectivenessMetrics">Effectiveness Metrics</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Filter By</label>
                  <div className="flex space-x-2">
                    <Select defaultValue="all">
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Filter Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All CAPAs</SelectItem>
                        <SelectItem value="priority">Priority</SelectItem>
                        <SelectItem value="status">Status</SelectItem>
                        <SelectItem value="category">Category</SelectItem>
                        <SelectItem value="source">Source</SelectItem>
                      </SelectContent>
                    </Select>
                    <Button variant="outline" size="icon">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="mt-4 p-6 bg-background rounded-md h-80">
                {chartData.length > 0 ? (
                  <>
                    {reportType === 'capaTrends' && (
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsLineChart
                          data={chartData}
                          margin={{
                            top: 5,
                            right: 30,
                            left: 20,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="count"
                            name="Number of CAPAs"
                            stroke="#8884d8"
                            activeDot={{ r: 8 }}
                          />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    )}

                    {reportType === 'categoryDistribution' && (
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={chartData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {chartData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    )}

                    {reportType === 'statusBreakdown' && (
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsBarChart
                          data={chartData}
                          layout="vertical"
                          margin={{
                            top: 5,
                            right: 30,
                            left: 100,
                            bottom: 5,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis type="number" />
                          <YAxis type="category" dataKey="name" />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="value" name="Number of CAPAs">
                            {chartData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.fill} />
                            ))}
                          </Bar>
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    )}
                  </>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <PieChart className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <p className="text-sm font-medium">Select report parameters above to generate a report</p>
                      <p className="text-xs text-muted-foreground mt-1">Charts and data will appear here</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button
                onClick={() => {
                  // Force refresh of the current report type to regenerate the chart
                  const currentType = reportType;
                  setReportType('');
                  setTimeout(() => setReportType(currentType), 10);
                }}
                disabled={chartData.length > 0}
              >
                {chartData.length > 0 ? 'Report Generated' : 'Generate Report'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>Trend Analysis</CardTitle>
                <CardDescription>
                  Analyze CAPA trends over time
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <DatePicker
                  selectsRange
                  startDate={startDate}
                  endDate={endDate}
                  onChange={(update: [Date | null, Date | null]) => {
                    setStartDate(update[0]);
                    setEndDate(update[1]);
                  }}
                  isClearable
                  placeholderText="Filter by Date Range"
                  className="w-[200px]"
                />
                <Button variant="outline" size="sm" onClick={() => {
                  // Force refresh of trend data
                  const currentTab = activeTab;
                  setActiveTab('reports');
                  setTimeout(() => setActiveTab(currentTab), 10);
                }}>
                  <Filter className="h-4 w-4 mr-2" />
                  Apply
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="capaOverTime" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="capaOverTime">CAPA Volume</TabsTrigger>
                  <TabsTrigger value="resolutionTime">Resolution Time</TabsTrigger>
                  <TabsTrigger value="categoryTrends">Category Trends</TabsTrigger>
                  <TabsTrigger value="sourceTrends">Source Trends</TabsTrigger>
                </TabsList>

                {/* CAPA Volume Over Time */}
                <TabsContent value="capaOverTime" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="col-span-3 bg-background rounded-md p-4 h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsLineChart
                          data={getCapaVolumeData()}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 20,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Line
                            type="monotone"
                            dataKey="count"
                            name="Number of CAPAs"
                            stroke="#8884d8"
                            activeDot={{ r: 8 }}
                          />
                          <Line
                            type="monotone"
                            dataKey="closed"
                            name="Closed CAPAs"
                            stroke="#82ca9d"
                            activeDot={{ r: 8 }}
                          />
                        </RechartsLineChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="bg-background rounded-md p-4">
                      <h3 className="text-lg font-medium mb-4">Summary</h3>
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Total CAPAs</p>
                          <p className="text-2xl font-bold">{capas.length}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Open CAPAs</p>
                          <p className="text-2xl font-bold">{capas.filter(c => c.status !== 'closed' && c.status !== 'rejected').length}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Closed CAPAs</p>
                          <p className="text-2xl font-bold">{capas.filter(c => c.status === 'closed').length}</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Trend</p>
                          <div className="flex items-center">
                            <div className={`flex items-center ${getCapaTrend() > 0 ? 'text-red-500' : 'text-green-500'}`}>
                              {getCapaTrend() > 0 ? (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                                </svg>
                              ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
                                </svg>
                              )}
                              <span className="ml-1 font-medium">{Math.abs(getCapaTrend())}%</span>
                            </div>
                            <span className="ml-2 text-sm text-muted-foreground">vs previous period</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Resolution Time Trends */}
                <TabsContent value="resolutionTime" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="col-span-3 bg-background rounded-md p-4 h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsBarChart
                          data={getResolutionTimeData()}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 20,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="month" />
                          <YAxis label={{ value: 'Days', angle: -90, position: 'insideLeft' }} />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="avgDays" name="Avg. Resolution Time (Days)" fill="#8884d8" />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="bg-background rounded-md p-4">
                      <h3 className="text-lg font-medium mb-4">Resolution Metrics</h3>
                      <div className="space-y-4">
                        <div>
                          <p className="text-sm text-muted-foreground">Avg. Resolution Time</p>
                          <p className="text-2xl font-bold">{getAvgResolutionTime()} days</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Fastest Resolution</p>
                          <p className="text-2xl font-bold">{getFastestResolutionTime()} days</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Slowest Resolution</p>
                          <p className="text-2xl font-bold">{getSlowestResolutionTime()} days</p>
                        </div>
                        <div>
                          <p className="text-sm text-muted-foreground">Trend</p>
                          <div className="flex items-center">
                            <div className={`flex items-center ${getResolutionTrend() > 0 ? 'text-red-500' : 'text-green-500'}`}>
                              {getResolutionTrend() > 0 ? (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                                </svg>
                              ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
                                </svg>
                              )}
                              <span className="ml-1 font-medium">{Math.abs(getResolutionTrend())}%</span>
                            </div>
                            <span className="ml-2 text-sm text-muted-foreground">vs previous period</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Category Trends */}
                <TabsContent value="categoryTrends" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="col-span-3 bg-background rounded-md p-4 h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsBarChart
                          data={getCategoryTrendData()}
                          margin={{
                            top: 20,
                            right: 30,
                            left: 20,
                            bottom: 20,
                          }}
                        >
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="category" />
                          <YAxis />
                          <Tooltip />
                          <Legend />
                          <Bar dataKey="current" name="Current Period" fill="#8884d8" />
                          <Bar dataKey="previous" name="Previous Period" fill="#82ca9d" />
                        </RechartsBarChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="bg-background rounded-md p-4">
                      <h3 className="text-lg font-medium mb-4">Top Categories</h3>
                      <div className="space-y-4">
                        {getTopCategories().map((category, index) => (
                          <div key={index}>
                            <div className="flex justify-between items-center">
                              <p className="text-sm font-medium">{category.name.replace(/_/g, ' ')}</p>
                              <p className="text-sm font-bold">{category.count}</p>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                              <div
                                className="bg-blue-600 h-2.5 rounded-full"
                                style={{ width: `${(category.count / getTopCategories()[0].count) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                {/* Source Trends */}
                <TabsContent value="sourceTrends" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="col-span-3 bg-background rounded-md p-4 h-80">
                      <ResponsiveContainer width="100%" height="100%">
                        <RechartsPieChart>
                          <Pie
                            data={getSourceTrendData()}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                            nameKey="name"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {getSourceTrendData().map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip />
                          <Legend />
                        </RechartsPieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="bg-background rounded-md p-4">
                      <h3 className="text-lg font-medium mb-4">Source Analysis</h3>
                      <div className="space-y-4">
                        {getSourceTrendData().slice(0, 5).map((source, index) => (
                          <div key={index}>
                            <div className="flex justify-between items-center">
                              <p className="text-sm font-medium">{source.name}</p>
                              <p className="text-sm font-bold">{source.value}</p>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                              <div
                                className="h-2.5 rounded-full"
                                style={{
                                  width: `${(source.value / getSourceTrendData()[0].value) * 100}%`,
                                  backgroundColor: source.color
                                }}
                              ></div>
                            </div>
                          </div>
                        ))}
                        <div className="pt-2 mt-2 border-t">
                          <p className="text-sm text-muted-foreground">Most significant change:</p>
                          <div className="flex items-center mt-1">
                            <p className="text-sm font-medium">{getMostChangedSource().name}</p>
                            <div className={`flex items-center ml-2 ${getMostChangedSource().change > 0 ? 'text-green-500' : 'text-red-500'}`}>
                              {getMostChangedSource().change > 0 ? (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                                </svg>
                              ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
                                </svg>
                              )}
                              <span className="ml-1 font-medium">{Math.abs(getMostChangedSource().change)}%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="flex justify-between">
              <p className="text-sm text-muted-foreground">
                Data based on {getFilteredCapas().length} CAPAs from {startDate ? new Date(startDate).toLocaleDateString() : 'all time'}
                to {endDate ? new Date(endDate).toLocaleDateString() : 'present'}
              </p>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4 mt-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>
                  Key performance indicators for CAPA management
                </CardDescription>
              </div>
              <div className="flex items-center space-x-2">
                <DatePicker
                  selectsRange
                  startDate={startDate}
                  endDate={endDate}
                  onChange={(update: [Date | null, Date | null]) => {
                    setStartDate(update[0]);
                    setEndDate(update[1]);
                  }}
                  isClearable
                  placeholderText="Filter by Date Range"
                  className="w-[200px]"
                />
                <Button variant="outline" size="sm" onClick={() => {
                  // Force refresh of metrics data
                  const currentTab = activeTab;
                  setActiveTab('reports');
                  setTimeout(() => setActiveTab(currentTab), 10);
                }}>
                  <Filter className="h-4 w-4 mr-2" />
                  Apply
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* KPI Summary Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {/* CAPA Completion Rate */}
                <Card className="bg-background">
                  <CardContent className="p-4">
                    <div className="flex flex-col space-y-2">
                      <p className="text-sm text-muted-foreground">CAPA Completion Rate</p>
                      <div className="flex items-end justify-between">
                        <p className="text-2xl font-bold">
                          {Math.round((getFilteredCapas().filter(capa => capa.status === 'closed').length /
                            (getFilteredCapas().length || 1)) * 100)}%
                        </p>
                        <div className="h-8 w-24">
                          <ResponsiveContainer width="100%" height="100%">
                            <RechartsLineChart data={getCapaVolumeData().slice(-5)}>
                              <Line
                                type="monotone"
                                dataKey="closed"
                                stroke="#22c55e"
                                strokeWidth={2}
                                dot={false}
                              />
                            </RechartsLineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                        <div
                          className="bg-green-600 h-2.5 rounded-full"
                          style={{ width: `${Math.round((getFilteredCapas().filter(capa => capa.status === 'closed').length /
                            (getFilteredCapas().length || 1)) * 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Average Resolution Time */}
                <Card className="bg-background">
                  <CardContent className="p-4">
                    <div className="flex flex-col space-y-2">
                      <p className="text-sm text-muted-foreground">Avg. Resolution Time</p>
                      <div className="flex items-end justify-between">
                        <p className="text-2xl font-bold">
                          {getAvgResolutionTime()} days
                        </p>
                        <div className="h-8 w-24">
                          <ResponsiveContainer width="100%" height="100%">
                            <RechartsLineChart data={getResolutionTimeData().slice(-5)}>
                              <Line
                                type="monotone"
                                dataKey="avgDays"
                                stroke="#8884d8"
                                strokeWidth={2}
                                dot={false}
                              />
                            </RechartsLineChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className={`flex items-center ${getResolutionTrend() > 0 ? 'text-red-500' : 'text-green-500'}`}>
                          {getResolutionTrend() > 0 ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M12 13a1 1 0 100 2h5a1 1 0 001-1v-5a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586l-4.293-4.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z" clipRule="evenodd" />
                            </svg>
                          )}
                          <span className="ml-1 text-xs font-medium">{Math.abs(getResolutionTrend())}%</span>
                        </div>
                        <span className="ml-2 text-xs text-muted-foreground">vs previous period</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Action Implementation Rate */}
                <Card className="bg-background">
                  <CardContent className="p-4">
                    <div className="flex flex-col space-y-2">
                      <p className="text-sm text-muted-foreground">Action Implementation Rate</p>
                      <div className="flex items-end justify-between">
                        <p className="text-2xl font-bold">
                          {getActionImplementationRate()}%
                        </p>
                        <div className="h-8 w-24">
                          <ResponsiveContainer width="100%" height="100%">
                            <RechartsBarChart data={[
                              { name: 'CA', value: getCorrectiveActionCompletionRate() },
                              { name: 'PA', value: getPreventiveActionCompletionRate() }
                            ]}>
                              <Bar dataKey="value" fill="#3b82f6" />
                            </RechartsBarChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                        <div
                          className="bg-blue-600 h-2.5 rounded-full"
                          style={{ width: `${getActionImplementationRate()}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Verification Success Rate */}
                <Card className="bg-background">
                  <CardContent className="p-4">
                    <div className="flex flex-col space-y-2">
                      <p className="text-sm text-muted-foreground">Verification Success Rate</p>
                      <div className="flex items-end justify-between">
                        <p className="text-2xl font-bold">
                          {getVerificationSuccessRate()}%
                        </p>
                        <div className="h-8 w-24">
                          <ResponsiveContainer width="100%" height="100%">
                            <RechartsPieChart>
                              <Pie
                                data={[
                                  { name: 'Success', value: getFilteredVerifications().filter(v => v.result === 'pass').length, fill: '#22c55e' },
                                  { name: 'Failure', value: getFilteredVerifications().filter(v => v.result === 'fail').length, fill: '#ef4444' }
                                ]}
                                cx="50%"
                                cy="50%"
                                innerRadius={15}
                                outerRadius={25}
                                dataKey="value"
                              />
                            </RechartsPieChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5 mt-1">
                        <div
                          className="bg-green-600 h-2.5 rounded-full"
                          style={{ width: `${getVerificationSuccessRate()}%` }}
                        ></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Detailed Metrics */}
              <Tabs defaultValue="completion" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="completion">Completion Metrics</TabsTrigger>
                  <TabsTrigger value="timeliness">Timeliness Metrics</TabsTrigger>
                  <TabsTrigger value="effectiveness">Effectiveness Metrics</TabsTrigger>
                </TabsList>

                {/* Completion Metrics */}
                <TabsContent value="completion" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* CAPA Status Distribution */}
                    <Card className="bg-background">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-md">CAPA Status Distribution</CardTitle>
                      </CardHeader>
                      <CardContent className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsPieChart>
                            <Pie
                              data={getCAPAStatusDistribution()}
                              cx="50%"
                              cy="50%"
                              labelLine={true}
                              outerRadius={100}
                              fill="#8884d8"
                              dataKey="value"
                              nameKey="name"
                              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            >
                              {getCAPAStatusDistribution().map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                            <Tooltip />
                            <Legend />
                          </RechartsPieChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* Action Completion by Type */}
                    <Card className="bg-background">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-md">Action Completion by Type</CardTitle>
                      </CardHeader>
                      <CardContent className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsBarChart
                            data={getActionCompletionByType()}
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 20,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="completed" name="Completed" stackId="a" fill="#22c55e" />
                            <Bar dataKey="pending" name="Pending" stackId="a" fill="#f59e0b" />
                          </RechartsBarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Timeliness Metrics */}
                <TabsContent value="timeliness" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* On-Time vs. Delayed Completion */}
                    <Card className="bg-background">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-md">On-Time vs. Delayed Completion</CardTitle>
                      </CardHeader>
                      <CardContent className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsBarChart
                            data={getTimelinessData()}
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 20,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="onTime" name="On Time" stackId="a" fill="#22c55e" />
                            <Bar dataKey="delayed" name="Delayed" stackId="a" fill="#ef4444" />
                          </RechartsBarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* Average Days to Complete by Priority */}
                    <Card className="bg-background">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-md">Avg. Days to Complete by Priority</CardTitle>
                      </CardHeader>
                      <CardContent className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsBarChart
                            data={getResolutionTimeByPriority()}
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 20,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="name" />
                            <YAxis label={{ value: 'Days', angle: -90, position: 'insideLeft' }} />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="avgDays" name="Avg. Days" fill="#8884d8" />
                            <Bar dataKey="target" name="Target" fill="#82ca9d" />
                          </RechartsBarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                {/* Effectiveness Metrics */}
                <TabsContent value="effectiveness" className="space-y-4 mt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Verification Results */}
                    <Card className="bg-background">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-md">Verification Results Over Time</CardTitle>
                      </CardHeader>
                      <CardContent className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsLineChart
                            data={getVerificationResultsOverTime()}
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 20,
                            }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="month" />
                            <YAxis />
                            <Tooltip />
                            <Legend />
                            <Line type="monotone" dataKey="pass" name="Pass" stroke="#22c55e" />
                            <Line type="monotone" dataKey="fail" name="Fail" stroke="#ef4444" />
                          </RechartsLineChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* Recurring Issues */}
                    <Card className="bg-background">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-md">Recurring Issues by Category</CardTitle>
                      </CardHeader>
                      <CardContent className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsBarChart
                            data={getRecurringIssuesByCategory()}
                            margin={{
                              top: 20,
                              right: 30,
                              left: 20,
                              bottom: 20,
                            }}
                            layout="vertical"
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis type="number" />
                            <YAxis type="category" dataKey="name" />
                            <Tooltip />
                            <Legend />
                            <Bar dataKey="count" name="Recurring Issues" fill="#f59e0b" />
                          </RechartsBarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter className="flex justify-between">
              <p className="text-sm text-muted-foreground">
                Metrics based on {getFilteredCapas().length} CAPAs from {startDate ? new Date(startDate).toLocaleDateString() : 'all time'}
                to {endDate ? new Date(endDate).toLocaleDateString() : 'present'}
              </p>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Printer className="h-4 w-4 mr-2" />
                  Print
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
