import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  FileText, 
  ExternalLink, 
  Unlink, 
  Link, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Calendar,
  User,
  Target,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CAPA, Claim } from '@/models/capa';
import { toast } from '@/hooks/use-toast';

interface CAPALinkDisplayProps {
  claim: Claim;
  linkedCapa?: CAPA;
  availableCapas: CAPA[];
  onNavigateToCAPA?: (capaId: string) => void;
  onCreateCAPA?: (claim: Claim) => void;
  onLinkCAPA?: (claimId: string, capaId: string) => void;
  onUnlinkCAPA?: (claimId: string) => void;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function CAPALinkDisplay({
  claim,
  linkedCapa,
  availableCapas,
  onNavigateToCAPA,
  onCreateCAPA,
  onLinkCAPA,
  onUnlinkCAPA,
  className,
  size = 'md'
}: CAPALinkDisplayProps) {
  const [isLinkDialogOpen, setIsLinkDialogOpen] = useState(false);
  const [selectedCapaId, setSelectedCapaId] = useState<string>('');

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'open': { variant: 'default', color: 'bg-blue-500', icon: Clock },
      'investigation': { variant: 'default', color: 'bg-yellow-500', icon: AlertCircle },
      'action_planned': { variant: 'default', color: 'bg-orange-500', icon: Calendar },
      'implementing': { variant: 'default', color: 'bg-purple-500', icon: TrendingUp },
      'verification': { variant: 'default', color: 'bg-indigo-500', icon: CheckCircle },
      'closed': { variant: 'default', color: 'bg-green-600', icon: CheckCircle },
      'rejected': { variant: 'outline', color: 'border-red-500 text-red-500', icon: AlertCircle }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.open;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant as any} className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      'critical': { variant: 'destructive', icon: AlertCircle },
      'high': { variant: 'default', color: 'bg-orange-500', icon: TrendingUp },
      'medium': { variant: 'default', color: 'bg-yellow-500', icon: Clock },
      'low': { variant: 'outline', icon: Clock }
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant as any} className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {priority.charAt(0).toUpperCase() + priority.slice(1)}
      </Badge>
    );
  };

  const handleLinkCAPA = () => {
    if (!selectedCapaId) {
      toast({
        title: "Error",
        description: "Please select a CAPA to link",
        variant: "destructive"
      });
      return;
    }

    onLinkCAPA?.(claim.id, selectedCapaId);
    setIsLinkDialogOpen(false);
    setSelectedCapaId('');
    
    toast({
      title: "Success",
      description: "CAPA linked to claim successfully"
    });
  };

  const handleUnlinkCAPA = () => {
    onUnlinkCAPA?.(claim.id);
    toast({
      title: "Success",
      description: "CAPA unlinked from claim"
    });
  };

  // Filter available CAPAs (exclude already linked ones and show relevant ones)
  const availableCapasForLinking = availableCapas.filter(capa => 
    capa.id !== claim.capaId && 
    (capa.productId === claim.productId || !capa.claimId)
  );

  if (size === 'sm') {
    // Compact display for table cells
    return (
      <div className={cn("flex items-center space-x-2", className)}>
        {linkedCapa ? (
          <>
            <div className="flex items-center space-x-1">
              {getStatusBadge(linkedCapa.status)}
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={() => onNavigateToCAPA?.(linkedCapa.id)}
                title="View CAPA Details"
              >
                <ExternalLink className="h-3 w-3" />
              </Button>
            </div>
          </>
        ) : (
          <div className="flex items-center space-x-1">
            <Badge variant="outline" className="border-muted-foreground text-muted-foreground">
              No CAPA
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => setIsLinkDialogOpen(true)}
              title="Link to CAPA"
            >
              <Link className="h-3 w-3" />
            </Button>
          </div>
        )}

        {/* Link/Unlink Dialog */}
        <Dialog open={isLinkDialogOpen} onOpenChange={setIsLinkDialogOpen}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Link CAPA to Claim</DialogTitle>
              <DialogDescription>
                Select an existing CAPA to link to this claim, or create a new one.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Select CAPA</label>
                <Select value={selectedCapaId} onValueChange={setSelectedCapaId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a CAPA to link" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableCapasForLinking.map((capa) => (
                      <SelectItem key={capa.id} value={capa.id}>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{capa.title}</span>
                          <Badge variant="outline" className="text-xs">
                            {capa.status}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {availableCapasForLinking.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  <FileText className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No available CAPAs to link</p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsLinkDialogOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={() => onCreateCAPA?.(claim)}
                variant="outline"
              >
                <FileText className="h-4 w-4 mr-2" />
                Create New CAPA
              </Button>
              <Button 
                onClick={handleLinkCAPA}
                disabled={!selectedCapaId}
              >
                <Link className="h-4 w-4 mr-2" />
                Link CAPA
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }

  // Full display for detailed views
  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center justify-between">
          <span>Linked CAPA</span>
          {linkedCapa && (
            <Button
              variant="ghost"
              size="sm"
              onClick={handleUnlinkCAPA}
              className="text-muted-foreground hover:text-destructive"
            >
              <Unlink className="h-4 w-4" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {linkedCapa ? (
          <div className="space-y-3">
            <div className="flex items-start justify-between">
              <div className="space-y-1">
                <p className="font-medium text-sm">{linkedCapa.title}</p>
                <p className="text-xs text-muted-foreground">ID: {linkedCapa.id}</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onNavigateToCAPA?.(linkedCapa.id)}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View
              </Button>
            </div>

            <Separator />

            <div className="grid grid-cols-2 gap-3 text-xs">
              <div>
                <p className="text-muted-foreground mb-1">Status</p>
                {getStatusBadge(linkedCapa.status)}
              </div>
              <div>
                <p className="text-muted-foreground mb-1">Priority</p>
                {getPriorityBadge(linkedCapa.priority)}
              </div>
              <div>
                <p className="text-muted-foreground mb-1">Responsible</p>
                <div className="flex items-center">
                  <User className="h-3 w-3 mr-1" />
                  <span>{linkedCapa.responsiblePerson}</span>
                </div>
              </div>
              <div>
                <p className="text-muted-foreground mb-1">Target Date</p>
                <div className="flex items-center">
                  <Target className="h-3 w-3 mr-1" />
                  <span>{format(linkedCapa.targetCompletionDate, 'MMM d, yyyy')}</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-4">
            <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
            <p className="text-sm text-muted-foreground mb-3">No CAPA linked to this claim</p>
            <div className="flex justify-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsLinkDialogOpen(true)}
              >
                <Link className="h-4 w-4 mr-2" />
                Link Existing
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() => onCreateCAPA?.(claim)}
              >
                <FileText className="h-4 w-4 mr-2" />
                Create New
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
