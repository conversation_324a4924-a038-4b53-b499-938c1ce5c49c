
import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Save, AlertCircle, FileDown, FileText, Network, List } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { getProducts, getCCPsForProduct, getProcessSteps, getHazardsForProduct } from '@/services/planService';
import { Product, ProcessStep, HACCPPlan } from '@/models/types';
import { InteractiveProcessFlow } from './InteractiveProcessFlow';
import { ReactFlowProcessDiagram } from './ReactFlowProcessDiagram';
import { generateHACCPPlanPDF } from '@/services/pdfExportService';
import { generateEnhancedHACCPPlanPDF } from '@/services/enhancedPdfExportService';

interface PreviewPlanTabProps {
  planName: string;
  planVersion: string;
  selectedProduct: string;
  currentPlan: HACCPPlan | null;
  ccpData: any[];
  hazardData?: any[];
  setActiveTab: (tab: string) => void;
  handleSavePlan: () => void;
}

export const PreviewPlanTab: React.FC<PreviewPlanTabProps> = ({
  planName,
  planVersion,
  selectedProduct,
  currentPlan,
  ccpData,
  hazardData = [],
  setActiveTab,
  handleSavePlan
}) => {
  const [productDetails, setProductDetails] = useState<Product | null>(null);
  const [processSteps, setProcessSteps] = useState<ProcessStep[]>([]);
  const [missingCCPs, setMissingCCPs] = useState(false);
  const [hazards, setHazards] = useState<any[]>(hazardData);
  const [viewMode, setViewMode] = useState<'diagram' | 'list'>('diagram');

  // Load product details and process steps on component mount
  useEffect(() => {
    // Get product details
    const products = getProducts();
    const product = products.find(p => p.id === selectedProduct);
    setProductDetails(product || null);

    // Get process steps
    const steps = getProcessSteps();
    setProcessSteps(steps);

    // Set hazards from props or get them if not provided
    if (hazardData && hazardData.length > 0) {
      setHazards(hazardData);
    } else {
      const productHazards = getHazardsForProduct(selectedProduct);
      setHazards(productHazards);
    }

    // Check if CCPs are missing
    setMissingCCPs(ccpData.length === 0);
  }, [selectedProduct, ccpData, hazardData]);

  // Get plan status
  const getPlanStatus = () => {
    if (!currentPlan) return 'Draft';
    return currentPlan.status.charAt(0).toUpperCase() + currentPlan.status.slice(1).replace('_', ' ');
  };

  // Export PDF function
  const handleExportPDF = () => {
    try {
      const pdf = generateHACCPPlanPDF(
        planName,
        planVersion,
        selectedProduct,
        currentPlan,
        productDetails,
        ccpData,
        hazards
      );

      pdf.save(`${planName.replace(/\s+/g, '_')}_v${planVersion}.pdf`);
    } catch (error) {
      console.error('Error exporting PDF:', error);
    }
  };

  // Export Enhanced PDF function
  const handleExportEnhancedPDF = () => {
    try {
      const pdf = generateEnhancedHACCPPlanPDF(
        planName,
        planVersion,
        selectedProduct,
        currentPlan,
        productDetails,
        ccpData,
        hazards,
        {
          includeCoverPage: true,
          companyName: 'HACCP Plan Pilot'
        }
      );

      pdf.save(`${planName.replace(/\s+/g, '_')}_v${planVersion}_enhanced.pdf`);
    } catch (error) {
      console.error('Error exporting enhanced PDF:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>HACCP Plan Preview</CardTitle>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={handleExportPDF}>
              <FileDown className="h-4 w-4 mr-2" />
              Export PDF
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportEnhancedPDF} className="bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800 border-blue-200">
              <FileText className="h-4 w-4 mr-2" />
              Enhanced PDF
            </Button>
            <div className="text-sm bg-blue-100 text-blue-800 px-3 py-1 rounded-full">
              {getPlanStatus()}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {missingCCPs && (
          <Alert variant="warning">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Warning: No Critical Control Points Defined</AlertTitle>
            <AlertDescription>
              This HACCP plan does not have any Critical Control Points defined. A valid HACCP plan requires at least one CCP to control significant hazards.
              Consider returning to Hazard Analysis to identify and define CCPs.
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-2">
          <h2 className="text-xl font-bold">{planName}</h2>
          <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
            <div>Version: {planVersion}</div>
            <div>Created: {new Date().toLocaleDateString()}</div>
            <div>Product: {productDetails?.name || 'Unknown Product'}</div>
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">1. Product Description</h3>
          <p>
            {productDetails?.description || 'No product description available.'}
            {!productDetails?.description && (
              <span className="block mt-2 text-yellow-600">
                Please add a detailed product description including packaging, storage conditions, and intended use.
              </span>
            )}
          </p>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">2. Process Flow</h3>
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'diagram' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('diagram')}
                className="flex items-center gap-2"
              >
                <Network className="h-4 w-4" />
                Diagram
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="flex items-center gap-2"
              >
                <List className="h-4 w-4" />
                List
              </Button>
            </div>
          </div>

          {viewMode === 'diagram' ? (
            <ReactFlowProcessDiagram
              processSteps={processSteps}
              hazards={hazards}
              ccpData={ccpData}
              readOnly={true}
            />
          ) : (
            <InteractiveProcessFlow
              processSteps={processSteps}
              hazards={hazards}
              ccpData={ccpData}
              readOnly={true}
            />
          )}
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">3. Critical Control Points</h3>
          {ccpData.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[150px]">Process Step</TableHead>
                    <TableHead>Hazard</TableHead>
                    <TableHead>Critical Limits</TableHead>
                    <TableHead>Monitoring</TableHead>
                    <TableHead>Corrective Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {ccpData.map((item, index) => {
                    const step = processSteps.find(s => s.id === item.hazard.processStepId);
                    return (
                      <TableRow key={index}>
                        <TableCell className="font-medium">{step?.name || 'Unknown Step'}</TableCell>
                        <TableCell>{item.hazard.description}</TableCell>
                        <TableCell>{item.ccp.criticalLimit || 'Not defined'}</TableCell>
                        <TableCell>{item.ccp.monitoringProcedure || 'Not defined'}</TableCell>
                        <TableCell>{item.ccp.correctiveAction || 'Not defined'}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          ) : (
            <p className="text-yellow-600">
              No Critical Control Points have been defined. A valid HACCP plan requires at least one CCP.
            </p>
          )}
        </div>

        <Separator />

        <div className="space-y-4">
          <h3 className="text-lg font-semibold">4. Verification Procedures</h3>
          <ul className="list-disc pl-5 space-y-2">
            <li>Daily calibration of thermometers</li>
            <li>Weekly review of monitoring records</li>
            <li>Monthly microbiological testing</li>
            <li>Annual HACCP system review</li>
            <li>Internal audits every 6 months</li>
          </ul>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setActiveTab('generate')}>
            Edit
          </Button>
          <Button onClick={() => {
            handleSavePlan();
            setActiveTab('approve');
          }}>
            <Save className="h-4 w-4 mr-2" />
            Save Plan
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
