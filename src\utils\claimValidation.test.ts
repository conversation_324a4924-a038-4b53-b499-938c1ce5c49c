import validateClaimForm from './claimValidation';

describe('validateClaimForm', () => {
  it('should return an error if customerName is empty', () => {
    const values = { customerName: '' };
    const { errors, isValid } = validateClaimForm(values);
    expect(isValid).toBe(false);
    expect(errors.customerName).toBe('Customer name is required');
  });

  it('should return an error if productId is empty', () => {
    const values = { productId: '' };
    const { errors, isValid } = validateClaimForm(values);
    expect(isValid).toBe(false);
    expect(errors.productId).toBe('Product selection is required');
  });

  it('should return an error if description is empty', () => {
    const values = { description: '' };
    const { errors, isValid } = validateClaimForm(values);
    expect(isValid).toBe(false);
    expect(errors.description).toBe('Description is required');
  });

  it('should return an error if description is less than 10 characters', () => {
    const values = { description: 'short' };
    const { errors, isValid } = validateClaimForm(values);
    expect(isValid).toBe(false);
    expect(errors.description).toBe('Description must be at least 10 characters');
  });

  it('should return no errors if all fields are valid', () => {
    const values = { customerName: 'Test Customer', productId: '123', description: 'This is a valid description' };
    const { errors, isValid } = validateClaimForm(values);
    expect(isValid).toBe(true);
    expect(Object.keys(errors).length).toBe(0);
  });
});