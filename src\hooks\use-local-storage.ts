import { useState, useEffect } from 'react';

/**
 * Custom hook for persisting state in localStorage
 * @param key The localStorage key to store the value under
 * @param initialValue The initial value (or function that returns the initial value)
 * @returns A stateful value and a function to update it, like useState
 */
export function useLocalStorage<T>(
  key: string,
  initialValue: T | (() => T)
): [T, React.Dispatch<React.SetStateAction<T>>] {
  // Get from localStorage on initial render, falling back to initialValue
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      // Get from localStorage
      const item = window.localStorage.getItem(key);
      
      // Parse stored json or return initialValue
      if (item) {
        // Handle Date objects during parsing
        return JSON.parse(item, (key, value) => {
          // Check if the value is a date string in ISO format
          if (typeof value === 'string' && 
              /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(value)) {
            return new Date(value);
          }
          return value;
        });
      }
      
      // If no item in localStorage, use initialValue
      if (typeof initialValue === 'function') {
        return (initialValue as () => T)();
      } else {
        return initialValue;
      }
    } catch (error) {
      // If error, use initialValue
      console.error('Error reading from localStorage:', error);
      
      if (typeof initialValue === 'function') {
        return (initialValue as () => T)();
      } else {
        return initialValue;
      }
    }
  });
  
  // Update localStorage when the state changes
  useEffect(() => {
    try {
      // Save to localStorage
      window.localStorage.setItem(key, JSON.stringify(storedValue));
    } catch (error) {
      console.error('Error writing to localStorage:', error);
    }
  }, [key, storedValue]);
  
  return [storedValue, setStoredValue];
}
