# Document Management System Metadata Schema

This document defines the metadata schema for the document management system.

## Fields

| Field Name (Français) | Data Type | Length Constraints | Required | Description |
|---|---|---|---|---|
| Document Reference Number (Référence document) | Text | Max 255 characters | Yes | Unique identifier for the document. |
| Document Title (Titre du document) | Text | Max 255 characters | Yes | Title of the document. |
| Version | Number |  | Yes | Version number of the document. |
| Issue Date (Date d’émission) | Date |  | Yes | Date when the document was issued. |
| Author (Function) [Auteur (fonction)] | Text | Max 255 characters | Yes | Author of the document and their function. |
| Responsible Department (Service responsable) | Text | Max 255 characters | Yes | Department responsible for the document. |
| Status (Statut) | Enumeration | Draft, Approved, Superseded | Yes | Current status of the document. |
| Approval Date (Date de validation) | Date |  | No | Date when the document was approved. |
| Approved By (Function) [Validé par (fonction)] | Text | Max 255 characters | No | Person who approved the document and their function. |
| Revision Date (Date de révision) | Date |  | No | Date when the document was last revised. |
| Revision Reason (Objet de la révision) | Text | Max 255 characters | No | Reason for the revision. |
| Previous Reference Number (Référence précédente) | Text | Max 255 characters | No | Reference number of the previous version of the document. |
| Storage Location (Lieu de stockage) | Text | Max 255 characters | Yes | Physical or electronic location of the document. |
| Distribution Method (Mode de diffusion) | Text | Max 255 characters | No | Method used to distribute the document. |
| Remarks (Remarques) | Text |  | No | Any additional remarks about the document. |

## Version Control

The system should automatically increment the version number each time a document is revised. Previous versions of the document should be archived and accessible.

## Access Permissions

Access permissions should be based on the Responsible Department. Users should only be able to access documents for which their department is responsible.

## Automated Notifications

The system should send automated notifications for document review and revision cycles. Notifications should be sent to the Author and Responsible Department.