import React from 'react';
import { format } from 'date-fns';
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDescription,
  SheetFooter,
  SheetClose
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { PreventiveAction, CAPA } from '@/models/capa';
import { PenLine, FileText, Calendar, CheckCircle, AlertCircle, FileCheck } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface PreventiveActionDetailsModalProps {
  action: PreventiveAction | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit: (action: PreventiveAction) => void;
  capa?: CAPA | null;
  getCapaTitle: (capaId: string) => string;
}

export function PreventiveActionDetailsModal({
  action,
  open,
  onOpenChange,
  onEdit,
  capa,
  getCapaTitle
}: PreventiveActionDetailsModalProps) {
  if (!action) return null;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'planned':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">Planned</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">In Progress</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">Completed</Badge>;
      case 'verified':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-300">Verified</Badge>;
      case 'delayed':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">Delayed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getEffectivenessBadge = (effectiveness: string) => {
    switch (effectiveness) {
      case 'effective':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">Effective</Badge>;
      case 'partially_effective':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">Partially Effective</Badge>;
      case 'not_effective':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">Not Effective</Badge>;
      case 'not_verified':
      default:
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-300">Not Verified</Badge>;
    }
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent
        side="right"
        className="w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl"
        onKeyDown={(e) => {
          // Add keyboard shortcuts
          if (e.key === 'e' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            onEdit(action);
          }
        }}
      >
        <SheetHeader className="mb-6">
          <SheetTitle className="text-xl flex items-center">
            <span className="mr-3">Preventive Action Details</span>
            {getStatusBadge(action.status)}
          </SheetTitle>
          <SheetDescription>
            {action.id} - {getCapaTitle(action.capaId)}
          </SheetDescription>
        </SheetHeader>

        <Tabs defaultValue="details" className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="verification">Verification</TabsTrigger>
            {action.notes && <TabsTrigger value="notes">Notes</TabsTrigger>}
          </TabsList>
          <div className="max-h-[60vh] overflow-y-auto pr-1">

          <TabsContent value="details" className="space-y-6">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
              <p className="text-base">{action.description}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Status</h3>
                <p>{getStatusBadge(action.status)}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Responsible Person</h3>
                <p>{action.responsiblePerson}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Target Date</h3>
                <p>{action.targetDate ? format(action.targetDate, 'PPP') : 'Not set'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Completion Date</h3>
                <p>
                  {action.completionDate ? format(action.completionDate, 'PPP') : 'Not completed yet'}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Created By</h3>
                <p>{action.createdBy} on {format(action.createdAt, 'PPP')}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Updated</h3>
                <p>{action.updatedBy} on {format(action.updatedAt, 'PPP')}</p>
              </div>
            </div>

            {action.status === 'delayed' && (
              <Alert variant="destructive" className="bg-red-50 border-red-200">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Action Delayed</AlertTitle>
                <AlertDescription>
                  This preventive action is behind schedule. The target date has passed without completion.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="verification" className="space-y-6">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Verification Method</h3>
              <p className="text-base">{action.verificationMethod}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Effectiveness</h3>
                <p>{getEffectivenessBadge(action.effectiveness)}</p>
              </div>
              {action.verifiedBy && (
                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-1">Verified By</h3>
                  <p>{action.verifiedBy}</p>
                </div>
              )}
            </div>

            {action.verificationResults && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Verification Results</h3>
                <p className="text-base">{action.verificationResults}</p>
              </div>
            )}

            {action.verificationDate && (
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Verification Date</h3>
                <p>{format(action.verificationDate, 'PPP')}</p>
              </div>
            )}

            {action.status !== 'verified' && action.status !== 'completed' && (
              <Alert className="bg-blue-50 border-blue-200">
                <FileCheck className="h-4 w-4" />
                <AlertTitle>Verification Pending</AlertTitle>
                <AlertDescription>
                  This action has not been verified yet. Verification will be performed once the action is completed.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          {action.notes && (
            <TabsContent value="notes" className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-1">Notes</h3>
                <p className="text-base whitespace-pre-line">{action.notes}</p>
              </div>
            </TabsContent>
          )}
          </div>
        </Tabs>

        <SheetFooter className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-6">
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              size="sm"
              className="mr-2"
              onClick={() => onEdit(action)}
            >
              <PenLine className="h-4 w-4 mr-2" />
              Edit Action
            </Button>
            <div className="text-xs text-muted-foreground mt-1">
              Keyboard shortcuts: <kbd className="px-1 py-0.5 bg-muted rounded border">Esc</kbd> to close,
              <kbd className="px-1 py-0.5 bg-muted rounded border ml-1">{/Mac|iPhone|iPod|iPad/.test(navigator.userAgent) ? '⌘' : 'Ctrl'}+E</kbd> to edit
            </div>
          </div>
          <SheetClose asChild>
            <Button variant="default">Close</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
