
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { HazardMatrix } from '@/components/ui/hazard-matrix';
import { DecisionTree } from '@/components/ccp/decision-tree';
import { calculateRisk } from '@/utils/riskCalculation';
import { Hazard, HazardType } from '@/models/types';
import { toast } from '@/hooks/use-toast';

interface HazardFormProps {
  onSubmit: (hazard: Partial<Hazard>) => void;
  initialHazard?: Partial<Hazard>;
  processStepId: string;
}

export function HazardForm({ onSubmit, initialHazard, processStepId }: HazardFormProps) {
  const [hazard, setHazard] = useState<Partial<Hazard>>(initialHazard || {
    processStepId,
    description: '',
    type: 'Biological' as HazardType,
    severity: 0,
    likelihood: 0,
    controlMeasures: '',
    isCCP: false
  });

  const [showDecisionTree, setShowDecisionTree] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setHazard(prev => ({ ...prev, [name]: value }));
  };

  const handleTypeChange = (value: HazardType) => {
    setHazard(prev => ({ ...prev, type: value }));
  };

  const handleRiskMatrixSelect = (severity: number, likelihood: number) => {
    setHazard(prev => ({ ...prev, severity, likelihood }));
  };

  const handleCCPDetermination = (isCCP: boolean) => {
    setHazard(prev => ({ ...prev, isCCP }));
    setShowDecisionTree(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!hazard.description) {
      toast({
        title: "Error",
        description: "Please provide a hazard description",
        variant: "destructive"
      });
      return;
    }

    if (!hazard.severity || !hazard.likelihood) {
      toast({
        title: "Error",
        description: "Please select severity and likelihood on the risk matrix",
        variant: "destructive"
      });
      return;
    }

    onSubmit(hazard);
  };

  const { level: riskLevel } =
    hazard.severity && hazard.likelihood
      ? calculateRisk(hazard.severity, hazard.likelihood)
      : { level: null };

  return (
    <div>
      {showDecisionTree ? (
        <div className="space-y-4">
          <DecisionTree onComplete={handleCCPDetermination} />
          <div className="flex justify-end">
            <Button variant="outline" onClick={() => setShowDecisionTree(false)}>
              Cancel
            </Button>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="description" className="text-base font-medium">Hazard Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={hazard.description}
                  onChange={handleChange}
                  placeholder="Describe the potential hazard in detail"
                  className="min-h-[120px]"
                  required
                />
              </div>

              <div className="space-y-3">
                <Label className="text-base font-medium">Hazard Type</Label>
                <div className="grid grid-cols-3 gap-4">
                  <div
                    className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 cursor-pointer transition-colors ${
                      hazard.type === 'Biological'
                        ? 'border-green-500 bg-green-50'
                        : 'border-muted hover:border-muted-foreground'
                    }`}
                    onClick={() => handleTypeChange('Biological')}
                  >
                    <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mb-2">
                      <span className="text-green-700 text-xs font-bold">B</span>
                    </div>
                    <span className="text-sm font-medium">Biological</span>
                  </div>

                  <div
                    className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 cursor-pointer transition-colors ${
                      hazard.type === 'Chemical'
                        ? 'border-purple-500 bg-purple-50'
                        : 'border-muted hover:border-muted-foreground'
                    }`}
                    onClick={() => handleTypeChange('Chemical')}
                  >
                    <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mb-2">
                      <span className="text-purple-700 text-xs font-bold">C</span>
                    </div>
                    <span className="text-sm font-medium">Chemical</span>
                  </div>

                  <div
                    className={`flex flex-col items-center justify-center p-3 rounded-lg border-2 cursor-pointer transition-colors ${
                      hazard.type === 'Physical'
                        ? 'border-orange-500 bg-orange-50'
                        : 'border-muted hover:border-muted-foreground'
                    }`}
                    onClick={() => handleTypeChange('Physical')}
                  >
                    <div className="w-8 h-8 rounded-full bg-orange-100 flex items-center justify-center mb-2">
                      <span className="text-orange-700 text-xs font-bold">P</span>
                    </div>
                    <span className="text-sm font-medium">Physical</span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="controlMeasures" className="text-base font-medium">Control Measures</Label>
                <Textarea
                  id="controlMeasures"
                  name="controlMeasures"
                  value={hazard.controlMeasures}
                  onChange={handleChange}
                  placeholder="Describe control measures that can be implemented to prevent, eliminate, or reduce the hazard"
                  className="min-h-[120px]"
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              <div className="space-y-3">
                <Label className="text-base font-medium">Risk Assessment</Label>
                <div className="p-3 bg-muted/20 rounded-lg border">
                  <HazardMatrix
                    selectedSeverity={hazard.severity || undefined}
                    selectedLikelihood={hazard.likelihood || undefined}
                    onSelect={handleRiskMatrixSelect}
                  />
                </div>

                {riskLevel && (
                  <div className={`p-3 rounded-md border ${
                    riskLevel === 'low' ? 'bg-green-50 border-green-200 text-green-800' :
                    riskLevel === 'medium' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' :
                    'bg-red-50 border-red-200 text-red-800'
                  }`}>
                    <div className="flex items-center">
                      <div className={`w-3 h-3 rounded-full mr-2 ${
                        riskLevel === 'low' ? 'bg-green-500' :
                        riskLevel === 'medium' ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}></div>
                      <p className="font-medium">
                        Risk Level: <span className="capitalize">{riskLevel}</span>
                        {hazard.severity && hazard.likelihood && (
                          <span className="ml-1 text-sm">
                            (Score: {hazard.severity * hazard.likelihood})
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <Label className="text-base font-medium">Critical Control Point (CCP)</Label>
                <div className="grid grid-cols-1 gap-3">
                  <div
                    className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
                      hazard.isCCP
                        ? 'border-yellow-500 bg-yellow-50'
                        : 'border-muted hover:border-muted-foreground'
                    }`}
                    onClick={() => setHazard(prev => ({ ...prev, isCCP: !prev.isCCP }))}
                  >
                    <div className="flex items-center">
                      <div className={`w-5 h-5 rounded border flex items-center justify-center mr-3 ${
                        hazard.isCCP ? 'bg-yellow-500 border-yellow-500' : 'border-gray-300'
                      }`}>
                        {hazard.isCCP && (
                          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round" className="w-3 h-3 text-white">
                            <polyline points="20 6 9 17 4 12"></polyline>
                          </svg>
                        )}
                      </div>
                      <div>
                        <p className="font-medium">{hazard.isCCP ? 'This is a Critical Control Point' : 'Not a Critical Control Point'}</p>
                        <p className="text-sm text-muted-foreground mt-1">
                          {hazard.isCCP
                            ? 'This step is essential for food safety and requires critical limits, monitoring, and verification.'
                            : 'This hazard can be controlled through prerequisite programs.'}
                        </p>
                      </div>
                    </div>
                  </div>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowDecisionTree(true)}
                    className="w-full"
                  >
                    Use Decision Tree to Determine CCP Status
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-8">
            <Button type="button" variant="outline" onClick={() => onSubmit(initialHazard || { processStepId, description: '', type: 'Biological', severity: 0, likelihood: 0, controlMeasures: '', isCCP: false })}>
              Cancel
            </Button>
            <Button type="submit">
              {initialHazard ? 'Update Hazard' : 'Add Hazard'}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}

export default HazardForm;
