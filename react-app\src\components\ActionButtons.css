.action-buttons {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: center;
}

.action-btn {
  padding: 0.5rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.875rem;
  transition: var(--transition);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 2rem;
  height: 2rem;
  background: none;
}

.view-btn {
  background-color: var(--info-color);
  color: var(--white);
}

.view-btn:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.edit-btn {
  background-color: var(--warning-color);
  color: var(--white);
}

.edit-btn:hover {
  background-color: #d97706;
  transform: translateY(-1px);
}

.delete-btn {
  background-color: var(--danger-color);
  color: var(--white);
}

.delete-btn:hover {
  background-color: #dc2626;
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .action-btn {
    min-width: 1.75rem;
    height: 1.75rem;
    padding: 0.375rem;
  }
}
