import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>rigger,
  TabsContent,
} from "@/components/ui/tabs";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { FileDown, Plus, Eye, Edit } from "lucide-react";
import { FishboneDiagram } from "@/components/plan/FishboneDiagram";

interface Analysis {
  id: string;
  title: string;
  method: string;
  date: string;
  status: string;
  capaId?: string;
  conductedBy?: string;
  findings?: string;
  rootCauses?: string[];
  contributingFactors?: string[];
}

interface FiveWhyItem {
  id: string;
  question: string;
  answer: string;
}

interface FishboneCategory {
  name: string;
  causes: string[];
}

interface FMEAItem {
  id: string;
  failureMode: string;
  effect: string;
  cause: string;
  severity: number;
  occurrence: number;
  detection: number;
  rpn: number;
}

const AnalysisDetails = ({ analysis }: { analysis: Analysis }) => {
  return (
    <Card className="mt-4">
      <CardHeader className="flex flex-row items-start justify-between">
        <div>
          <CardTitle>{analysis.title}</CardTitle>
          <CardDescription>
            {analysis.method} - {analysis.date}
          </CardDescription>
        </div>
        <Badge variant={
          analysis.status === "Completed" ? "default" :
          analysis.status === "In Progress" ? "secondary" : "outline"
        }>
          {analysis.status}
        </Badge>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h4 className="text-sm font-medium mb-1">CAPA Reference</h4>
            <p className="text-sm text-muted-foreground">{analysis.capaId || "Not linked to a CAPA"}</p>
          </div>
          <div>
            <h4 className="text-sm font-medium mb-1">Conducted By</h4>
            <p className="text-sm text-muted-foreground">{analysis.conductedBy || "Not specified"}</p>
          </div>
        </div>

        {analysis.findings && (
          <div>
            <h4 className="text-sm font-medium mb-1">Findings</h4>
            <p className="text-sm text-muted-foreground">{analysis.findings}</p>
          </div>
        )}

        {analysis.rootCauses && analysis.rootCauses.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-1">Root Causes</h4>
            <ul className="list-disc pl-5">
              {analysis.rootCauses.map((cause, index) => (
                <li key={index} className="text-sm text-muted-foreground">{cause}</li>
              ))}
            </ul>
          </div>
        )}

        {analysis.contributingFactors && analysis.contributingFactors.length > 0 && (
          <div>
            <h4 className="text-sm font-medium mb-1">Contributing Factors</h4>
            <ul className="list-disc pl-5">
              {analysis.contributingFactors.map((factor, index) => (
                <li key={index} className="text-sm text-muted-foreground">{factor}</li>
              ))}
            </ul>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end gap-2">
        <Button variant="outline" size="sm">
          <FileDown className="h-4 w-4 mr-2" />
          Export
        </Button>
        <Button size="sm">Edit Analysis</Button>
      </CardFooter>
    </Card>
  );
};

const mockData: Analysis[] = [
  {
    id: "1",
    title: "Equipment Failure",
    method: "5-Why",
    date: "2024-01-15",
    status: "Completed",
    capaId: "CAPA-2024-001",
    conductedBy: "John Smith",
    findings: "The equipment failure was caused by worn-out bearings that weren't replaced during scheduled maintenance.",
    rootCauses: [
      "Inadequate preventive maintenance schedule",
      "Lack of proper inspection procedures",
      "No system for tracking component lifespan"
    ],
    contributingFactors: [
      "High production volume",
      "Delayed equipment replacement",
      "Insufficient maintenance staff"
    ]
  },
  {
    id: "2",
    title: "Process Deviation",
    method: "Fishbone",
    date: "2024-02-20",
    status: "In Progress",
    capaId: "CAPA-2024-003",
    conductedBy: "Maria Garcia"
  },
  {
    id: "3",
    title: "Material Defect",
    method: "FMEA",
    date: "2024-03-10",
    status: "Pending",
    capaId: "CAPA-2024-005",
    conductedBy: "David Chen"
  },
];

const FiveWhyAnalysis = () => {
  const [whyItems, setWhyItems] = useState<{ question: string; answer: string }[]>([
    { question: "Why did the issue occur?", answer: "" },
  ]);

  const addWhyQuestion = () => {
    if (whyItems.length < 5) {
      const lastAnswer = whyItems[whyItems.length - 1].answer;
      setWhyItems([
        ...whyItems,
        {
          question: `Why ${lastAnswer.toLowerCase()}?`,
          answer: ""
        }
      ]);
    }
  };

  const updateAnswer = (index: number, answer: string) => {
    const newItems = [...whyItems];
    newItems[index].answer = answer;
    setWhyItems(newItems);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>5-Why Analysis</CardTitle>
        <CardDescription>
          Ask "why" up to 5 times to find the root cause of the problem
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {whyItems.map((item, index) => (
          <div key={index} className="space-y-2">
            <Label htmlFor={`why-${index}`} className="font-medium">
              {index + 1}. {item.question}
            </Label>
            <Textarea
              id={`why-${index}`}
              value={item.answer}
              onChange={(e) => updateAnswer(index, e.target.value)}
              placeholder="Enter your answer"
              className="min-h-[60px]"
            />
          </div>
        ))}

        {whyItems.length < 5 && whyItems[whyItems.length - 1].answer && (
          <Button
            onClick={addWhyQuestion}
            variant="outline"
            className="mt-2"
          >
            Add Next Why
          </Button>
        )}

        <div className="mt-6">
          <h4 className="text-sm font-medium mb-2">Root Causes Identified:</h4>
          {whyItems[whyItems.length - 1].answer ? (
            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm">{whyItems[whyItems.length - 1].answer}</p>
            </div>
          ) : (
            <p className="text-sm text-muted-foreground">Complete the analysis to identify root causes</p>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button onClick={() => {
          // Create a new analysis from the 5-Why data
          if (whyItems[whyItems.length - 1].answer) {
            const newAnalysis: Analysis = {
              id: `${Date.now()}`,
              title: "5-Why Analysis",
              method: "5-Why",
              date: new Date().toISOString().split('T')[0],
              status: "Completed",
              findings: whyItems[0].answer,
              rootCauses: [whyItems[whyItems.length - 1].answer],
              contributingFactors: whyItems.slice(1, -1).map(item => item.answer)
            };

            // Save to localStorage via parent component
            if (typeof window !== 'undefined') {
              const event = new CustomEvent('save-analysis', { detail: newAnalysis });
              window.dispatchEvent(event);
            }
          }
        }}>Save Analysis</Button>
      </CardFooter>
    </Card>
  );
};

const FishboneAnalysis = () => {
  const categories = [
    { name: "People", causes: [] },
    { name: "Methods", causes: [] },
    { name: "Machines", causes: [] },
    { name: "Materials", causes: [] },
    { name: "Measurement", causes: [] },
    { name: "Environment", causes: [] },
  ];

  const [fishboneData, setFishboneData] = useState(categories);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [newCause, setNewCause] = useState("");

  const addCause = () => {
    if (selectedCategory && newCause.trim()) {
      setFishboneData(
        fishboneData.map(category =>
          category.name === selectedCategory
            ? { ...category, causes: [...category.causes, newCause.trim()] }
            : category
        )
      );
      setNewCause("");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fishbone (Ishikawa) Diagram Analysis</CardTitle>
        <CardDescription>
          Identify causes across six standard categories
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          {fishboneData.map((category) => (
            <Card
              key={category.name}
              className={`cursor-pointer ${selectedCategory === category.name ? 'border-primary' : ''}`}
              onClick={() => setSelectedCategory(category.name)}
            >
              <CardHeader className="py-3">
                <CardTitle className="text-base">{category.name}</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                {category.causes.length > 0 ? (
                  <ul className="list-disc pl-5 space-y-1">
                    {category.causes.map((cause, index) => (
                      <li key={index} className="text-sm">{cause}</li>
                    ))}
                  </ul>
                ) : (
                  <p className="text-sm text-muted-foreground">No causes added yet</p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-4 space-y-2">
          <Label htmlFor="new-cause">Add Cause to {selectedCategory || "selected category"}</Label>
          <div className="flex gap-2">
            <Input
              id="new-cause"
              value={newCause}
              onChange={(e) => setNewCause(e.target.value)}
              placeholder="Enter a cause"
              disabled={!selectedCategory}
              className="flex-1"
            />
            <Button onClick={addCause} disabled={!selectedCategory || !newCause.trim()}>
              Add
            </Button>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button>Save Analysis</Button>
      </CardFooter>
    </Card>
  );
};

const FMEAAnalysis = () => {
  const [fmeaItems, setFmeaItems] = useState<FMEAItem[]>([
    {
      id: "1",
      failureMode: "",
      effect: "",
      cause: "",
      severity: 1,
      occurrence: 1,
      detection: 1,
      rpn: 1
    }
  ]);

  const updateItem = (id: string, field: keyof FMEAItem, value: any) => {
    setFmeaItems(items =>
      items.map(item => {
        if (item.id === id) {
          const updatedItem = { ...item, [field]: value };

          // Recalculate RPN if severity, occurrence, or detection changes
          if (field === 'severity' || field === 'occurrence' || field === 'detection') {
            updatedItem.rpn = updatedItem.severity * updatedItem.occurrence * updatedItem.detection;
          }

          return updatedItem;
        }
        return item;
      })
    );
  };

  const addItem = () => {
    const newId = (parseInt(fmeaItems[fmeaItems.length - 1].id) + 1).toString();
    setFmeaItems([
      ...fmeaItems,
      {
        id: newId,
        failureMode: "",
        effect: "",
        cause: "",
        severity: 1,
        occurrence: 1,
        detection: 1,
        rpn: 1
      }
    ]);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Failure Mode and Effects Analysis (FMEA)</CardTitle>
        <CardDescription>
          Identify potential failure modes, effects, causes, and calculate risk priority
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Failure Mode</TableHead>
              <TableHead>Effect</TableHead>
              <TableHead>Cause</TableHead>
              <TableHead className="w-[80px]">Severity (1-10)</TableHead>
              <TableHead className="w-[80px]">Occurrence (1-10)</TableHead>
              <TableHead className="w-[80px]">Detection (1-10)</TableHead>
              <TableHead className="w-[80px]">RPN</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fmeaItems.map((item) => (
              <TableRow key={item.id}>
                <TableCell>
                  <Input
                    value={item.failureMode}
                    onChange={(e) => updateItem(item.id, 'failureMode', e.target.value)}
                    placeholder="Failure mode"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    value={item.effect}
                    onChange={(e) => updateItem(item.id, 'effect', e.target.value)}
                    placeholder="Effect"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    value={item.cause}
                    onChange={(e) => updateItem(item.id, 'cause', e.target.value)}
                    placeholder="Cause"
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    min={1}
                    max={10}
                    value={item.severity}
                    onChange={(e) => updateItem(item.id, 'severity', parseInt(e.target.value) || 1)}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    min={1}
                    max={10}
                    value={item.occurrence}
                    onChange={(e) => updateItem(item.id, 'occurrence', parseInt(e.target.value) || 1)}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    min={1}
                    max={10}
                    value={item.detection}
                    onChange={(e) => updateItem(item.id, 'detection', parseInt(e.target.value) || 1)}
                  />
                </TableCell>
                <TableCell className="font-medium">
                  {item.rpn}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <Button variant="outline" className="mt-4" onClick={addItem}>
          Add Row
        </Button>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button>Save Analysis</Button>
      </CardFooter>
    </Card>
  );
};

const RootCauseAnalysis = () => {
  const [open, setOpen] = useState(false);
  const [selectedAnalysis, setSelectedAnalysis] = useState<Analysis | null>(null);
  const [activeTab, setActiveTab] = useState("list");
  const [analysisMethod, setAnalysisMethod] = useState<string>("5-why");

  // Load saved analyses from localStorage
  const [savedAnalyses, setSavedAnalyses] = useState<Analysis[]>(() => {
    try {
      const saved = localStorage.getItem('haccp_root_cause_analyses_ui');
      return saved ? JSON.parse(saved) : mockData;
    } catch (error) {
      console.error('Error loading root cause analyses from localStorage:', error);
      return mockData;
    }
  });

  // Save analyses to localStorage when they change
  useEffect(() => {
    try {
      localStorage.setItem('haccp_root_cause_analyses_ui', JSON.stringify(savedAnalyses));
    } catch (error) {
      console.error('Error saving root cause analyses to localStorage:', error);
    }
  }, [savedAnalyses]);

  // Event listener for saving new analyses
  useEffect(() => {
    const handleSaveAnalysis = (event: Event) => {
      const customEvent = event as CustomEvent<Analysis>;
      const newAnalysis = customEvent.detail;

      // Add the new analysis to the saved analyses
      setSavedAnalyses(prev => [...prev, newAnalysis]);

      // Show success message or notification
      alert('Analysis saved successfully!');

      // Navigate back to the list view
      setActiveTab('list');
    };

    window.addEventListener('save-analysis', handleSaveAnalysis as EventListener);

    return () => {
      window.removeEventListener('save-analysis', handleSaveAnalysis as EventListener);
    };
  }, []);

  const handleBackToList = () => {
    setSelectedAnalysis(null);
    setActiveTab("list");
  };

  const handleNewAnalysis = () => {
    setSelectedAnalysis(null);
    setActiveTab("new");
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Root Cause Analysis</h1>
        <div className="flex gap-2">
          {activeTab !== "list" && (
            <Button variant="outline" onClick={handleBackToList}>
              Back to List
            </Button>
          )}
          {activeTab === "list" && (
            <Button onClick={handleNewAnalysis}>
              <Plus className="h-4 w-4 mr-2" />
              New Analysis
            </Button>
          )}
        </div>
      </div>

      {activeTab === "list" && (
        <>
          {selectedAnalysis ? (
            <AnalysisDetails analysis={selectedAnalysis} />
          ) : (
            <Card>
              <CardHeader>
                <CardTitle>Analysis Records</CardTitle>
                <CardDescription>
                  View and manage your root cause analyses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableCaption>A list of your root cause analyses.</TableCaption>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[80px]">ID</TableHead>
                      <TableHead>Title</TableHead>
                      <TableHead>Method</TableHead>
                      <TableHead>Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>CAPA ID</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {savedAnalyses.map((analysis) => (
                      <TableRow
                        key={analysis.id}
                        onClick={() => setSelectedAnalysis(analysis)}
                        className="cursor-pointer hover:bg-muted"
                      >
                        <TableCell className="font-medium">{analysis.id}</TableCell>
                        <TableCell>{analysis.title}</TableCell>
                        <TableCell>{analysis.method}</TableCell>
                        <TableCell>{analysis.date}</TableCell>
                        <TableCell>
                          <Badge variant={
                            analysis.status === "Completed" ? "default" :
                            analysis.status === "In Progress" ? "secondary" : "outline"
                          }>
                            {analysis.status}
                          </Badge>
                        </TableCell>
                        <TableCell>{analysis.capaId || "-"}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}
        </>
      )}

      {activeTab === "new" && (
        <Card>
          <CardHeader>
            <CardTitle>New Root Cause Analysis</CardTitle>
            <CardDescription>
              Select a method and complete the analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="analysis-title">Analysis Title</Label>
                  <Input id="analysis-title" placeholder="Enter a descriptive title" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capa-id">Related CAPA ID (optional)</Label>
                  <Input id="capa-id" placeholder="Enter CAPA ID if applicable" />
                </div>
              </div>

              <div className="space-y-2">
                <Label>Analysis Method</Label>
                <Tabs
                  defaultValue="5-why"
                  className="mt-2"
                  onValueChange={(value) => setAnalysisMethod(value)}
                >
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="5-why">5-Why</TabsTrigger>
                    <TabsTrigger value="fishbone">Fishbone</TabsTrigger>
                    <TabsTrigger value="fmea">FMEA</TabsTrigger>
                  </TabsList>
                  <TabsContent value="5-why" className="mt-4">
                    <FiveWhyAnalysis />
                  </TabsContent>
                  <TabsContent value="fishbone" className="mt-4">
                    <FishboneAnalysis />
                  </TabsContent>
                  <TabsContent value="fmea" className="mt-4">
                    <FMEAAnalysis />
                  </TabsContent>
                </Tabs>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RootCauseAnalysis;
