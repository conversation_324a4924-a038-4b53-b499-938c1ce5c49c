import * as React from "react"
import { DialogContent } from "@/components/ui/dialog"

/**
 * A DialogContent component that supports a maxHeight via CSS classes
 * This is a simpler approach than the DialogWithMaxHeight component
 */
export const DialogWithMaxHeightCSS = React.forwardRef<
  React.ElementRef<typeof DialogContent>,
  React.ComponentPropsWithoutRef<typeof DialogContent>
>(({ className, children, ...props }, ref) => {
  return (
    <DialogContent className={`${className || ""} max-h-[75vh] overflow-y-auto`} {...props} ref={ref}>
      {children}
    </DialogContent>
  );
});

DialogWithMaxHeightCSS.displayName = "DialogWithMaxHeightCSS";
