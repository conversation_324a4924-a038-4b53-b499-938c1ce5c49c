import React from 'react';
import { DocumentStatus } from '../types';
import './Badge.css';

interface StatusBadgeProps {
  status: DocumentStatus;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
  const getBadgeClass = (status: DocumentStatus): string => {
    const classes: Record<DocumentStatus, string> = {
      'RAS': 'status-ras',
      'Modifier': 'status-modifier',
      'Créer': 'status-creer',
      'Annuler': 'status-annuler'
    };
    return classes[status] || 'status-default';
  };

  return (
    <span className={`status-badge ${getBadgeClass(status)}`}>
      {status}
    </span>
  );
};

export default StatusBadge;
