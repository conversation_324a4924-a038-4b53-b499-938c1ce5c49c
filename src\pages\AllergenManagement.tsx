import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger, Ta<PERSON>Content } from '@/components/ui/tabs';
import {
  Allergen,
  AllergenControlMeasure,
  AllergenRiskAssessment as AllergenRiskAssessmentType,
  ProductAllergen,
  VerificationActivity,
  TrainingRecord,
  COMMON_ALLERGENS,
  MOCK_ALLERGEN_CONTROL_MEASURES,
  MOCK_ALLERGEN_RISK_ASSESSMENTS,
  MOCK_PRODUCT_ALLERGENS,
  MOCK_VERIFICATION_ACTIVITIES,
  MOCK_TRAINING_RECORDS
} from '@/models/allergen';
import { AllergenInventory } from '@/components/allergen/AllergenInventory';
import { AllergenControlMeasures } from '@/components/allergen/AllergenControlMeasures';
import { AllergenRiskAssessments } from '@/components/allergen/AllergenRiskAssessments';
import { ProductAllergenMatrix } from '@/components/allergen/ProductAllergenMatrix';
import { VerificationActivitiesManagement } from '@/components/capa/VerificationActivitiesManagement';
import { TrainingRecords } from '@/components/allergen/TrainingRecords';
import { AllergenDashboardSummary } from '@/components/allergen/AllergenDashboardSummary';
import { useLocalStorage } from '@/hooks/use-local-storage';

export function AllergenManagement() {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Use localStorage for all allergen-related data
  const [allergens, setAllergens] = useLocalStorage<Allergen[]>('haccp_allergens', COMMON_ALLERGENS);
  const [controlMeasures, setControlMeasures] = useLocalStorage<AllergenControlMeasure[]>('haccp_allergen_control_measures', MOCK_ALLERGEN_CONTROL_MEASURES);
  const [riskAssessments, setRiskAssessments] = useLocalStorage<AllergenRiskAssessmentType[]>('haccp_allergen_risk_assessments', MOCK_ALLERGEN_RISK_ASSESSMENTS);
  const [productAllergens, setProductAllergens] = useLocalStorage<ProductAllergen[]>('haccp_product_allergens', MOCK_PRODUCT_ALLERGENS);

  const [verificationActivities, setVerificationActivities] = useLocalStorage<VerificationActivity[]>('haccp_allergen_verification_activities', MOCK_VERIFICATION_ACTIVITIES);
  const [trainingRecords, setTrainingRecords] = useLocalStorage<TrainingRecord[]>('haccp_allergen_training_records', MOCK_TRAINING_RECORDS);

  // Products state
  const [products, setProducts] = useLocalStorage('haccp_allergen_products', [
    { id: 'product1', name: 'Chicken Soup', description: 'Classic chicken soup with vegetables' },
    { id: 'product2', name: 'Beef Stew', description: 'Hearty beef stew with potatoes and carrots' },
    { id: 'product3', name: 'Vegetable Curry', description: 'Spicy vegetable curry with coconut milk' }
  ]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">Allergen Management</h1>
      </div>

      <Tabs defaultValue="dashboard" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-7 gap-2">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="inventory">Allergen Inventory</TabsTrigger>
          <TabsTrigger value="control-measures">Control Measures</TabsTrigger>
          <TabsTrigger value="risk-assessments">Risk Assessments</TabsTrigger>
          <TabsTrigger value="product-matrix">Product Matrix</TabsTrigger>
          <TabsTrigger value="verification">Verification</TabsTrigger>
          <TabsTrigger value="training">Training</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <AllergenDashboardSummary
            allergens={allergens}
            controlMeasures={controlMeasures}
            riskAssessments={riskAssessments}
            productAllergens={productAllergens}
            verificationActivities={verificationActivities}
            trainingRecords={trainingRecords}
          />
        </TabsContent>

        <TabsContent value="inventory" className="space-y-4">
          <AllergenInventory
            allergens={allergens}
            setAllergens={setAllergens}
          />
        </TabsContent>

        <TabsContent value="control-measures" className="space-y-4">
          <AllergenControlMeasures
            controlMeasures={controlMeasures}
            setControlMeasures={setControlMeasures}
          />
        </TabsContent>

        <TabsContent value="risk-assessments" className="space-y-4">
          <AllergenRiskAssessments
            riskAssessments={riskAssessments}
            setRiskAssessments={setRiskAssessments}
            products={products}
          />
        </TabsContent>

        <TabsContent value="product-matrix" className="space-y-4">
          <ProductAllergenMatrix
            products={products}
            allergens={allergens}
            productAllergens={productAllergens}
            setProductAllergens={setProductAllergens}
            setProducts={setProducts}
          />
        </TabsContent>

        <TabsContent value="verification" className="space-y-4">
          <VerificationActivitiesManagement
            verificationActivities={verificationActivities}
            setVerificationActivities={setVerificationActivities}
            allergens={allergens}
            products={products}
          />
        </TabsContent>

        <TabsContent value="training" className="space-y-4">
          <TrainingRecords
            trainingRecords={trainingRecords}
            setTrainingRecords={setTrainingRecords}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default AllergenManagement;
