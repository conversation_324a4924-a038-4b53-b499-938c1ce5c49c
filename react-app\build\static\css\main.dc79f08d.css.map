{"version": 3, "file": "static/css/main.dc79f08d.css", "mappings": "yLAAA,YAEE,gCAAiC,CACjC,2BAA4B,CAF5B,iBAGF,CAEA,eAME,kBAAmB,CAHnB,yBAA0B,CAE1B,YAAa,CAJb,gBAAiB,CACjB,eAAgB,CAMhB,qBAAsB,CADtB,sBAAuB,CAHvB,8BAKF,CAEA,iBACE,0BAA2B,CAC3B,iBACF,CAEA,cAEE,2BAA4B,CAD5B,kBAAmB,CAGnB,eAAgB,CADhB,QAEF,CAEA,yBACE,eAEE,qBAAsB,CADtB,cAAe,CAEf,qBACF,CAEA,iBACE,iBACF,CACF,CAEA,yBACE,YACE,2BACF,CAEA,eACE,gBACF,CACF,CCjDA,iBAOE,gCAAkC,CANlC,8EAAiF,CAEjF,qCAAsC,CAGtC,+BAAgC,CAJhC,kBAAmB,CAGnB,+BAAgC,CADhC,yBAIF,CAEA,iBAEE,sBAAuB,CADvB,YAAa,CAEb,qBACF,CAEA,mBACE,gBAAiB,CACjB,4BAA6B,CAC7B,UACF,CAEA,oBAEE,kBAAmB,CACnB,eAAgB,CAFhB,8BAGF,CAEA,mBAGE,eACF,CAEA,iCALE,8BAA+B,CAC/B,UAQF,CAJA,cAEE,8BAEF,CAEA,iBAEE,eAAgB,CADhB,+BAEF,CAEA,qBACE,kBAAmB,CACnB,SACF,CAEA,UACE,eAAgB,CAChB,WAAY,CACZ,kBAAmB,CAEnB,cAAe,CACf,iBAAmB,CAEnB,UAAY,CADZ,SAAU,CAHV,yBAA0B,CAK1B,4BACF,CAEA,gBACE,SACF,CAEA,qBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CC3EA,uBACE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,sBAEE,kBAAmB,CADnB,YAAa,CAEb,qBAAsB,CACtB,+BACF,CAEA,UAWE,kBAAmB,CAVnB,qCAAsC,CAEtC,qCAAsC,CAEtC,kCAAmC,CAHnC,kBAAmB,CAMnB,cAAe,CAEf,mBAAoB,CAJpB,iBAAmB,CACnB,eAAgB,CAKhB,qBAAsB,CARtB,qBAAuB,CAKvB,4BAIF,CAEA,gBACE,qCAAsC,CACtC,iCAAkC,CAClC,0BACF,CAEA,WAEE,2BAA4B,CAE5B,QAAO,CAHP,iBAAmB,CAEnB,iBAAkB,CAElB,WAAY,CACZ,eAAgB,CAChB,sBAAuB,CACvB,kBACF,CAEA,iBAQE,kBAAmB,CAPnB,8BAA+B,CAE/B,WAAY,CACZ,iBAAkB,CAFlB,kBAAmB,CAQnB,cAAe,CAHf,YAAa,CAKb,gBAAkB,CANlB,WAAY,CAGZ,sBAAuB,CAEvB,4BAA6B,CAN7B,UAQF,CAEA,uBACE,kBAAmB,CACnB,oBACF,CAEA,gBAKE,8BAA+B,CAJ/B,iCAAkC,CAClC,kCAAmC,CAKnC,cAAe,CAJf,yBAA0B,CAC1B,iBAAkB,CAElB,4BAEF,CAEA,+CAGE,oBAAmC,CADnC,iCAEF,CAEA,kBAEE,uBAAwB,CADxB,cAAe,CAEf,+BACF,CAEA,kBAEE,2BAA4B,CAC5B,iBAAmB,CAFnB,8BAGF,CAEA,sBACE,uBAAwB,CACxB,gBACF,CAGA,yBACE,sBAEE,mBAAoB,CADpB,qBAAsB,CAEtB,qBACF,CAEA,gBACE,yBACF,CAEA,kBACE,gBACF,CACF,CChHA,YACE,yBACF,CAEA,UAGE,0BAAsB,CAFtB,YAAa,CAEb,qBAAsB,CADtB,wDAA2D,CAE3D,+BACF,CAEA,YACE,iBACF,CAEA,kBAEE,kBAAmB,CAInB,yBAA0B,CAL1B,YAAa,CAMb,iBAAmB,CAFnB,eAAgB,CAFhB,qBAAsB,CACtB,+BAIF,CAEA,oBACE,0BAA2B,CAC3B,iBACF,CAEA,iCAEE,yBAA0B,CAD1B,WAAY,CAGZ,eAAgB,CADhB,6BAEF,CAEA,qCAQE,4BAA6B,CAJ7B,gCAAiC,CACjC,kCAAmC,CAInC,yBAA0B,CAF1B,iBAAmB,CAJnB,cAAgB,CAGhB,4BAA6B,CAJ7B,UAQF,CAEA,iDAKE,uBAAwB,CAFxB,iCAAkC,CAClC,8BAA4C,CAF5C,YAIF,CAEA,+BACE,uBACF,CAEA,iDAEE,gCAAiC,CACjC,8BACF,CAGA,eACE,yBAA0B,CAC1B,gBAAkB,CAElB,eAAgB,CADhB,4BAEF,CAGA,mBACE,iBACF,CAEA,SAKE,uBAAwB,CADxB,WAAY,CAHZ,iBAAkB,CAClB,YAAc,CACd,WAAY,CAGZ,UACF,CAEA,6BAEE,SAAU,CADV,kBAEF,CAEA,cAGE,gCAAiC,CAGjC,kCAAmC,CAInC,WAAY,CANZ,kBAAmB,CAUnB,gBAAkB,CAClB,eAAgB,CAHhB,SAAU,CALV,yBAA0B,CAC1B,iBAAkB,CAGlB,OAAQ,CANR,iBAAkB,CAQlB,sBAAwB,CAZxB,iBAAkB,CAClB,WAAY,CAOZ,UAOF,CAEA,oBAQE,8CAAiE,CADjE,kBAAmB,CADnB,gBAAiB,CALjB,UAAW,CAIX,gBAAiB,CAHjB,iBAAkB,CAElB,UAAW,CADX,QAMF,CAGA,mBACE,gBACF,CAGA,cAKE,oCAAqC,CAJrC,YAAa,CAEb,qBAAsB,CADtB,wBAAyB,CAIzB,4BAA6B,CAF7B,6BAGF,CAGA,yBACE,UAEE,qBAAsB,CADtB,yBAEF,CAEA,cACE,6BACF,CAEA,YACE,yBACF,CACF,CAEA,yBACE,YACE,yBACF,CACF,CC/JA,iBAIE,0BAAsB,CACtB,eAAgB,CAHhB,YAAa,CAEb,qBAAsB,CADtB,6BAA8B,CAF9B,yBAKF,CAEA,cACE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,wBAGE,kBAAmB,CADnB,YAAa,CADb,iBAGF,CAEA,aAGE,uBAAwB,CACxB,iBAAmB,CAFnB,WAAa,CADb,iBAAkB,CAIlB,SACF,CAEA,aAME,4BAA6B,CAH7B,gCAAiC,CACjC,kCAAmC,CAGnC,yBAA0B,CAF1B,iBAAmB,CAHnB,qBAAsC,CAMtC,4BAA6B,CAP7B,UAQF,CAEA,mBAIE,uBAAwB,CAFxB,iCAAkC,CAClC,8BAA4C,CAF5C,YAIF,CAMA,wCAHE,uBAkBF,CAfA,cAaE,kBAAmB,CAVnB,eAAgB,CAChB,WAAY,CAIZ,iBAAkB,CAFlB,cAAe,CAIf,YAAa,CAEb,aAAc,CAEd,sBAAuB,CAPvB,cAAgB,CANhB,iBAAkB,CAClB,WAAa,CAOb,4BAA6B,CAE7B,YAIF,CAEA,oBACE,gCAAiC,CACjC,yBACF,CAEA,mBACE,YACF,CAEA,cACE,YAAa,CACb,qBACF,CAEA,qBAKE,4BAA6B,CAF7B,gCAAiC,CACjC,kCAAmC,CAGnC,yBAA0B,CAN1B,QAAO,CAKP,iBAAmB,CAJnB,cAAgB,CAMhB,4BACF,CAEA,2BAIE,uBAAwB,CAFxB,iCAAkC,CAClC,8BAA4C,CAF5C,YAIF,CAGA,0BACE,iBAEE,qBAAsB,CADtB,yBAEF,CAEA,cACE,qBACF,CACF,CAEA,yBACE,iBACE,yBACF,CACF,CClHA,OAEE,kCAAmC,CAKnC,oBAAqB,CAJrB,gBAAkB,CAClB,eAAgB,CAEhB,qBAAuB,CALvB,2CAA4C,CAI5C,wBAGF,CAEA,iBACE,oBAAmC,CACnC,aACF,CAEA,iBACE,oBAAmC,CACnC,aACF,CAEA,sBACE,oBAAmC,CACnC,aACF,CAEA,cACE,oBAAmC,CACnC,aACF,CAEA,gBACE,oBAAkC,CAClC,aACF,CAEA,eACE,oBAAoC,CACpC,aACF,CAGA,cAEE,kCAAmC,CAKnC,oBAAqB,CAJrB,gBAAkB,CAClB,eAAgB,CAEhB,qBAAuB,CALvB,2CAA4C,CAI5C,wBAGF,CAEA,YACE,oBAAmC,CACnC,aACF,CAEA,iBACE,oBAAmC,CACnC,aACF,CAEA,cACE,oBAAmC,CACnC,aACF,CAEA,gBACE,oBAAkC,CAClC,aACF,CAEA,gBACE,oBAAoC,CACpC,aACF,CAGA,eACE,+BAAgC,CAGhC,kCAAmC,CAFnC,kBAAmB,CAKnB,oBAAqB,CAFrB,gBAAkB,CAClB,eAAgB,CAHhB,2CAKF,CCtFA,gBACE,YAAa,CACb,qBAAsB,CACtB,sBACF,CAEA,YAQE,kBAAmB,CAInB,eAAgB,CAVhB,WAAY,CACZ,kCAAmC,CACnC,cAAe,CAGf,mBAAoB,CAFpB,iBAAmB,CAMnB,WAAY,CAFZ,sBAAuB,CACvB,cAAe,CATf,aAAe,CAKf,4BAOF,CAEA,UACE,kCAAmC,CACnC,kBACF,CAEA,gBACE,wBAAyB,CACzB,0BACF,CAEA,UACE,qCAAsC,CACtC,kBACF,CAEA,gBACE,wBAAyB,CACzB,0BACF,CAEA,YACE,oCAAqC,CACrC,kBACF,CAEA,kBACE,wBAAyB,CACzB,0BACF,CAEA,yBACE,YAEE,cAAe,CADf,iBAAkB,CAElB,eACF,CACF,CCzDA,gBAEE,kBAAmB,CADnB,YAAa,CAGb,qBAAsB,CADtB,6BAEF,CAEA,gBAEE,2BAA4B,CAD5B,iBAAmB,CAEnB,eACF,CAEA,iBAGE,gCAAiC,CADjC,kCAAmC,CADnC,eAGF,CAEA,MAEE,wBAAyB,CACzB,iBAAmB,CAFnB,UAGF,CAEA,MAGE,uCAAwC,CAFxC,mBAAqB,CACrB,eAAgB,CAEhB,qBACF,CAEA,GACE,gCAAiC,CACjC,kBAAmB,CACnB,eAAgB,CAChB,uBAAgB,CAAhB,eAAgB,CAChB,KAAM,CAEN,kBAAmB,CADnB,UAEF,CAEA,YACE,cAAe,CAEf,iBAAkB,CADlB,wBAAiB,CAAjB,gBAEF,CAEA,kBACE,gCACF,CAEA,WAGE,gBAAkB,CAFlB,6BAA8B,CAC9B,UAEF,CAEA,6BACE,SACF,CAEA,SACE,iCACF,CAEA,eACE,+BACF,CAEA,uBACE,0BACF,CAEA,6BACE,+BACF,CAEA,iBACE,oCACF,CAEA,uBACE,oCACF,CAGA,cACE,YAAa,CACb,qBAAsB,CACtB,qBACF,CAEA,YAEE,yBAA0B,CAD1B,eAEF,CAEA,eAEE,uBAAwB,CADxB,gBAEF,CAEA,QACE,0BAA2B,CAM3B,gCAAiC,CAJjC,kCAAmC,CAGnC,yBAA0B,CAF1B,8CAAwD,CACxD,gBAAkB,CAHlB,2CAMF,CAGA,WAKE,kBAAmB,CAEnB,eAAgB,CAChB,WAAY,CAPZ,0BAA2B,CAQ3B,cAAe,CALf,mBAAoB,CAMpB,iBAAmB,CAPnB,eAAgB,CAGhB,qBAAsB,CAJtB,oBASF,CAEA,iBAEE,0BAA2B,CAD3B,yBAEF,CAGA,aAGE,2BAA4B,CAD5B,0BAA2B,CAD3B,iBAGF,CAEA,eAEE,uBAAwB,CADxB,cAAe,CAEf,+BACF,CAEA,gBAGE,yBAA0B,CAF1B,iBAAkB,CAClB,eAAgB,CAEhB,8BACF,CAEA,eACE,iBAAmB,CACnB,QACF,CAGA,yBACE,gBAEE,sBAAuB,CADvB,qBAAsB,CAEtB,qBACF,CAEA,MAEE,gBAAkB,CADlB,yBAEF,CACF,CCzKA,gBAGE,kBAAmB,CADnB,6BAA8B,CAG9B,yBAEF,CAEA,qDARE,YAAa,CAKb,cAAe,CAFf,qBAUF,CAEA,4BACE,eACF,CAGA,yBACE,gBACE,qBAAsB,CACtB,yBACF,CAEA,qCAGE,sBAAuB,CADvB,UAEF,CACF,CChCA,MAEE,uBAAwB,CACxB,uBAAwB,CACxB,yBAA0B,CAC1B,sBAAuB,CACvB,uBAAwB,CACxB,uBAAwB,CACxB,sBAAuB,CACvB,oBAAqB,CAGrB,YAAgB,CAChB,iBAAkB,CAClB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CACnB,kBAAmB,CAGnB,iBAAqB,CACrB,sBAAuB,CACvB,qBAAsB,CAGtB,sBAAuB,CACvB,wBAAyB,CACzB,oBAAqB,CAGrB,mBAAoB,CACpB,uBAAwB,CACxB,uBAAwB,CACxB,wDAA6E,CAC7E,iEAAsF,CACtF,mEAAwF,CACxF,+CAAmD,CACnD,uCAAwC,CAGxC,oBAAqB,CACrB,mBAAoB,CACpB,iBAAkB,CAClB,mBAAoB,CACpB,iBAAkB,CAClB,kBACF,CAGA,EACE,qBACF,CAEA,KAKE,kDAAiF,CAAjF,6EAAiF,CADjF,aAA0B,CAA1B,yBAA0B,CAH1B,6EAAuF,CAOvF,cAAe,CAFf,eAAgB,CAJhB,QAAS,CACT,SAMF,CAGA,UALE,gBAQF,CAHA,KAEE,iBACF,CAEA,WAEE,aAAc,CADd,gBAAiB,CAGjB,gBAAiB,CADjB,cAA0B,CAA1B,yBAEF,CAGA,SACE,eAA6B,CAA7B,4BAA6B,CAK7B,wBAAiC,CAAjC,gCAAiC,CAJjC,kBAAsC,CAAtC,qCAAsC,CACtC,sDAA6B,CAA7B,4BAA6B,CAC7B,kBAAgC,CAAhC,+BAAgC,CAChC,eAEF,CAEA,gBAGE,kBAA+B,CAA/B,8BAA+B,CAD/B,+BAAwC,CAAxC,uCAAwC,CADxC,mBAA4C,CAA5C,2CAGF,CAEA,mBAME,kBAAmB,CAHnB,aAA0B,CAA1B,yBAA0B,CAE1B,YAAa,CAJb,iBAAkB,CAClB,eAAgB,CAKhB,SAAsB,CAAtB,qBAAsB,CAHtB,iBAA+B,CAA/B,8BAIF,CAEA,qBACE,aAA2B,CAA3B,0BAA2B,CAC3B,kBACF,CAEA,kBAEE,aAA4B,CAA5B,2BAA4B,CAD5B,iBAAmB,CAEnB,QACF,CAGA,KAEE,kBAAmB,CAYnB,eAAgB,CAThB,sBAA6B,CAC7B,iBAAmC,CAAnC,kCAAmC,CAInC,cAAe,CATf,mBAAoB,CAMpB,iBAAmB,CACnB,eAAgB,CALhB,SAAsB,CAAtB,qBAAsB,CACtB,qBAAuB,CAKvB,oBAAqB,CAErB,0CAA6B,CAA7B,4BAA6B,CAE7B,wBAAiB,CAAjB,gBAAiB,CADjB,kBAGF,CAEA,cAEE,kBAAmB,CADnB,UAEF,CAEA,aACE,wBAAsC,CAAtC,qCAAsC,CAEtC,oBAAkC,CAAlC,iCAAkC,CADlC,UAAmB,CAAnB,kBAEF,CAEA,kCACE,wBAAsC,CAAtC,qCAAsC,CACtC,oBAAkC,CAAlC,iCAAkC,CAElC,4DAAgC,CAAhC,+BAAgC,CADhC,0BAEF,CAEA,eACE,wBAAiC,CAAjC,gCAAiC,CAEjC,oBAA6B,CAA7B,4BAA6B,CAD7B,aAA0B,CAA1B,yBAEF,CAEA,oCACE,wBAAiC,CAAjC,gCAAiC,CACjC,oBAA6B,CAA7B,4BACF,CAEA,aACE,wBAA6B,CAE7B,oBAA6B,CAA7B,4BAA6B,CAD7B,aAA0B,CAA1B,yBAEF,CAEA,kCACE,wBAAgC,CAAhC,+BAAgC,CAChC,oBAA6B,CAA7B,4BACF,CAEA,YACE,wBAAqC,CAArC,oCAAqC,CAErC,oBAAiC,CAAjC,gCAAiC,CADjC,UAAmB,CAAnB,kBAEF,CAEA,iCACE,wBAAyB,CACzB,oBACF,CAGA,QACE,sBACF,CAEA,SAOE,kBAAsB,CAEtB,QAAS,CANT,UAAW,CAEX,WAAY,CACZ,eAAgB,CAFhB,SAAU,CAHV,iBAAkB,CAOlB,kBAAmB,CANnB,SAQF,CAEA,aACE,iBACF,CAEA,WACE,eACF,CAEA,YACE,gBACF,CAEA,YACE,uBAAmC,CAAnC,iCACF,CAEA,cACE,uBAAqC,CAArC,mCACF,CAEA,gBACE,uBAAuC,CAAvC,qCACF,CAGA,iBAQE,kBAAmB,CAFnB,oBAA8B,CAD9B,QAAS,CAET,YAAa,CAEb,sBAAuB,CANvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,iBACE,eAAwB,CAAxB,uBAAwB,CAExB,kBAAsC,CAAtC,qCAAsC,CAEtC,8DAAgC,CAAhC,+BAAgC,CAHhC,YAA0B,CAA1B,yBAA0B,CAE1B,iBAEF,CAEA,mBAIE,iCAAkC,CAFlC,aAA2B,CAA3B,0BAA2B,CAD3B,cAAe,CAEf,kBAAgC,CAAhC,+BAEF,CAEA,mBAEE,aAA0B,CAA1B,yBAA0B,CAC1B,eAAgB,CAFhB,QAGF,CAEA,gBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAGA,0BACE,WACE,YAA0B,CAA1B,yBACF,CACF,CAEA,yBACE,WACE,aAA0B,CAA1B,yBACF,CAEA,gBACE,YAA0B,CAA1B,yBACF,CACF,CAEA,yBACE,WACE,aAA0B,CAA1B,yBACF,CACF", "sources": ["components/Header.css", "components/WelcomeMessage.css", "components/FileUpload.css", "components/DocumentForm.css", "components/SearchFilters.css", "components/Badge.css", "components/ActionButtons.css", "components/DocumentTable.css", "components/ExportControls.css", "styles/App.css"], "sourcesContent": [".app-header {\n  text-align: center;\n  margin-bottom: var(--spacing-2xl);\n  padding: var(--spacing-xl) 0;\n}\n\n.app-header h1 {\n  font-size: 2.5rem;\n  font-weight: 700;\n  color: var(--text-primary);\n  margin: 0 0 var(--spacing-sm) 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--spacing-md);\n}\n\n.app-header h1 i {\n  color: var(--primary-color);\n  font-size: 2.25rem;\n}\n\n.app-subtitle {\n  font-size: 1.125rem;\n  color: var(--text-secondary);\n  margin: 0;\n  font-weight: 400;\n}\n\n@media (max-width: 768px) {\n  .app-header h1 {\n    font-size: 2rem;\n    flex-direction: column;\n    gap: var(--spacing-sm);\n  }\n\n  .app-header h1 i {\n    font-size: 1.75rem;\n  }\n}\n\n@media (max-width: 480px) {\n  .app-header {\n    padding: var(--spacing-md) 0;\n  }\n\n  .app-header h1 {\n    font-size: 1.5rem;\n  }\n}\n", ".welcome-message {\n  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));\n  color: var(--white);\n  border-radius: var(--border-radius-lg);\n  padding: var(--spacing-lg);\n  margin-bottom: var(--spacing-xl);\n  box-shadow: var(--box-shadow-md);\n  animation: slideDown 0.5s ease-out;\n}\n\n.welcome-content {\n  display: flex;\n  align-items: flex-start;\n  gap: var(--spacing-md);\n}\n\n.welcome-content i {\n  font-size: 1.5rem;\n  margin-top: var(--spacing-xs);\n  opacity: 0.9;\n}\n\n.welcome-content h3 {\n  margin: 0 0 var(--spacing-sm) 0;\n  font-size: 1.125rem;\n  font-weight: 600;\n}\n\n.welcome-content p {\n  margin: 0 0 var(--spacing-md) 0;\n  opacity: 0.9;\n  line-height: 1.5;\n}\n\n.feature-list {\n  margin: 0 0 var(--spacing-md) 0;\n  padding-left: var(--spacing-lg);\n  opacity: 0.9;\n}\n\n.feature-list li {\n  margin-bottom: var(--spacing-sm);\n  line-height: 1.4;\n}\n\n.feature-list strong {\n  color: var(--white);\n  opacity: 1;\n}\n\n.btn-link {\n  background: none;\n  border: none;\n  color: var(--white);\n  text-decoration: underline;\n  cursor: pointer;\n  font-size: 0.875rem;\n  padding: 0;\n  opacity: 0.8;\n  transition: var(--transition);\n}\n\n.btn-link:hover {\n  opacity: 1;\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n", ".file-upload-container {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-md);\n}\n\n.file-upload-controls {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-md);\n  margin-bottom: var(--spacing-md);\n}\n\n.file-btn {\n  background-color: var(--primary-color);\n  color: var(--white);\n  border: 1px solid var(--primary-color);\n  padding: 0.75rem 1.5rem;\n  border-radius: var(--border-radius);\n  font-size: 0.875rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: var(--transition);\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n\n.file-btn:hover {\n  background-color: var(--primary-hover);\n  border-color: var(--primary-hover);\n  transform: translateY(-1px);\n}\n\n.file-name {\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n  font-style: italic;\n  flex: 1;\n  min-width: 0;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.remove-file-btn {\n  background: var(--danger-color);\n  color: var(--white);\n  border: none;\n  border-radius: 50%;\n  width: 2rem;\n  height: 2rem;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  transition: var(--transition);\n  font-size: 0.75rem;\n}\n\n.remove-file-btn:hover {\n  background: #dc2626;\n  transform: scale(1.1);\n}\n\n.file-drop-zone {\n  border: 2px dashed var(--gray-300);\n  border-radius: var(--border-radius);\n  padding: var(--spacing-xl);\n  text-align: center;\n  background: var(--bg-secondary);\n  transition: var(--transition);\n  cursor: pointer;\n}\n\n.file-drop-zone:hover,\n.file-drop-zone.dragover {\n  border-color: var(--primary-color);\n  background: rgba(79, 70, 229, 0.05);\n}\n\n.file-drop-zone i {\n  font-size: 2rem;\n  color: var(--text-muted);\n  margin-bottom: var(--spacing-sm);\n}\n\n.file-drop-zone p {\n  margin: 0 0 var(--spacing-xs) 0;\n  color: var(--text-secondary);\n  font-size: 0.875rem;\n}\n\n.file-drop-zone small {\n  color: var(--text-muted);\n  font-size: 0.75rem;\n}\n\n/* Responsive Design */\n@media (max-width: 480px) {\n  .file-upload-controls {\n    flex-direction: column;\n    align-items: stretch;\n    gap: var(--spacing-sm);\n  }\n\n  .file-drop-zone {\n    padding: var(--spacing-md);\n  }\n\n  .file-drop-zone i {\n    font-size: 1.5rem;\n  }\n}\n", "/* Form Styles */\n.input-form {\n  padding: var(--spacing-xl);\n}\n\n.form-row {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n  gap: var(--spacing-lg);\n  margin-bottom: var(--spacing-lg);\n}\n\n.form-group {\n  position: relative;\n}\n\n.form-group label {\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  margin-bottom: var(--spacing-sm);\n  font-weight: 500;\n  color: var(--text-primary);\n  font-size: 0.875rem;\n}\n\n.form-group label i {\n  color: var(--primary-color);\n  font-size: 0.875rem;\n}\n\n.form-group label.required::after {\n  content: '*';\n  color: var(--danger-color);\n  margin-left: var(--spacing-xs);\n  font-weight: 600;\n}\n\n.form-group input,\n.form-group select {\n  width: 100%;\n  padding: 0.75rem;\n  border: 1px solid var(--gray-300);\n  border-radius: var(--border-radius);\n  transition: var(--transition);\n  font-size: 0.875rem;\n  background: var(--bg-primary);\n  color: var(--text-primary);\n}\n\n.form-group input:focus,\n.form-group select:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\n  background: var(--white);\n}\n\n.form-group input::placeholder {\n  color: var(--text-muted);\n}\n\n.form-group input.error,\n.form-group select.error {\n  border-color: var(--danger-color);\n  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);\n}\n\n/* Error Messages */\n.error-message {\n  color: var(--danger-color);\n  font-size: 0.75rem;\n  margin-top: var(--spacing-xs);\n  font-weight: 500;\n}\n\n/* Tooltip Styles */\n.tooltip-container {\n  position: relative;\n}\n\n.tooltip {\n  position: absolute;\n  right: 0.75rem;\n  top: 2.25rem;\n  cursor: help;\n  color: var(--text-muted);\n  z-index: 10;\n}\n\n.tooltip:hover .tooltip-text {\n  visibility: visible;\n  opacity: 1;\n}\n\n.tooltip-text {\n  visibility: hidden;\n  width: 200px;\n  background-color: var(--gray-800);\n  color: var(--white);\n  text-align: center;\n  border-radius: var(--border-radius);\n  padding: var(--spacing-sm);\n  position: absolute;\n  z-index: 20;\n  bottom: 125%;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.3s;\n  font-size: 0.75rem;\n  font-weight: 400;\n}\n\n.tooltip-text::after {\n  content: \"\";\n  position: absolute;\n  top: 100%;\n  right: 1rem;\n  margin-left: -5px;\n  border-width: 5px;\n  border-style: solid;\n  border-color: var(--gray-800) transparent transparent transparent;\n}\n\n/* File Upload Group */\n.file-upload-group {\n  grid-column: 1 / -1;\n}\n\n/* Form Actions */\n.form-actions {\n  display: flex;\n  justify-content: flex-end;\n  gap: var(--spacing-md);\n  padding-top: var(--spacing-lg);\n  border-top: 1px solid var(--gray-200);\n  margin-top: var(--spacing-lg);\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .form-row {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n\n  .form-actions {\n    flex-direction: column-reverse;\n  }\n\n  .input-form {\n    padding: var(--spacing-md);\n  }\n}\n\n@media (max-width: 480px) {\n  .input-form {\n    padding: var(--spacing-md);\n  }\n}\n", ".search-controls {\n  padding: var(--spacing-xl);\n  display: grid;\n  grid-template-columns: 2fr 1fr;\n  gap: var(--spacing-lg);\n  align-items: end;\n}\n\n.search-group {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-sm);\n}\n\n.search-input-container {\n  position: relative;\n  display: flex;\n  align-items: center;\n}\n\n.search-icon {\n  position: absolute;\n  left: 0.75rem;\n  color: var(--text-muted);\n  font-size: 0.875rem;\n  z-index: 5;\n}\n\n#searchInput {\n  width: 100%;\n  padding: 0.75rem 2.5rem 0.75rem 2.5rem;\n  border: 1px solid var(--gray-300);\n  border-radius: var(--border-radius);\n  font-size: 0.875rem;\n  background: var(--bg-primary);\n  color: var(--text-primary);\n  transition: var(--transition);\n}\n\n#searchInput:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\n  background: var(--white);\n}\n\n#searchInput::placeholder {\n  color: var(--text-muted);\n}\n\n.clear-search {\n  position: absolute;\n  right: 0.5rem;\n  background: none;\n  border: none;\n  color: var(--text-muted);\n  cursor: pointer;\n  padding: 0.25rem;\n  border-radius: 50%;\n  transition: var(--transition);\n  display: none;\n  width: 1.5rem;\n  height: 1.5rem;\n  align-items: center;\n  justify-content: center;\n}\n\n.clear-search:hover {\n  background-color: var(--gray-100);\n  color: var(--text-primary);\n}\n\n.clear-search.show {\n  display: flex;\n}\n\n.filter-group {\n  display: flex;\n  gap: var(--spacing-md);\n}\n\n.filter-group select {\n  flex: 1;\n  padding: 0.75rem;\n  border: 1px solid var(--gray-300);\n  border-radius: var(--border-radius);\n  background: var(--bg-primary);\n  font-size: 0.875rem;\n  color: var(--text-primary);\n  transition: var(--transition);\n}\n\n.filter-group select:focus {\n  outline: none;\n  border-color: var(--primary-color);\n  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);\n  background: var(--white);\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .search-controls {\n    grid-template-columns: 1fr;\n    gap: var(--spacing-md);\n  }\n\n  .filter-group {\n    flex-direction: column;\n  }\n}\n\n@media (max-width: 768px) {\n  .search-controls {\n    padding: var(--spacing-md);\n  }\n}\n", "/* Type Badges */\n.badge {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius);\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  display: inline-block;\n}\n\n.badge-processus {\n  background: rgba(59, 130, 246, 0.1);\n  color: #1d4ed8;\n}\n\n.badge-procedure {\n  background: rgba(16, 185, 129, 0.1);\n  color: #047857;\n}\n\n.badge-enregistrement {\n  background: rgba(245, 158, 11, 0.1);\n  color: #92400e;\n}\n\n.badge-manuel {\n  background: rgba(139, 92, 246, 0.1);\n  color: #6b21a8;\n}\n\n.badge-logiciel {\n  background: rgba(239, 68, 68, 0.1);\n  color: #b91c1c;\n}\n\n.badge-default {\n  background: rgba(107, 114, 128, 0.1);\n  color: #374151;\n}\n\n/* Status Badges */\n.status-badge {\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius);\n  font-size: 0.75rem;\n  font-weight: 500;\n  text-transform: uppercase;\n  letter-spacing: 0.025em;\n  display: inline-block;\n}\n\n.status-ras {\n  background: rgba(16, 185, 129, 0.1);\n  color: #047857;\n}\n\n.status-modifier {\n  background: rgba(245, 158, 11, 0.1);\n  color: #92400e;\n}\n\n.status-creer {\n  background: rgba(59, 130, 246, 0.1);\n  color: #1d4ed8;\n}\n\n.status-annuler {\n  background: rgba(239, 68, 68, 0.1);\n  color: #b91c1c;\n}\n\n.status-default {\n  background: rgba(107, 114, 128, 0.1);\n  color: #374151;\n}\n\n/* Version Badge */\n.version-badge {\n  background: var(--primary-color);\n  color: var(--white);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius);\n  font-size: 0.75rem;\n  font-weight: 500;\n  display: inline-block;\n}\n", ".action-buttons {\n  display: flex;\n  gap: var(--spacing-xs);\n  justify-content: center;\n}\n\n.action-btn {\n  padding: 0.5rem;\n  border: none;\n  border-radius: var(--border-radius);\n  cursor: pointer;\n  font-size: 0.875rem;\n  transition: var(--transition);\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  min-width: 2rem;\n  height: 2rem;\n  background: none;\n}\n\n.view-btn {\n  background-color: var(--info-color);\n  color: var(--white);\n}\n\n.view-btn:hover {\n  background-color: #2563eb;\n  transform: translateY(-1px);\n}\n\n.edit-btn {\n  background-color: var(--warning-color);\n  color: var(--white);\n}\n\n.edit-btn:hover {\n  background-color: #d97706;\n  transform: translateY(-1px);\n}\n\n.delete-btn {\n  background-color: var(--danger-color);\n  color: var(--white);\n}\n\n.delete-btn:hover {\n  background-color: #dc2626;\n  transform: translateY(-1px);\n}\n\n@media (max-width: 768px) {\n  .action-btn {\n    min-width: 1.75rem;\n    height: 1.75rem;\n    padding: 0.375rem;\n  }\n}\n", ".table-controls {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  gap: var(--spacing-md);\n}\n\n.document-count {\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n  font-weight: 500;\n}\n\n.table-container {\n  overflow-x: auto;\n  border-radius: var(--border-radius);\n  border: 1px solid var(--gray-200);\n}\n\ntable {\n  width: 100%;\n  border-collapse: collapse;\n  font-size: 0.875rem;\n}\n\nth, td {\n  padding: 0.75rem 1rem;\n  text-align: left;\n  border-bottom: 1px solid var(--gray-200);\n  vertical-align: middle;\n}\n\nth {\n  background-color: var(--gray-800);\n  color: var(--white);\n  font-weight: 600;\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  white-space: nowrap;\n}\n\nth.sortable {\n  cursor: pointer;\n  user-select: none;\n  position: relative;\n}\n\nth.sortable:hover {\n  background-color: var(--gray-700);\n}\n\n.sort-icon {\n  margin-left: var(--spacing-sm);\n  opacity: 0.5;\n  font-size: 0.75rem;\n}\n\nth.sortable:hover .sort-icon {\n  opacity: 1;\n}\n\ntbody tr {\n  transition: var(--transition-fast);\n}\n\ntbody tr:hover {\n  background-color: var(--gray-50);\n}\n\ntbody tr:nth-child(even) {\n  background-color: rgba(249, 250, 251, 0.5);\n}\n\ntbody tr:nth-child(even):hover {\n  background-color: var(--gray-50);\n}\n\n.status-modifier {\n  background-color: rgba(245, 158, 11, 0.1) !important;\n}\n\n.status-modifier:hover {\n  background-color: rgba(245, 158, 11, 0.15) !important;\n}\n\n/* Table Cell Enhancements */\n.cell-content {\n  display: flex;\n  flex-direction: column;\n  gap: var(--spacing-xs);\n}\n\n.cell-title {\n  font-weight: 500;\n  color: var(--text-primary);\n}\n\n.cell-subtitle {\n  font-size: 0.75rem;\n  color: var(--text-muted);\n}\n\n.ref-id {\n  background: var(--gray-100);\n  padding: var(--spacing-xs) var(--spacing-sm);\n  border-radius: var(--border-radius);\n  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;\n  font-size: 0.75rem;\n  color: var(--text-primary);\n  border: 1px solid var(--gray-200);\n}\n\n/* File Link */\n.file-link {\n  color: var(--primary-color);\n  text-decoration: none;\n  font-weight: 500;\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-xs);\n  background: none;\n  border: none;\n  cursor: pointer;\n  font-size: 0.875rem;\n}\n\n.file-link:hover {\n  text-decoration: underline;\n  color: var(--primary-hover);\n}\n\n/* Empty State */\n.empty-state {\n  text-align: center;\n  padding: var(--spacing-2xl);\n  color: var(--text-secondary);\n}\n\n.empty-state i {\n  font-size: 3rem;\n  color: var(--text-muted);\n  margin-bottom: var(--spacing-md);\n}\n\n.empty-state h3 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0 0 var(--spacing-sm) 0;\n}\n\n.empty-state p {\n  font-size: 0.875rem;\n  margin: 0;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .table-controls {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: var(--spacing-sm);\n  }\n\n  th, td {\n    padding: var(--spacing-sm);\n    font-size: 0.75rem;\n  }\n}\n", ".export-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: var(--spacing-md);\n  padding: var(--spacing-xl);\n  flex-wrap: wrap;\n}\n\n.export-group,\n.data-management-group {\n  display: flex;\n  gap: var(--spacing-md);\n  flex-wrap: wrap;\n}\n\n.data-management-group .btn {\n  font-size: 0.8rem;\n}\n\n/* Responsive Design */\n@media (max-width: 768px) {\n  .export-options {\n    flex-direction: column;\n    padding: var(--spacing-md);\n  }\n\n  .export-group,\n  .data-management-group {\n    width: 100%;\n    justify-content: center;\n  }\n}\n", ":root {\n  /* Enhanced Color Palette */\n  --primary-color: #4f46e5;\n  --primary-hover: #4338ca;\n  --secondary-color: #6366f1;\n  --accent-color: #f59e0b;\n  --success-color: #10b981;\n  --warning-color: #f59e0b;\n  --danger-color: #ef4444;\n  --info-color: #3b82f6;\n\n  /* Neutral Colors */\n  --white: #ffffff;\n  --gray-50: #f9fafb;\n  --gray-100: #f3f4f6;\n  --gray-200: #e5e7eb;\n  --gray-300: #d1d5db;\n  --gray-400: #9ca3af;\n  --gray-500: #6b7280;\n  --gray-600: #4b5563;\n  --gray-700: #374151;\n  --gray-800: #1f2937;\n  --gray-900: #111827;\n\n  /* Background Colors */\n  --bg-primary: #ffffff;\n  --bg-secondary: #f9fafb;\n  --bg-tertiary: #f3f4f6;\n\n  /* Text Colors */\n  --text-primary: #111827;\n  --text-secondary: #6b7280;\n  --text-muted: #9ca3af;\n\n  /* Design System */\n  --border-radius: 8px;\n  --border-radius-lg: 12px;\n  --border-radius-xl: 16px;\n  --box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);\n  --box-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n  --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);\n  --transition-fast: all 0.15s ease-in-out;\n\n  /* Spacing */\n  --spacing-xs: 0.25rem;\n  --spacing-sm: 0.5rem;\n  --spacing-md: 1rem;\n  --spacing-lg: 1.5rem;\n  --spacing-xl: 2rem;\n  --spacing-2xl: 3rem;\n}\n\n/* Reset and Base Styles */\n* {\n  box-sizing: border-box;\n}\n\nbody {\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n  margin: 0;\n  padding: 0;\n  color: var(--text-primary);\n  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--gray-100) 100%);\n  line-height: 1.6;\n  min-height: 100vh;\n  font-size: 14px;\n}\n\n/* Layout Components */\n.app {\n  min-height: 100vh;\n  position: relative;\n}\n\n.container {\n  max-width: 1400px;\n  margin: 0 auto;\n  padding: var(--spacing-lg);\n  min-height: 100vh;\n}\n\n/* Section Styles */\n.section {\n  background: var(--bg-primary);\n  border-radius: var(--border-radius-lg);\n  box-shadow: var(--box-shadow);\n  margin-bottom: var(--spacing-xl);\n  overflow: hidden;\n  border: 1px solid var(--gray-200);\n}\n\n.section-header {\n  padding: var(--spacing-lg) var(--spacing-xl);\n  border-bottom: 1px solid var(--gray-200);\n  background: var(--bg-secondary);\n}\n\n.section-header h2 {\n  font-size: 1.25rem;\n  font-weight: 600;\n  color: var(--text-primary);\n  margin: 0 0 var(--spacing-xs) 0;\n  display: flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n}\n\n.section-header h2 i {\n  color: var(--primary-color);\n  font-size: 1.125rem;\n}\n\n.section-header p {\n  font-size: 0.875rem;\n  color: var(--text-secondary);\n  margin: 0;\n}\n\n/* Enhanced Button System */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  gap: var(--spacing-sm);\n  padding: 0.75rem 1.5rem;\n  border: 1px solid transparent;\n  border-radius: var(--border-radius);\n  font-size: 0.875rem;\n  font-weight: 500;\n  text-decoration: none;\n  cursor: pointer;\n  transition: var(--transition);\n  white-space: nowrap;\n  user-select: none;\n  background: none;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n.btn-primary {\n  background-color: var(--primary-color);\n  color: var(--white);\n  border-color: var(--primary-color);\n}\n\n.btn-primary:hover:not(:disabled) {\n  background-color: var(--primary-hover);\n  border-color: var(--primary-hover);\n  transform: translateY(-1px);\n  box-shadow: var(--box-shadow-md);\n}\n\n.btn-secondary {\n  background-color: var(--gray-100);\n  color: var(--text-primary);\n  border-color: var(--gray-300);\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background-color: var(--gray-200);\n  border-color: var(--gray-400);\n}\n\n.btn-outline {\n  background-color: transparent;\n  color: var(--text-primary);\n  border-color: var(--gray-300);\n}\n\n.btn-outline:hover:not(:disabled) {\n  background-color: var(--gray-50);\n  border-color: var(--gray-400);\n}\n\n.btn-danger {\n  background-color: var(--danger-color);\n  color: var(--white);\n  border-color: var(--danger-color);\n}\n\n.btn-danger:hover:not(:disabled) {\n  background-color: #dc2626;\n  border-color: #dc2626;\n}\n\n/* Utility Classes */\n.hidden {\n  display: none !important;\n}\n\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border: 0;\n}\n\n.text-center {\n  text-align: center;\n}\n\n.text-left {\n  text-align: left;\n}\n\n.text-right {\n  text-align: right;\n}\n\n.text-muted {\n  color: var(--text-muted) !important;\n}\n\n.text-primary {\n  color: var(--text-primary) !important;\n}\n\n.text-secondary {\n  color: var(--text-secondary) !important;\n}\n\n/* Loading Overlay */\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 1000;\n}\n\n.loading-spinner {\n  background: var(--white);\n  padding: var(--spacing-xl);\n  border-radius: var(--border-radius-lg);\n  text-align: center;\n  box-shadow: var(--box-shadow-lg);\n}\n\n.loading-spinner i {\n  font-size: 2rem;\n  color: var(--primary-color);\n  margin-bottom: var(--spacing-md);\n  animation: spin 1s linear infinite;\n}\n\n.loading-spinner p {\n  margin: 0;\n  color: var(--text-primary);\n  font-weight: 500;\n}\n\n@keyframes spin {\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n}\n\n/* Responsive Design */\n@media (max-width: 1024px) {\n  .container {\n    padding: var(--spacing-md);\n  }\n}\n\n@media (max-width: 768px) {\n  .container {\n    padding: var(--spacing-sm);\n  }\n\n  .section-header {\n    padding: var(--spacing-md);\n  }\n}\n\n@media (max-width: 480px) {\n  .container {\n    padding: var(--spacing-sm);\n  }\n}\n"], "names": [], "sourceRoot": ""}