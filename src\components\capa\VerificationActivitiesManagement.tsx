import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { CAPA, CorrectiveAction, PreventiveAction, VerificationActivity } from '@/models/capa';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, PenLine, Trash2, Search, AlertTriangle, CheckCircle, FileText, Calendar } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { format } from 'date-fns';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';

interface VerificationActivitiesManagementProps {
  verificationActivities: VerificationActivity[] | any[]; // Allow for allergen verification activities
  setVerificationActivities: React.Dispatch<React.SetStateAction<VerificationActivity[] | any[]>>;
  capas?: CAPA[];
  correctiveActions?: CorrectiveAction[];
  preventiveActions?: PreventiveAction[];
  allergens?: any[]; // For allergen management
  products?: any[]; // For allergen management
}

export function VerificationActivitiesManagement({
  verificationActivities,
  setVerificationActivities,
  capas,
  correctiveActions,
  preventiveActions,
  allergens,
  products
}: VerificationActivitiesManagementProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<VerificationActivity | null>(null);
  const [selectedCapa, setSelectedCapa] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Form state
  const [formValues, setFormValues] = useState<Partial<VerificationActivity>>({
    capaId: '',
    description: '',
    date: new Date(),
    method: '',
    result: 'pending',
    findings: '',
    conductedBy: ''
  });

  // Filter activities based on search query and filters
  const filteredActivities = verificationActivities.filter(activity => {
    // Filter by search query
    const matchesSearch =
      (activity.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
       // For allergen verification activities which use 'details' instead of 'description'
       (activity as any).details?.toLowerCase().includes(searchQuery.toLowerCase())) &&
      (activity.method?.toLowerCase().includes(searchQuery.toLowerCase()) || true) &&
      (activity.conductedBy?.toLowerCase().includes(searchQuery.toLowerCase()) || true) &&
      (activity.reviewedBy ? activity.reviewedBy.toLowerCase().includes(searchQuery.toLowerCase()) : true);

    // Filter by CAPA
    const matchesCapa = selectedCapa === 'all' ? true : activity.capaId === selectedCapa;

    // Filter by status
    const matchesStatus = filterStatus === 'all' ? true : activity.result === filterStatus;

    return matchesSearch && matchesCapa && matchesStatus;
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setFormValues(prev => ({ ...prev, date }));
    }
  };

  const openAddDialog = () => {
    setFormValues({
      capaId: '',
      description: '',
      date: new Date(),
      method: '',
      result: 'pending',
      findings: '',
      conductedBy: ''
    });
    setIsAddDialogOpen(true);
  };

  const openEditDialog = (activity: VerificationActivity) => {
    setSelectedActivity(activity);
    setFormValues({
      capaId: activity.capaId,
      description: activity.description,
      date: activity.date,
      method: activity.method,
      result: activity.result,
      findings: activity.findings,
      conductedBy: activity.conductedBy,
      reviewedBy: activity.reviewedBy,
      reviewDate: activity.reviewDate
    });
    setIsEditDialogOpen(true);
  };

  const handleAddActivity = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.capaId) {
      toast({
        title: "Error",
        description: "Please select a CAPA",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.description?.trim()) {
      toast({
        title: "Error",
        description: "Description is required",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.method?.trim()) {
      toast({
        title: "Error",
        description: "Verification method is required",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.conductedBy?.trim()) {
      toast({
        title: "Error",
        description: "Conducted by field is required",
        variant: "destructive"
      });
      return;
    }

    const newActivity: VerificationActivity = {
      id: `v${verificationActivities.length + 1}`,
      capaId: formValues.capaId || '',
      description: formValues.description || '',
      date: formValues.date || new Date(),
      method: formValues.method || '',
      result: formValues.result as 'pass' | 'fail' | 'pending',
      findings: formValues.findings || '',
      conductedBy: formValues.conductedBy || '',
      reviewedBy: formValues.reviewedBy,
      reviewDate: formValues.reviewDate,
      attachments: [],
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };

    setVerificationActivities([...verificationActivities, newActivity]);

    // Update the CAPA to include this verification activity
    // Note: In a real app, you would make an API call here

    setIsAddDialogOpen(false);

    toast({
      title: "Success",
      description: "Verification activity added successfully"
    });
  };

  const handleEditActivity = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.capaId) {
      toast({
        title: "Error",
        description: "Please select a CAPA",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.description?.trim()) {
      toast({
        title: "Error",
        description: "Description is required",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.method?.trim()) {
      toast({
        title: "Error",
        description: "Verification method is required",
        variant: "destructive"
      });
      return;
    }

    if (!formValues.conductedBy?.trim()) {
      toast({
        title: "Error",
        description: "Conducted by field is required",
        variant: "destructive"
      });
      return;
    }

    if (!selectedActivity) return;

    const updatedActivities = verificationActivities.map((activity: any) =>
      activity.id === selectedActivity.id
        ? {
            ...activity,
            capaId: formValues.capaId || activity.capaId,
            description: formValues.description || activity.description,
            date: formValues.date || activity.date,
            method: formValues.method || activity.method,
            result: formValues.result as 'pass' | 'fail' | 'pending',
            findings: formValues.findings || activity.findings,
            conductedBy: formValues.conductedBy || activity.conductedBy,
            reviewedBy: formValues.reviewedBy,
            reviewDate: formValues.reviewDate,
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : activity
    );

    setVerificationActivities(updatedActivities);
    setIsEditDialogOpen(false);

    toast({
      title: "Success",
      description: "Verification activity updated successfully"
    });
  };

  const handleDeleteActivity = (id: string) => {
    setVerificationActivities(verificationActivities.filter(activity => activity.id !== id));

    toast({
      title: "Success",
      description: "Verification activity deleted successfully"
    });
  };

  const getResultBadge = (result: 'pass' | 'fail' | 'pending') => {
    switch (result) {
      case 'pass':
        return <Badge className="bg-green-500"><CheckCircle className="h-3 w-3 mr-1" /> Pass</Badge>;
      case 'fail':
        return <Badge className="bg-red-500"><AlertTriangle className="h-3 w-3 mr-1" /> Fail</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-500"><FileText className="h-3 w-3 mr-1" /> Pending</Badge>;
    }
  };

  const getCapaTitle = (capaId: string) => {
    // Handle case where capas might not be provided (in allergen management)
    if (!capas) return capaId;

    const capa = capas.find(c => c.id === capaId);
    return capa ? capa.title : 'Unknown CAPA';
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Verification Activities</CardTitle>
              <CardDescription>
                Verify the effectiveness of corrective and preventive actions
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Verification
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap items-center gap-4 mb-6">
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search verification activities..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-[250px]"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Label htmlFor="capa-filter" className="text-sm">CAPA:</Label>
              <Select
                value={selectedCapa}
                onValueChange={setSelectedCapa}
              >
                <SelectTrigger id="capa-filter" className="w-[200px]">
                  <SelectValue placeholder="All CAPAs" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All CAPAs</SelectItem>
                  {capas && capas.map(capa => (
                    <SelectItem key={capa.id} value={capa.id}>{capa.title}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Label htmlFor="status-filter" className="text-sm">Status:</Label>
              <Select
                value={filterStatus}
                onValueChange={setFilterStatus}
              >
                <SelectTrigger id="status-filter" className="w-[150px]">
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pass">Pass</SelectItem>
                  <SelectItem value="fail">Fail</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>CAPA</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Result</TableHead>
                  <TableHead>Conducted By</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredActivities.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      No verification activities found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredActivities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell className="font-medium">{getCapaTitle(activity.capaId)}</TableCell>
                      <TableCell>{activity.description}</TableCell>
                      <TableCell>{format(activity.date, 'MMM d, yyyy')}</TableCell>
                      <TableCell>{activity.method}</TableCell>
                      <TableCell>{getResultBadge(activity.result)}</TableCell>
                      <TableCell>{activity.conductedBy}</TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button variant="outline" size="sm" onClick={() => openEditDialog(activity)}>
                            <PenLine className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button variant="destructive" size="sm" onClick={() => handleDeleteActivity(activity.id)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Add Verification Activity Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Add Verification Activity</DialogTitle>
            <DialogDescription>
              Document a verification activity for a CAPA
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleAddActivity}>
            <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto pr-1">
              <div className="space-y-2">
                <Label htmlFor="capaId">Related CAPA</Label>
                <Select
                  value={formValues.capaId || ''}
                  onValueChange={(value) => handleSelectChange('capaId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select CAPA" />
                  </SelectTrigger>
                  <SelectContent>
                    {capas && capas.map(capa => (
                      <SelectItem key={capa.id} value={capa.id}>{capa.title}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formValues.description || ''}
                  onChange={handleInputChange}
                  placeholder="Describe the verification activity"
                  rows={2}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formValues.date ? format(formValues.date, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formValues.date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="method">Verification Method</Label>
                <Textarea
                  id="method"
                  name="method"
                  value={formValues.method || ''}
                  onChange={handleInputChange}
                  placeholder="Describe the method used for verification"
                  rows={2}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="result">Result</Label>
                <Select
                  value={formValues.result || 'pending'}
                  onValueChange={(value) => handleSelectChange('result', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select result" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pass">Pass</SelectItem>
                    <SelectItem value="fail">Fail</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formValues.result !== 'pending' && (
                <div className="space-y-2">
                  <Label htmlFor="findings">Findings</Label>
                  <Textarea
                    id="findings"
                    name="findings"
                    value={formValues.findings || ''}
                    onChange={handleInputChange}
                    placeholder="Document the findings from the verification"
                    rows={2}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="conductedBy">Conducted By</Label>
                <Input
                  id="conductedBy"
                  name="conductedBy"
                  value={formValues.conductedBy || ''}
                  onChange={handleInputChange}
                  placeholder="Name of person who conducted the verification"
                  required
                />
              </div>

              {formValues.result !== 'pending' && (
                <div className="space-y-2">
                  <Label htmlFor="reviewedBy">Reviewed By (Optional)</Label>
                  <Input
                    id="reviewedBy"
                    name="reviewedBy"
                    value={formValues.reviewedBy || ''}
                    onChange={handleInputChange}
                    placeholder="Name of person who reviewed the verification"
                  />
                </div>
              )}
            </div>

            <DialogFooter className="mt-4 border-t pt-4">
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Plus className="h-4 w-4 mr-2" />
                Add Verification
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Verification Activity Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md max-h-[90vh]">
          <DialogHeader>
            <DialogTitle>Edit Verification Activity</DialogTitle>
            <DialogDescription>
              Update verification activity details
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleEditActivity}>
            <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto pr-1">
              <div className="space-y-2">
                <Label htmlFor="edit-capaId">Related CAPA</Label>
                <Select
                  value={formValues.capaId || ''}
                  onValueChange={(value) => handleSelectChange('capaId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select CAPA" />
                  </SelectTrigger>
                  <SelectContent>
                    {capas && capas.map(capa => (
                      <SelectItem key={capa.id} value={capa.id}>{capa.title}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  value={formValues.description || ''}
                  onChange={handleInputChange}
                  placeholder="Describe the verification activity"
                  rows={2}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-date">Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      {formValues.date ? format(formValues.date, 'PPP') : <span>Pick a date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={formValues.date}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-method">Verification Method</Label>
                <Textarea
                  id="edit-method"
                  name="method"
                  value={formValues.method || ''}
                  onChange={handleInputChange}
                  placeholder="Describe the method used for verification"
                  rows={2}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-result">Result</Label>
                <Select
                  value={formValues.result || 'pending'}
                  onValueChange={(value) => handleSelectChange('result', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select result" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="pass">Pass</SelectItem>
                    <SelectItem value="fail">Fail</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {formValues.result !== 'pending' && (
                <div className="space-y-2">
                  <Label htmlFor="edit-findings">Findings</Label>
                  <Textarea
                    id="edit-findings"
                    name="findings"
                    value={formValues.findings || ''}
                    onChange={handleInputChange}
                    placeholder="Document the findings from the verification"
                    rows={2}
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="edit-conductedBy">Conducted By</Label>
                <Input
                  id="edit-conductedBy"
                  name="conductedBy"
                  value={formValues.conductedBy || ''}
                  onChange={handleInputChange}
                  placeholder="Name of person who conducted the verification"
                  required
                />
              </div>

              {formValues.result !== 'pending' && (
                <div className="space-y-2">
                  <Label htmlFor="edit-reviewedBy">Reviewed By (Optional)</Label>
                  <Input
                    id="edit-reviewedBy"
                    name="reviewedBy"
                    value={formValues.reviewedBy || ''}
                    onChange={handleInputChange}
                    placeholder="Name of person who reviewed the verification"
                  />
                </div>
              )}
            </div>

            <DialogFooter className="mt-4 border-t pt-4">
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
