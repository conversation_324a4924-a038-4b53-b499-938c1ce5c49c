import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CAPA, PreventiveAction } from '@/models/capa';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Search, Plus, FileDown, Eye, Edit, CheckCircle } from 'lucide-react';
import { AddPreventiveActionDialog } from './AddPreventiveActionDialog';
import { EditPreventiveActionDialog } from './EditPreventiveActionDialog';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { PreventiveActionDetailsModal } from './PreventiveActionDetailsModal';

interface PreventiveActionsManagementProps {
  preventiveActions: PreventiveAction[];
  setPreventiveActions: React.Dispatch<React.SetStateAction<PreventiveAction[]>>;
  capas: CAPA[];
}

export function PreventiveActionsManagement({
  preventiveActions,
  setPreventiveActions,
  capas
}: PreventiveActionsManagementProps) {
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [openEditDialog, setOpenEditDialog] = useState(false);
  const [selectedAction, setSelectedAction] = useState<PreventiveAction | null>(null);
  const [openDetailsModal, setOpenDetailsModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<string | null>(null);
  const [selectedCapa, setSelectedCapa] = useState<CAPA | null>(null);

  const handleAddPreventiveAction = (newAction: PreventiveAction) => {
    setPreventiveActions([...preventiveActions, newAction]);
  };

  const handleEditPreventiveAction = (editedAction: PreventiveAction) => {
    const updatedActions = preventiveActions.map((action) =>
      action.id === editedAction.id ? editedAction : action
    );
    setPreventiveActions(updatedActions);
    setOpenEditDialog(false);
  };

  // Filter actions based on search query and status filter
  const filteredActions = preventiveActions.filter((action) => {
    const matchesSearch =
      searchQuery === '' ||
      action.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      action.capaId.toLowerCase().includes(searchQuery.toLowerCase()) ||
      action.responsiblePerson.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus =
      filterStatus === null ||
      action.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  // Get CAPA title by ID
  const getCapaTitle = (capaId: string) => {
    const capa = capas.find(c => c.id === capaId);
    return capa ? capa.title : capaId;
  };

  // Render status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'planned':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-300">Planned</Badge>;
      case 'in_progress':
        return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-300">In Progress</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-300">Completed</Badge>;
      case 'verified':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-300">Verified</Badge>;
      case 'delayed':
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-300">Delayed</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle>Preventive Actions</CardTitle>
            <CardDescription>
              Define and track preventive actions to avoid recurrence
            </CardDescription>
          </div>
          <Button
            onClick={() => setOpenAddDialog(true)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Action
          </Button>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search actions..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                className={filterStatus === null ? 'bg-primary text-primary-foreground' : ''}
                onClick={() => setFilterStatus(null)}
              >
                All
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={filterStatus === 'planned' ? 'bg-primary text-primary-foreground' : ''}
                onClick={() => setFilterStatus('planned')}
              >
                Planned
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={filterStatus === 'in_progress' ? 'bg-primary text-primary-foreground' : ''}
                onClick={() => setFilterStatus('in_progress')}
              >
                In Progress
              </Button>
              <Button
                variant="outline"
                size="sm"
                className={filterStatus === 'completed' ? 'bg-primary text-primary-foreground' : ''}
                onClick={() => setFilterStatus('completed')}
              >
                Completed
              </Button>
            </div>
            <Button variant="outline" size="icon">
              <FileDown className="h-4 w-4" />
            </Button>
          </div>

          <Table>
            <TableCaption>A list of preventive actions.</TableCaption>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[80px]">ID</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Related CAPA</TableHead>
                <TableHead>Target Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Responsible Person</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredActions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                    No preventive actions found. Click "Add Action" to create one.
                  </TableCell>
                </TableRow>
              ) : (
                filteredActions.map((action) => (
                  <TableRow key={action.id}>
                    <TableCell className="font-medium">{action.id}</TableCell>
                    <TableCell>
                      <div className="max-w-[300px] truncate" title={action.description}>
                        {action.description}
                      </div>
                    </TableCell>
                    <TableCell>{getCapaTitle(action.capaId)}</TableCell>
                    <TableCell>{action.targetDate?.toLocaleDateString()}</TableCell>
                    <TableCell>{getStatusBadge(action.status)}</TableCell>
                    <TableCell>{action.responsiblePerson}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="ghost"
                          size="icon"
                          title="View details"
                          onClick={() => {
                            setSelectedAction(action);
                            // Find the related CAPA if needed
                            const relatedCapa = capas.find(c => c.id === action.capaId);
                            setSelectedCapa(relatedCapa || null);
                            setOpenDetailsModal(true);
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          title="Edit action"
                          onClick={() => {
                            setSelectedAction(action);
                            setOpenEditDialog(true);
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <AddPreventiveActionDialog
        open={openAddDialog}
        onClose={() => setOpenAddDialog(false)}
        capas={capas}
        onAdd={handleAddPreventiveAction}
      />

      <EditPreventiveActionDialog
        open={openEditDialog}
        onClose={() => setOpenEditDialog(false)}
        capas={capas}
        action={selectedAction}
        onEdit={handleEditPreventiveAction}
      />
      <PreventiveActionDetailsModal
        action={selectedAction}
        open={openDetailsModal}
        onOpenChange={setOpenDetailsModal}
        onEdit={(action) => {
          setSelectedAction(action);
          setOpenDetailsModal(false);
          setOpenEditDialog(true);
        }}
        capa={selectedCapa}
        getCapaTitle={getCapaTitle}
      />
    </div>
  );
}
