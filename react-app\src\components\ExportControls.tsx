import React from 'react';
import toast from 'react-hot-toast';
import { useDocuments } from '../context/DocumentContext';
import './ExportControls.css';

const ExportControls: React.FC = () => {
  const { documents, resetToSampleData, clearAllDocuments, setLoading } = useDocuments();

  const exportToJson = () => {
    try {
      const dataStr = JSON.stringify(documents, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);
      
      const exportName = 'document_control_export.json';
      
      const link = document.createElement('a');
      link.setAttribute('href', dataUri);
      link.setAttribute('download', exportName);
      link.click();
      
      toast.success('Documents exported as JSON successfully');
    } catch (error) {
      toast.error('Error exporting documents');
    }
  };

  const exportToCsv = () => {
    try {
      const headers = ['Type', 'Designation', 'Reference ID', 'Format', 'Effective Date', 'Version', 'Status', 'File Name'];
      const rows = documents.map(doc => [
        doc.type,
        doc.designation,
        doc.referenceId,
        doc.format,
        formatDate(doc.effectiveDate),
        doc.version,
        doc.status,
        doc.fileName || ''
      ]);
      
      let csvContent = headers.join(',') + '\n';
      rows.forEach(row => {
        csvContent += row.map(field => `"${field}"`).join(',') + '\n';
      });
      
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', 'document_control_export.csv');
      link.click();
      
      URL.revokeObjectURL(url);
      toast.success('Documents exported as CSV successfully');
    } catch (error) {
      toast.error('Error exporting documents');
    }
  };

  const handleResetSampleData = async () => {
    const confirmMessage = 'This will replace all current documents with sample data. Are you sure you want to continue?';

    if (window.confirm(confirmMessage)) {
      try {
        setLoading(true);
        resetToSampleData();
        toast.success('Sample data has been reset successfully!');
      } catch (error) {
        toast.error('Error resetting sample data. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleClearAllDocuments = async () => {
    const confirmMessage = 'This will permanently delete ALL documents. This action cannot be undone.\n\nAre you sure you want to continue?';

    if (window.confirm(confirmMessage)) {
      const doubleConfirm = window.prompt('Type "DELETE ALL" to confirm this action:');

      if (doubleConfirm === 'DELETE ALL') {
        try {
          setLoading(true);
          clearAllDocuments();
          toast.success('All documents have been deleted successfully.');
        } catch (error) {
          toast.error('Error clearing data. Please try again.');
        } finally {
          setLoading(false);
        }
      } else if (doubleConfirm !== null) {
        toast.error('Action cancelled. Please type "DELETE ALL" exactly to confirm.');
      }
    }
  };

  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB');
  };

  return (
    <section className="section export-section">
      <div className="export-options">
        <div className="export-group">
          <button 
            onClick={exportToJson}
            className="btn btn-outline"
            type="button"
          >
            <i className="fas fa-code"></i> Export as JSON
          </button>
          <button 
            onClick={exportToCsv}
            className="btn btn-outline"
            type="button"
          >
            <i className="fas fa-file-csv"></i> Export as CSV
          </button>
        </div>
        <div className="data-management-group">
          <button 
            onClick={handleResetSampleData}
            className="btn btn-outline" 
            title="Reset to sample data"
            type="button"
          >
            <i className="fas fa-refresh"></i> Reset Sample Data
          </button>
          <button 
            onClick={handleClearAllDocuments}
            className="btn btn-outline" 
            title="Clear all documents"
            type="button"
          >
            <i className="fas fa-trash-alt"></i> Clear All Data
          </button>
        </div>
      </div>
    </section>
  );
};

export default ExportControls;
