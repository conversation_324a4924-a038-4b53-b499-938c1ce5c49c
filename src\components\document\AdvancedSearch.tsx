import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { CalendarIcon, Search, X, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { DOCUMENT_STATUSES, DOCUMENT_ACCESS_LEVELS } from '@/models/document';

export interface AdvancedFilters {
  searchTerm: string;
  departments: string[];
  accessLevels: string[];
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
  statuses: string[];
  authors: string[];
}

interface AdvancedSearchProps {
  filters: AdvancedFilters;
  onFiltersChange: (filters: AdvancedFilters) => void;
  availableDepartments: string[];
  availableAuthors: <AUTHORS>
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  filters,
  onFiltersChange,
  availableDepartments,
  availableAuthors,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<AdvancedFilters>(filters);

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    setIsOpen(false);
  };

  const handleClearFilters = () => {
    const clearedFilters: AdvancedFilters = {
      searchTerm: '',
      departments: [],
      accessLevels: [],
      dateRange: { from: null, to: null },
      statuses: [],
      authors: [],
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
    setIsOpen(false);
  };

  const handleArrayFilterChange = (
    key: keyof Pick<AdvancedFilters, 'departments' | 'accessLevels' | 'statuses' | 'authors'>,
    value: string,
    checked: boolean
  ) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: checked
        ? [...prev[key], value]
        : prev[key].filter(item => item !== value)
    }));
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchTerm) count++;
    if (filters.departments.length > 0) count++;
    if (filters.accessLevels.length > 0) count++;
    if (filters.dateRange.from || filters.dateRange.to) count++;
    if (filters.statuses.length > 0) count++;
    if (filters.authors.length > 0) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="relative">
          <Filter className="h-4 w-4 mr-2" />
          Advanced Search
          {activeFiltersCount > 0 && (
            <Badge variant="secondary" className="ml-2 h-5 w-5 rounded-full p-0 text-xs">
              {activeFiltersCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Advanced Search & Filters</DialogTitle>
          <DialogDescription>
            Use multiple criteria to find specific documents in your collection.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Search Term */}
          <div className="space-y-2">
            <Label htmlFor="search-term">Search Term</Label>
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="search-term"
                placeholder="Search in title, reference, or content..."
                value={localFilters.searchTerm}
                onChange={(e) => setLocalFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                className="pl-10"
              />
            </div>
          </div>

          {/* Date Range */}
          <div className="space-y-2">
            <Label>Issue Date Range</Label>
            <div className="flex gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !localFilters.dateRange.from && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {localFilters.dateRange.from ? (
                      format(localFilters.dateRange.from, "PPP")
                    ) : (
                      <span>From date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={localFilters.dateRange.from || undefined}
                    onSelect={(date) => setLocalFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, from: date || null }
                    }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !localFilters.dateRange.to && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {localFilters.dateRange.to ? (
                      format(localFilters.dateRange.to, "PPP")
                    ) : (
                      <span>To date</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={localFilters.dateRange.to || undefined}
                    onSelect={(date) => setLocalFilters(prev => ({
                      ...prev,
                      dateRange: { ...prev.dateRange, to: date || null }
                    }))}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Departments */}
            <div className="space-y-2">
              <Label>Departments</Label>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto space-y-2">
                {availableDepartments.map((dept) => (
                  <div key={dept} className="flex items-center space-x-2">
                    <Checkbox
                      id={`dept-${dept}`}
                      checked={localFilters.departments.includes(dept)}
                      onCheckedChange={(checked) =>
                        handleArrayFilterChange('departments', dept, checked as boolean)
                      }
                    />
                    <Label htmlFor={`dept-${dept}`} className="text-sm font-normal">
                      {dept}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Access Levels */}
            <div className="space-y-2">
              <Label>Access Levels</Label>
              <div className="border rounded-md p-3 space-y-2">
                {DOCUMENT_ACCESS_LEVELS.map((level) => (
                  <div key={level} className="flex items-center space-x-2">
                    <Checkbox
                      id={`access-${level}`}
                      checked={localFilters.accessLevels.includes(level)}
                      onCheckedChange={(checked) =>
                        handleArrayFilterChange('accessLevels', level, checked as boolean)
                      }
                    />
                    <Label htmlFor={`access-${level}`} className="text-sm font-normal">
                      {level}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Statuses */}
            <div className="space-y-2">
              <Label>Document Status</Label>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto space-y-2">
                {DOCUMENT_STATUSES.map((status) => (
                  <div key={status} className="flex items-center space-x-2">
                    <Checkbox
                      id={`status-${status}`}
                      checked={localFilters.statuses.includes(status)}
                      onCheckedChange={(checked) =>
                        handleArrayFilterChange('statuses', status, checked as boolean)
                      }
                    />
                    <Label htmlFor={`status-${status}`} className="text-sm font-normal">
                      {status}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Authors */}
            <div className="space-y-2">
              <Label>Authors</Label>
              <div className="border rounded-md p-3 max-h-40 overflow-y-auto space-y-2">
                {availableAuthors.map((author) => (
                  <div key={author} className="flex items-center space-x-2">
                    <Checkbox
                      id={`author-${author}`}
                      checked={localFilters.authors.includes(author)}
                      onCheckedChange={(checked) =>
                        handleArrayFilterChange('authors', author, checked as boolean)
                      }
                    />
                    <Label htmlFor={`author-${author}`} className="text-sm font-normal">
                      {author}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="flex gap-2">
          <Button variant="outline" onClick={handleClearFilters}>
            <X className="h-4 w-4 mr-2" />
            Clear All
          </Button>
          <Button onClick={handleApplyFilters}>
            <Search className="h-4 w-4 mr-2" />
            Apply Filters
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AdvancedSearch;
