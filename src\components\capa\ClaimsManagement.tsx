import React, { useState, useEffect, useMemo } from 'react';
import useClaimForm from '@/hooks/useClaimForm';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DialogWithMaxHeightCSS } from '@/components/ui/dialog-with-max-height-css';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { CalendarIcon, FileText, Plus, Search, Trash2, PenLine, AlertCircle, CheckCircle, Clock, Camera, Image } from 'lucide-react';
import { Claim, CAPA } from '@/models/capa';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PhotoUpload, PhotoFile } from '@/components/ui/photo-upload';
import { PhotoGallery, Photo } from '@/components/ui/photo-gallery';
import { CAPALinkDisplay } from '@/components/ui/capa-link-display';
import * as photoStorageService from '@/services/photoStorageService';

const CLAIMS_ERROR_MESSAGE = 'Failed to save claim. Please try again.';
const CLAIMS_UPDATE_ERROR_MESSAGE = 'Failed to update claim. Please try again.';
const REQUIRED_FIELD_ERROR_MESSAGE = 'Please fill in all required fields';
const CAPA_CREATION_SUCCESS_MESSAGE = 'CAPA created and linked to claim successfully';
const CAPA_LINK_SUCCESS_MESSAGE = 'CAPA linked to claim successfully';
const CAPA_UNLINK_SUCCESS_MESSAGE = 'CAPA unlinked from claim';

interface ClaimsManagementProps {
  claims: Claim[];
  setClaims: React.Dispatch<React.SetStateAction<Claim[]>>;
  products: { id: string; name: string; description: string }[];
  capas: CAPA[];
  setCapas: React.Dispatch<React.SetStateAction<CAPA[]>>;
  onNavigateToCAPA?: (capaId: string) => void;
}

export function ClaimsManagement({
  claims,
  setClaims,
  products,
  capas,
  setCapas,
  onNavigateToCAPA
}: ClaimsManagementProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedClaim, setSelectedClaim] = useState<Claim | null>(null);
  const [isCreateCAPADialogOpen, setIsCreateCAPADialogOpen] = useState(false);

  // Integrate useClaimForm hook
  const { claim: formValues, handleChange: handleFormChange, resetForm, loadClaim, updateField } = useClaimForm();

  // Photo upload state
  const [uploadedPhotos, setUploadedPhotos] = useState<PhotoFile[]>([]);
  const [isUploadingPhotos, setIsUploadingPhotos] = useState(false);


  // CAPA form state
  const [capaFormValues, setCapaFormValues] = useState<Partial<CAPA>>({
    title: '',
    description: '',
    source: 'customer_claim',
    category: 'product_quality',
    priority: 'medium',
    status: 'open',
    dateIdentified: new Date(),
    targetCompletionDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
    responsiblePerson: '',
    productId: '',
    claimId: ''
  });

  // Filter claims based on search query
  const filteredClaims = useMemo(() => {
    return claims.filter(claim => {
      const product = products.find(p => p.id === claim.productId);
      return (
        claim.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        claim.batchNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        claim.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (product && product.name.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    });
  }, [claims, searchQuery, products]);


  const handleCAPAFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setCapaFormValues(prev => ({ ...prev, [name]: value }));
  };


  const handleCAPASelectChange = (name: string, value: string) => {
    setCapaFormValues(prev => ({ ...prev, [name]: value }));
  };


  const handleCAPADateChange = (name: string, date: Date | undefined) => {
    setCapaFormValues(prev => ({ ...prev, [name]: date }));
  };

  const openAddDialog = () => {
    resetForm();
    setUploadedPhotos([]);
    setIsUploadingPhotos(false);
    setIsAddDialogOpen(true);
  };

  const openEditDialog = async (claim: Claim) => {
    setSelectedClaim(claim);
    setIsUploadingPhotos(true);

    // Load claim data into form
    loadClaim(claim);

    try {
      // Load existing photos for editing from storage service
      const storedPhotos = photoStorageService.getPhotosForClaim(claim.id);

      // Convert StoredPhoto to PhotoFile for PhotoUpload component
      const photosForUpload: PhotoFile[] = storedPhotos.map(sp => ({
        id: sp.id,
        file: new File([''], sp.name, { type: 'image/jpeg' }), // Placeholder file
        url: sp.url,
        name: sp.name,
        size: sp.size,
        uploading: false,
        uploadProgress: 100
      }));

      setUploadedPhotos(photosForUpload);

      toast({
        title: 'Claim loaded',
        description: `Loaded claim with ${photosForUpload.length} photo(s)`,
      });
    } catch (error) {
      console.error('Failed to load photos:', error);
      toast({
        title: 'Warning',
        description: 'Failed to load existing photos, but you can still edit the claim.',
        variant: 'default'
      });
      setUploadedPhotos([]);
    } finally {
      setIsUploadingPhotos(false);
    }

    setIsEditDialogOpen(true);
  };

  const openCreateCAPADialog = (claim: Claim) => {
    setSelectedClaim(claim);
    const product = products.find(p => p.id === claim.productId);

    setCapaFormValues({
      title: `CAPA for ${product?.name || 'Product'} - ${claim.description.substring(0, 30)}...`,
      description: `Investigation and corrective actions for customer claim: ${claim.description}`,
      source: 'customer_claim',
      category: 'product_quality',
      priority: claim.severity === 'critical' ? 'critical' : claim.severity === 'major' ? 'high' : 'medium',
      status: 'open',
      dateIdentified: claim.dateReceived,
      targetCompletionDate: new Date(new Date().setMonth(claim.dateReceived.getMonth() + 1)),
      responsiblePerson: 'Quality Manager',
      productId: claim.productId,
      claimId: claim.id
    });

    setIsCreateCAPADialogOpen(true);
  };

  const handleAddClaim = async () => {
    if (!formValues.customerName || !formValues.productId || !formValues.description) {
      toast({
        title: 'Error',
        description: REQUIRED_FIELD_ERROR_MESSAGE,
        variant: 'destructive'
      });
      return;
    }

    setIsUploadingPhotos(true);

    try {
      // Upload photos using photoStorageService
      const photosToUpload = uploadedPhotos.filter(photo => photo.uploading || photo.uploadProgress !== 100);
      let uploadedStoredPhotos = [];
      try {
        uploadedStoredPhotos = await photoStorageService.uploadPhotos(photosToUpload, undefined, (photoId, progress) => {
          setUploadedPhotos(prevPhotos =>
            prevPhotos.map(p =>
              p.id === photoId ? { ...p, uploadProgress: progress, uploading: progress < 100 } : p
            )
          );
        });
      } catch (error: any) {
        toast({
          title: 'Error',
          description: 'Failed to upload photos. Please try again.',
          variant: 'destructive'
        });
        return;
      }

      // Combine uploaded photos with already stored photos
      const allPhotos = [
        ...uploadedStoredPhotos,
        ...uploadedPhotos.filter(photo => !photosToUpload.some(p => p.id === photo.id))
      ];

      // Extract URLs for attachments
      const attachmentUrls = allPhotos.map(photo => photo.url);

      const newClaim: Claim = {
        id: `claim${claims.length + 1}`,
        customerName: formValues.customerName!,
        contactInfo: formValues.contactInfo || '',
        productId: formValues.productId!,
        batchNumber: formValues.batchNumber || '',
        dateReceived: formValues.dateReceived || new Date(),
        description: formValues.description!,
        severity: formValues.severity as 'critical' | 'major' | 'minor',
        status: formValues.status as 'new' | 'under_investigation' | 'resolved' | 'closed' | 'rejected',
        responseDetails: formValues.responseDetails,
        responseDate: formValues.responseDate,
        attachments: attachmentUrls,
        createdBy: 'user1',
        createdAt: new Date(),
        updatedBy: 'user1',
        updatedAt: new Date()
      };

      setClaims([...claims, newClaim]);
      setIsAddDialogOpen(false);

      toast({
        title: 'Success',
        description: `Claim added successfully${allPhotos.length > 0 ? ` with ${allPhotos.length} photo(s)` : ''}`
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: CLAIMS_ERROR_MESSAGE,
        variant: 'destructive'
      });
    } finally {
      setIsUploadingPhotos(false);
    }
  };

  const handleEditClaim = async () => {
    if (!selectedClaim) return;


        if (!formValues.customerName || !formValues.productId || !formValues.description) {
          toast({
            title: 'Error',
            description: REQUIRED_FIELD_ERROR_MESSAGE,
            variant: 'destructive'
          });
          return;
        }
    setIsUploadingPhotos(true);

    try {
      // Upload photos using photoStorageService
      const photosToUpload = uploadedPhotos.filter(photo => photo.uploading || photo.uploadProgress !== 100);
      let uploadedStoredPhotos = [];
      try {
        uploadedStoredPhotos = await photoStorageService.uploadPhotos(photosToUpload, selectedClaim.id, (photoId, progress) => {
          setUploadedPhotos(prevPhotos =>
            prevPhotos.map(p =>
              p.id === photoId ? { ...p, uploadProgress: progress, uploading: progress < 100 } : p
            )
          );
        });
      } catch (error: any) {
        toast({
          title: 'Error',
          description: 'Failed to upload photos. Please try again.',
          variant: 'destructive'
        });
        return;
      }

      // Combine uploaded photos with already stored photos
      const allPhotos = [
        ...uploadedStoredPhotos,
        ...uploadedPhotos.filter(photo => !photosToUpload.some(p => p.id === photo.id))
      ];

      // Extract URLs for attachments
      const attachmentUrls = allPhotos.map(photo => photo.url);

      const updatedClaims = claims.map(claim =>
        claim.id === selectedClaim.id
          ? {
              ...claim,
              customerName: formValues.customerName!,
              contactInfo: formValues.contactInfo || '',
              productId: formValues.productId!,
              batchNumber: formValues.batchNumber || '',
              dateReceived: formValues.dateReceived || new Date(),
              description: formValues.description!,
              severity: formValues.severity as 'critical' | 'major' | 'minor',
              status: formValues.status as 'new' | 'under_investigation' | 'resolved' | 'closed' | 'rejected',
              responseDetails: formValues.responseDetails,
              responseDate: formValues.responseDate,
              attachments: attachmentUrls,
              updatedBy: 'user1',
              updatedAt: new Date()
            }
          : claim
      );

      setClaims(updatedClaims);
      setIsEditDialogOpen(false);

      toast({
        title: 'Success',
        description: `Claim updated successfully${allPhotos.length > 0 ? ` with ${allPhotos.length} photo(s)` : ''}`
      });
    } catch (error: any) {
      toast({
        title: 'Error',
        description: CLAIMS_UPDATE_ERROR_MESSAGE,
        variant: 'destructive'
      });
    } finally {
      setIsUploadingPhotos(false);
    }
  };

  const handleCreateCAPA = () => {
    if (!selectedClaim) return;

    if (!capaFormValues.title || !capaFormValues.responsiblePerson) {
      toast({
        title: 'Error',
        description: REQUIRED_FIELD_ERROR_MESSAGE,
        variant: 'destructive'
      });
      return;
    }

    const newCAPA: CAPA = {
      id: `capa${capas.length + 1}`,
      title: capaFormValues.title!,
      description: capaFormValues.description || '',
      source: capaFormValues.source as any || 'customer_claim',
      category: capaFormValues.category as any || 'product_quality',
      priority: capaFormValues.priority as any || 'medium',
      status: capaFormValues.status as any || 'open',
      dateIdentified: capaFormValues.dateIdentified || new Date(),
      targetCompletionDate: capaFormValues.targetCompletionDate || new Date(new Date().setMonth(new Date().getMonth() + 1)),
      responsiblePerson: capaFormValues.responsiblePerson!,
      productId: capaFormValues.productId || selectedClaim.productId,
      claimId: selectedClaim.id,
      rootCauseAnalysisId: undefined,
      correctiveActionIds: [],
      preventiveActionIds: [],
      verificationIds: [],
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };

    // Update CAPA list
    setCapas([...capas, newCAPA]);

    // Update claim with CAPA ID
    const updatedClaims = claims.map(claim =>
      claim.id === selectedClaim.id
        ? {
            ...claim,
            capaId: newCAPA.id,
            status: 'under_investigation' as const,
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : claim
    );

    setClaims(updatedClaims);
    setIsCreateCAPADialogOpen(false);


        toast({
          title: 'Success',
          description: CAPA_CREATION_SUCCESS_MESSAGE
        });
      };
  // CAPA linking functions
  const handleLinkCAPA = (claimId: string, capaId: string) => {
    const updatedClaims = claims.map(claim =>
      claim.id === claimId
        ? {
            ...claim,
            capaId,
            status: 'under_investigation' as const,
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : claim
    );

    setClaims(updatedClaims);


        toast({
          title: 'Success',
          description: CAPA_LINK_SUCCESS_MESSAGE
        });
      };
  const handleUnlinkCAPA = (claimId: string) => {
    const updatedClaims = claims.map(claim =>
      claim.id === claimId
        ? {
            ...claim,
            capaId: undefined,
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : claim
    );

    setClaims(updatedClaims);


        toast({
          title: 'Success',
          description: CAPA_UNLINK_SUCCESS_MESSAGE
        });
      };
  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      case 'major':
        return <Badge variant="default" className="bg-orange-500">Major</Badge>;
      case 'minor':
        return <Badge variant="outline">Minor</Badge>;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'new':
        return <Badge variant="default" className="bg-blue-500">New</Badge>;
      case 'under_investigation':
        return <Badge variant="default" className="bg-yellow-500">Under Investigation</Badge>;
      case 'resolved':
        return <Badge variant="default" className="bg-green-500">Resolved</Badge>;
      case 'closed':
        return <Badge variant="default" className="bg-green-700">Closed</Badge>;
      case 'rejected':
        return <Badge variant="outline" className="border-red-500 text-red-500">Rejected</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Customer Claims</CardTitle>
              <CardDescription>
                Manage customer claims and complaints
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Claim
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search claims..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          {filteredClaims.length === 0 ? (
            <div className="text-center py-6 text-muted-foreground">
              No claims found. Add a new claim to get started.
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Customer</TableHead>
                    <TableHead>Product</TableHead>
                    <TableHead>Date Received</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Photos</TableHead>
                    <TableHead>CAPA</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredClaims.map((claim) => {
                    const product = products.find(p => p.id === claim.productId);
                    const linkedCapa = capas.find(c => c.id === claim.capaId);

                    // Convert claim attachments to photo format for display
                    const claimPhotos = (claim.attachments || []).map((url, index) => ({
                      id: `claim${claim.id}_photo_${index}`,
                      url,
                      name: `Photo_${index + 1}`,
                      size: 0,
                      uploadDate: claim.updatedAt
                    }));

                    return (
                      <TableRow key={claim.id}>
                        <TableCell className="font-medium">{claim.customerName}</TableCell>
                        <TableCell>{product?.name || 'Unknown Product'}</TableCell>
                        <TableCell>{format(claim.dateReceived, 'MMM d, yyyy')}</TableCell>
                        <TableCell>{getSeverityBadge(claim.severity)}</TableCell>
                        <TableCell>{getStatusBadge(claim.status)}</TableCell>
                        <TableCell>
                          {claimPhotos.length > 0 ? (
                            <PhotoGallery
                              photos={claimPhotos}
                              size="sm"
                              maxThumbnails={3}
                            />
                          ) : (
                            <div className="flex items-center text-muted-foreground">
                              <Image className="h-4 w-4 mr-1" />
                              <span className="text-xs">No photos</span>
                            </div>
                          )}
                        </TableCell>
                        <TableCell>
                          <CAPALinkDisplay
                            claim={claim}
                            linkedCapa={linkedCapa}
                            availableCapas={capas}
                            onNavigateToCAPA={onNavigateToCAPA}
                            onCreateCAPA={openCreateCAPADialog}
                            onLinkCAPA={handleLinkCAPA}
                            onUnlinkCAPA={handleUnlinkCAPA}
                            size="sm"
                          />
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button variant="ghost" size="icon" onClick={() => openEditDialog(claim)}>
                              <PenLine className="h-4 w-4" />
                            </Button>
                            {!claim.capaId && (
                              <Button variant="ghost" size="icon" onClick={() => openCreateCAPADialog(claim)}>
                                <FileText className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Claim Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogWithMaxHeightCSS className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Customer Claim</DialogTitle>
            <DialogDescription>
              Record a new customer claim or complaint.
            </DialogDescription>
          </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="customerName">Customer Name *</Label>
                  <Input
                    id="customerName"
                    name="customerName"
                    value={formValues?.customerName || ''}
                    onChange={handleFormChange}
                  />
                </div>

              <div className="space-y-2">
                <Label htmlFor="contactInfo">Contact Information</Label>
                <Input
                  id="contactInfo"
                  name="contactInfo"
                  value={formValues?.contactInfo || ''}
                  onChange={handleFormChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="productId">Product *</Label>
                <Select
                  value={formValues?.productId || ''}
                  onValueChange={(value) => handleFormChange({ target: { name: 'productId', value } } as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a product" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="batchNumber">Batch/Lot Number</Label>
                <Input
                  id="batchNumber"
                  name="batchNumber"
                  value={formValues?.batchNumber || ''}
                  onChange={handleFormChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Date Received</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues?.dateReceived ? (
                        format(formValues?.dateReceived, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues?.dateReceived}
                      onSelect={(date) => handleFormChange({ target: { name: 'dateReceived', value: date } } as any)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="severity">Severity</Label>
                <Select
                  value={formValues?.severity || 'minor'}
                  onValueChange={(value) => handleFormChange({ target: { name: 'severity', value } } as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="major">Major</SelectItem>
                    <SelectItem value="minor">Minor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues?.description || ''}
                onChange={handleFormChange}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={formValues?.status || 'new'}
                onValueChange={(value) => handleFormChange({ target: { name: 'status', value } } as any)}
                >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="under_investigation">Under Investigation</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="responseDetails">Response Details</Label>
              <Textarea
                id="responseDetails"
                name="responseDetails"
                value={formValues?.responseDetails || ''}
                onChange={handleFormChange}
                rows={2}
              />
            </div>

            {/* Photo Upload Section */}
            <PhotoUpload
              photos={uploadedPhotos}
              onPhotosChange={setUploadedPhotos}
              maxFiles={10}
              maxFileSize={5}
              disabled={isUploadingPhotos}
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddDialogOpen(false)} disabled={isUploadingPhotos}>
              Cancel
            </Button>
            <Button onClick={handleAddClaim} disabled={isUploadingPhotos}>
              {isUploadingPhotos ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Saving...
                </>
              ) : (
                'Add Claim'
              )}
            </Button>
          </DialogFooter>
        </DialogWithMaxHeightCSS>
      </Dialog>

      {/* Edit Claim Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogWithMaxHeightCSS className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Customer Claim</DialogTitle>
            <DialogDescription>
              Update the customer claim details.
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-customerName">Customer Name *</Label>
                <Input
                  id="edit-customerName"
                  name="customerName"
                  value={formValues?.customerName || ''}
                  onChange={handleFormChange}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-contactInfo">Contact Information</Label>
                <Input
                  id="edit-contactInfo"
                  name="contactInfo"
                  value={formValues?.contactInfo || ''}
                  onChange={handleFormChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-productId">Product *</Label>
                <Select
                  value={formValues?.productId || ''}
                  onValueChange={(value) => handleFormChange({ target: { name: 'productId', value } } as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a product" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-batchNumber">Batch/Lot Number</Label>
                <Input
                  id="edit-batchNumber"
                  name="batchNumber"
                  value={formValues?.batchNumber || ''}
                  onChange={handleFormChange}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>Date Received</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues?.dateReceived ? (
                        format(formValues?.dateReceived, 'PPP')
                      ) : (
                        <span>Pick a date</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues?.dateReceived}
                      onSelect={(date) => handleFormChange({ target: { name: 'dateReceived', value: date } } as any)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-severity">Severity</Label>
                <Select
                  value={formValues?.severity || 'minor'}
                  onValueChange={(value) => handleFormChange({ target: { name: 'severity', value } } as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="critical">Critical</SelectItem>
                    <SelectItem value="major">Major</SelectItem>
                    <SelectItem value="minor">Minor</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-description">Description *</Label>
              <Textarea
                id="edit-description"
                name="description"
                value={formValues?.description || ''}
                onChange={handleFormChange}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-status">Status</Label>
              <Select
                value={formValues?.status || 'new'}
                onValueChange={(value) => handleFormChange({ target: { name: 'status', value } } as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="under_investigation">Under Investigation</SelectItem>
                  <SelectItem value="resolved">Resolved</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit-responseDetails">Response Details</Label>
              <Textarea
                id="edit-responseDetails"
                name="responseDetails"
                value={formValues?.responseDetails || ''}
                onChange={handleFormChange}
                rows={2}
              />
            </div>

            {/* Photo Upload Section */}
            <PhotoUpload
              photos={uploadedPhotos}
              onPhotosChange={setUploadedPhotos}
              maxFiles={10}
              maxFileSize={5}
              disabled={isUploadingPhotos}
            />
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)} disabled={isUploadingPhotos}>
              Cancel
            </Button>
            <Button onClick={handleEditClaim} disabled={isUploadingPhotos}>
              {isUploadingPhotos ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Claim'
              )}
            </Button>
          </DialogFooter>
        </DialogWithMaxHeightCSS>
      </Dialog>
    </div>
  );
}
