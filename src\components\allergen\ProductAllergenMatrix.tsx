import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from '@/hooks/use-toast';
import { CheckCircle, Search, PenLine, AlertTriangle, CheckCircle2, HelpCircle, Plus } from 'lucide-react';
import { Allergen, ProductAllergen, COMMON_ALLERGENS, MOCK_PRODUCT_ALLERGENS } from '@/models/allergen';

interface Product {
  id: string;
  name: string;
  description: string;
}

interface ProductAllergenMatrixProps {
  products: Product[];
  allergens: Allergen[];
  productAllergens?: ProductAllergen[];
  setProductAllergens?: React.Dispatch<React.SetStateAction<ProductAllergen[]>>;
  setProducts?: React.Dispatch<React.SetStateAction<Product[]>>;
}

export function ProductAllergenMatrix({
  products,
  allergens,
  productAllergens = MOCK_PRODUCT_ALLERGENS,
  setProductAllergens,
  setProducts
}: ProductAllergenMatrixProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isAddProductDialogOpen, setIsAddProductDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedAllergen, setSelectedAllergen] = useState<Allergen | null>(null);
  const [selectedProductAllergen, setSelectedProductAllergen] = useState<ProductAllergen | null>(null);

  // Form state for allergen status
  const [formValues, setFormValues] = useState<Partial<ProductAllergen>>({
    status: 'not_present',
    controlMeasures: '',
    verificationProcedures: ''
  });

  // Form state for new product
  const [newProductForm, setNewProductForm] = useState<Partial<Product>>({
    name: '',
    description: ''
  });

  // Filter products based on search query
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get allergen status for a product
  const getAllergenStatus = (productId: string, allergenId: string): ProductAllergen | undefined => {
    return productAllergens.find(pa => pa.productId === productId && pa.allergenId === allergenId);
  };

  // Open edit dialog
  const openEditDialog = (product: Product, allergen: Allergen) => {
    setSelectedProduct(product);
    setSelectedAllergen(allergen);

    const existingProductAllergen = getAllergenStatus(product.id, allergen.id);
    setSelectedProductAllergen(existingProductAllergen || null);

    setFormValues({
      status: existingProductAllergen?.status || 'not_present',
      controlMeasures: existingProductAllergen?.controlMeasures || '',
      verificationProcedures: existingProductAllergen?.verificationProcedures || ''
    });

    setIsEditDialogOpen(true);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  // Handle new product form input changes
  const handleNewProductInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewProductForm(prev => ({ ...prev, [name]: value }));
  };

  // Open add product dialog
  const openAddProductDialog = () => {
    setNewProductForm({
      name: '',
      description: ''
    });
    setIsAddProductDialogOpen(true);
  };

  // Handle add product form submission
  const handleAddProduct = (e: React.FormEvent) => {
    e.preventDefault();

    if (!newProductForm.name?.trim()) {
      toast({
        title: 'Error',
        description: 'Product name is required',
        variant: 'destructive'
      });
      return;
    }

    if (setProducts) {
      const newProduct: Product = {
        id: `product${products.length + 1}`,
        name: newProductForm.name,
        description: newProductForm.description || ''
      };

      setProducts([...products, newProduct]);
      setIsAddProductDialogOpen(false);

      toast({
        title: 'Success',
        description: 'Product added successfully'
      });
    }
  };

  // Handle form submission for editing a product allergen
  const handleEditProductAllergen = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedProduct || !selectedAllergen) return;

    if (!formValues.status) {
      toast({
        title: 'Error',
        description: 'Please select an allergen status',
        variant: 'destructive'
      });
      return;
    }

    if (setProductAllergens) {
      // If the product allergen already exists, update it
      if (selectedProductAllergen) {
        const updatedProductAllergens = productAllergens.map(pa =>
          pa.id === selectedProductAllergen.id
            ? {
                ...pa,
                status: formValues.status as 'present' | 'may_contain' | 'not_present',
                controlMeasures: formValues.controlMeasures || '',
                verificationProcedures: formValues.verificationProcedures || '',
                updatedBy: 'user1',
                updatedAt: new Date()
              }
            : pa
        );

        setProductAllergens(updatedProductAllergens);
      }
      // Otherwise, create a new product allergen
      else {
        const newProductAllergen: ProductAllergen = {
          id: `pa${productAllergens.length + 1}`,
          productId: selectedProduct.id,
          allergenId: selectedAllergen.id,
          status: formValues.status as 'present' | 'may_contain' | 'not_present',
          controlMeasures: formValues.controlMeasures || '',
          verificationProcedures: formValues.verificationProcedures || '',
          createdBy: 'user1',
          createdAt: new Date(),
          updatedBy: 'user1',
          updatedAt: new Date()
        };

        setProductAllergens([...productAllergens, newProductAllergen]);
      }
    }

    setIsEditDialogOpen(false);

    toast({
      title: 'Success',
      description: 'Product allergen updated successfully'
    });
  };

  // Render allergen status cell
  const renderAllergenStatusCell = (product: Product, allergen: Allergen) => {
    const productAllergen = getAllergenStatus(product.id, allergen.id);

    if (!productAllergen) {
      return (
        <TableCell
          className="text-center cursor-pointer hover:bg-muted/50"
          onClick={() => openEditDialog(product, allergen)}
        >
          <HelpCircle className="h-5 w-5 mx-auto text-gray-300" />
        </TableCell>
      );
    }

    switch (productAllergen.status) {
      case 'present':
        return (
          <TableCell
            className="text-center cursor-pointer hover:bg-muted/50 bg-red-50"
            onClick={() => openEditDialog(product, allergen)}
          >
            <div className="flex flex-col items-center">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <span className="text-xs font-medium text-red-500">Present</span>
            </div>
          </TableCell>
        );
      case 'may_contain':
        return (
          <TableCell
            className="text-center cursor-pointer hover:bg-muted/50 bg-amber-50"
            onClick={() => openEditDialog(product, allergen)}
          >
            <div className="flex flex-col items-center">
              <HelpCircle className="h-5 w-5 text-amber-500" />
              <span className="text-xs font-medium text-amber-500">May Contain</span>
            </div>
          </TableCell>
        );
      case 'not_present':
        return (
          <TableCell
            className="text-center cursor-pointer hover:bg-muted/50 bg-green-50"
            onClick={() => openEditDialog(product, allergen)}
          >
            <div className="flex flex-col items-center">
              <CheckCircle2 className="h-5 w-5 text-green-500" />
              <span className="text-xs font-medium text-green-500">Not Present</span>
            </div>
          </TableCell>
        );
      default:
        return (
          <TableCell
            className="text-center cursor-pointer hover:bg-muted/50"
            onClick={() => openEditDialog(product, allergen)}
          >
            <HelpCircle className="h-5 w-5 mx-auto text-gray-300" />
          </TableCell>
        );
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Product-Allergen Matrix</CardTitle>
              <CardDescription>
                Manage allergen status for each product
              </CardDescription>
            </div>
            {setProducts && (
              <Button onClick={openAddProductDialog}>
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-4">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search products..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>

          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">Product</TableHead>
                  {allergens.map((allergen) => (
                    <TableHead key={allergen.id} className="text-center">
                      <div className="flex flex-col items-center">
                        <span>{allergen.name}</span>
                        {allergen.riskLevel === 'high' && (
                          <Badge className="bg-red-500 mt-1">High Risk</Badge>
                        )}
                      </div>
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={allergens.length + 1} className="h-24 text-center">
                      No products found.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">
                        <div>
                          {product.name}
                          <p className="text-xs text-muted-foreground truncate max-w-[200px]">
                            {product.description}
                          </p>
                        </div>
                      </TableCell>
                      {allergens.map((allergen) => (
                        renderAllergenStatusCell(product, allergen)
                      ))}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          <div className="mt-4 flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded-full"></div>
              <span className="text-sm">Present</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-amber-500 rounded-full"></div>
              <span className="text-sm">May Contain</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span className="text-sm">Not Present</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-gray-300 rounded-full"></div>
              <span className="text-sm">Not Specified</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Edit Product Allergen Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Allergen Status</DialogTitle>
            <DialogDescription>
              {selectedProduct && selectedAllergen && (
                <span>
                  Update allergen status for {selectedProduct.name} - {selectedAllergen.name}
                </span>
              )}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleEditProductAllergen} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="status">Allergen Status *</Label>
              <Select
                value={formValues.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="present">Present</SelectItem>
                  <SelectItem value="may_contain">May Contain</SelectItem>
                  <SelectItem value="not_present">Not Present</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="controlMeasures">Control Measures</Label>
              <Textarea
                id="controlMeasures"
                name="controlMeasures"
                value={formValues.controlMeasures}
                onChange={handleInputChange}
                placeholder="e.g., Dedicated equipment, Cleaning procedures..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="verificationProcedures">Verification Procedures</Label>
              <Textarea
                id="verificationProcedures"
                name="verificationProcedures"
                value={formValues.verificationProcedures}
                onChange={handleInputChange}
                placeholder="e.g., Allergen testing, Label verification..."
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Status
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Add Product Dialog */}
      <Dialog open={isAddProductDialogOpen} onOpenChange={setIsAddProductDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Product</DialogTitle>
            <DialogDescription>
              Add a new product to the allergen matrix
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleAddProduct} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Product Name *</Label>
              <Input
                id="name"
                name="name"
                value={newProductForm.name || ''}
                onChange={handleNewProductInputChange}
                placeholder="e.g., Tomato Soup"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={newProductForm.description || ''}
                onChange={handleNewProductInputChange}
                placeholder="Brief description of the product"
                rows={3}
              />
            </div>

            <DialogFooter>
              <Button type="submit">Add Product</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
