// Allergen Management Types for FSSC 22000 v6.0 compliance

export type AllergenStatus = 'present' | 'may_contain' | 'not_present';

// Verification activity types
export type VerificationType = 'allergen_testing' | 'cleaning_validation' | 'label_verification' | 'supplier_audit' | 'internal_audit';

// Training record types
export type TrainingType = 'allergen_awareness' | 'cross_contact_prevention' | 'cleaning_procedures' | 'label_verification' | 'emergency_response';

export interface Allergen {
  id: string;
  name: string;
  description: string;
  commonNames: string[];
  riskLevel: 'high' | 'medium' | 'low';
}

export interface ProductAllergen {
  id: string;
  productId: string;
  allergenId: string;
  status: AllergenStatus;
  controlMeasures: string;
  verificationProcedures: string;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

export interface AllergenControlMeasure {
  id: string;
  name: string;
  description: string;
  type: 'prevention' | 'segregation' | 'cleaning' | 'labeling' | 'training';
  verificationMethod: string;
  frequency: string;
  responsiblePerson: string;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

export interface AllergenRiskAssessment {
  id: string;
  productId: string;
  assessmentDate: Date;
  nextReviewDate: Date;
  conductedBy: string;
  approvedBy: string;
  crossContactRisks: string;
  controlMeasureEffectiveness: string;
  correctiveActions: string;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

export interface VerificationActivity {
  id: string;
  type: VerificationType;
  date: Date;
  productId?: string;
  allergenId?: string;
  controlMeasureId?: string;
  result: 'pass' | 'fail' | 'pending';
  details: string;
  correctiveActions?: string;
  conductedBy: string;
  verifiedBy?: string;
  attachments?: string[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

export interface TrainingRecord {
  id: string;
  type: TrainingType;
  title: string;
  description: string;
  date: Date;
  duration: number; // in minutes
  trainer: string;
  attendees: string[];
  materials: string;
  assessmentResults?: string;
  nextTrainingDate?: Date;
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Common allergens list
export const COMMON_ALLERGENS: Allergen[] = [
  {
    id: 'a1',
    name: 'Milk',
    description: 'Milk and dairy products',
    commonNames: ['Casein', 'Whey', 'Lactose', 'Dairy', 'Butter', 'Cheese', 'Cream'],
    riskLevel: 'high'
  },
  {
    id: 'a2',
    name: 'Eggs',
    description: 'Eggs and egg products',
    commonNames: ['Albumin', 'Lysozyme', 'Ovalbumin', 'Egg white', 'Egg yolk', 'Mayonnaise'],
    riskLevel: 'high'
  },
  {
    id: 'a3',
    name: 'Peanuts',
    description: 'Peanuts and peanut products',
    commonNames: ['Groundnuts', 'Arachis oil', 'Beer nuts', 'Monkey nuts', 'Peanut butter'],
    riskLevel: 'high'
  },
  {
    id: 'a4',
    name: 'Tree Nuts',
    description: 'Various types of tree nuts',
    commonNames: ['Almonds', 'Hazelnuts', 'Walnuts', 'Cashews', 'Pecans', 'Pistachios', 'Brazil nuts'],
    riskLevel: 'high'
  },
  {
    id: 'a5',
    name: 'Fish',
    description: 'Fish and fish products',
    commonNames: ['Cod', 'Salmon', 'Tuna', 'Fish sauce', 'Fish gelatin', 'Surimi'],
    riskLevel: 'high'
  },
  {
    id: 'a6',
    name: 'Shellfish',
    description: 'Crustaceans and mollusks',
    commonNames: ['Shrimp', 'Crab', 'Lobster', 'Clams', 'Mussels', 'Oysters', 'Scallops'],
    riskLevel: 'high'
  },
  {
    id: 'a7',
    name: 'Soy',
    description: 'Soybeans and soy products',
    commonNames: ['Edamame', 'Miso', 'Tempeh', 'Tofu', 'Soy sauce', 'Soy lecithin'],
    riskLevel: 'high'
  },
  {
    id: 'a8',
    name: 'Wheat',
    description: 'Wheat and wheat products',
    commonNames: ['Flour', 'Bread', 'Pasta', 'Semolina', 'Couscous', 'Seitan', 'Bulgur'],
    riskLevel: 'high'
  },
  {
    id: 'a9',
    name: 'Gluten',
    description: 'Gluten-containing grains',
    commonNames: ['Wheat', 'Barley', 'Rye', 'Oats', 'Triticale', 'Spelt', 'Kamut'],
    riskLevel: 'high'
  },
  {
    id: 'a10',
    name: 'Sesame',
    description: 'Sesame seeds and sesame products',
    commonNames: ['Tahini', 'Sesame oil', 'Sesame flour', 'Sesame paste', 'Halva'],
    riskLevel: 'high'
  },
  {
    id: 'a11',
    name: 'Sulfites',
    description: 'Sulfur dioxide and sulfites',
    commonNames: ['E220-E228', 'Preservatives', 'Wine', 'Dried fruits', 'Vinegar'],
    riskLevel: 'medium'
  },
  {
    id: 'a12',
    name: 'Lupin',
    description: 'Lupin and lupin products',
    commonNames: ['Lupin flour', 'Lupin seeds', 'Lupin protein'],
    riskLevel: 'medium'
  },
  {
    id: 'a13',
    name: 'Celery',
    description: 'Celery and celery products',
    commonNames: ['Celery seeds', 'Celery salt', 'Celery root', 'Celeriac'],
    riskLevel: 'medium'
  },
  {
    id: 'a14',
    name: 'Mustard',
    description: 'Mustard and mustard products',
    commonNames: ['Mustard seeds', 'Mustard powder', 'Mustard greens', 'Dijon mustard'],
    riskLevel: 'medium'
  }
];

// Mock data for allergen control measures
export const MOCK_ALLERGEN_CONTROL_MEASURES: AllergenControlMeasure[] = [
  {
    id: 'acm1',
    name: 'Production Scheduling',
    description: 'Schedule production runs to minimize cross-contact (e.g., non-allergen products first, followed by allergen-containing products)',
    type: 'prevention',
    verificationMethod: 'Production schedule review',
    frequency: 'Daily',
    responsiblePerson: 'Production Manager',
    createdBy: 'user1',
    createdAt: new Date('2023-05-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-05-10')
  },
  {
    id: 'acm2',
    name: 'Dedicated Equipment',
    description: 'Use dedicated equipment for allergen-containing products where possible',
    type: 'segregation',
    verificationMethod: 'Equipment audit',
    frequency: 'Monthly',
    responsiblePerson: 'QA Manager',
    createdBy: 'user1',
    createdAt: new Date('2023-05-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-05-15')
  },
  {
    id: 'acm3',
    name: 'Validated Cleaning Procedures',
    description: 'Implement and validate cleaning procedures to remove allergen residues between production runs',
    type: 'cleaning',
    verificationMethod: 'Allergen swab testing',
    frequency: 'After each allergen changeover',
    responsiblePerson: 'Sanitation Manager',
    createdBy: 'user1',
    createdAt: new Date('2023-05-20'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-05-20')
  },
  {
    id: 'acm4',
    name: 'Allergen Labeling Verification',
    description: 'Verify that product labels accurately reflect allergen content',
    type: 'labeling',
    verificationMethod: 'Label review and approval',
    frequency: 'Each new product/label change',
    responsiblePerson: 'QA Manager',
    createdBy: 'user1',
    createdAt: new Date('2023-05-25'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-05-25')
  },
  {
    id: 'acm5',
    name: 'Allergen Awareness Training',
    description: 'Train all employees on allergen awareness and control procedures',
    type: 'training',
    verificationMethod: 'Training records review',
    frequency: 'Annual and new employee onboarding',
    responsiblePerson: 'Training Manager',
    createdBy: 'user1',
    createdAt: new Date('2023-05-30'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-05-30')
  }
];

// Mock data for allergen risk assessments
export const MOCK_ALLERGEN_RISK_ASSESSMENTS: AllergenRiskAssessment[] = [
  {
    id: 'ara1',
    productId: 'product1',
    assessmentDate: new Date('2024-01-15'),
    nextReviewDate: new Date('2025-01-15'),
    conductedBy: 'Food Safety Team',
    approvedBy: 'QA Manager',
    crossContactRisks: 'Potential cross-contact during shared equipment use for products containing milk and products without milk. Shared storage areas for raw ingredients.',
    controlMeasureEffectiveness: 'Current cleaning procedures validated by allergen testing. Production scheduling effective in minimizing risk. Dedicated utensils implemented.',
    correctiveActions: 'Implement additional training for temporary staff. Improve allergen storage with dedicated, labeled containers. Increase frequency of verification testing.',
    createdBy: 'user1',
    createdAt: new Date('2024-01-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-01-15')
  }
];

// Mock data for product allergens
export const MOCK_PRODUCT_ALLERGENS: ProductAllergen[] = [
  {
    id: 'pa1',
    productId: 'product1',
    allergenId: 'a1', // Milk
    status: 'present',
    controlMeasures: 'Clearly labeled on packaging, Dedicated storage area, Employee training',
    verificationProcedures: 'Label verification before production, Regular audits',
    createdBy: 'user1',
    createdAt: new Date('2023-06-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-06-10')
  },
  {
    id: 'pa2',
    productId: 'product1',
    allergenId: 'a2', // Eggs
    status: 'present',
    controlMeasures: 'Clearly labeled on packaging, Dedicated storage area, Employee training',
    verificationProcedures: 'Label verification before production, Regular audits',
    createdBy: 'user1',
    createdAt: new Date('2023-06-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-06-10')
  },
  {
    id: 'pa3',
    productId: 'product1',
    allergenId: 'a7', // Soy
    status: 'may_contain',
    controlMeasures: 'Precautionary labeling, Cleaning procedures between production runs',
    verificationProcedures: 'Allergen swab testing after cleaning, Regular audits',
    createdBy: 'user1',
    createdAt: new Date('2023-06-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2023-06-10')
  }
];

// Mock data for verification activities
export const MOCK_VERIFICATION_ACTIVITIES: VerificationActivity[] = [
  {
    id: 'va1',
    type: 'allergen_testing',
    date: new Date('2024-03-15'),
    productId: 'product1',
    allergenId: 'a1', // Milk
    result: 'pass',
    details: 'Allergen swab testing on production line after cleaning. No detectable milk protein residue.',
    conductedBy: 'QA Technician',
    verifiedBy: 'QA Manager',
    attachments: ['allergen_test_report_20240315.pdf'],
    createdBy: 'user1',
    createdAt: new Date('2024-03-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-03-15')
  },
  {
    id: 'va2',
    type: 'cleaning_validation',
    date: new Date('2024-02-20'),
    controlMeasureId: 'acm3', // Cleaning procedures
    result: 'pass',
    details: 'Validation of allergen cleaning procedure effectiveness. Visual inspection, ATP testing, and allergen-specific testing all passed.',
    conductedBy: 'Sanitation Supervisor',
    verifiedBy: 'Food Safety Manager',
    attachments: ['cleaning_validation_report_20240220.pdf'],
    createdBy: 'user1',
    createdAt: new Date('2024-02-20'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-02-20')
  },
  {
    id: 'va3',
    type: 'label_verification',
    date: new Date('2024-04-05'),
    productId: 'product1',
    result: 'fail',
    details: 'Pre-production label verification found missing allergen statement for eggs.',
    correctiveActions: 'Production run delayed. Labels corrected and verified before proceeding.',
    conductedBy: 'Production Supervisor',
    verifiedBy: 'QA Manager',
    createdBy: 'user1',
    createdAt: new Date('2024-04-05'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-04-05')
  },
  {
    id: 'va4',
    type: 'supplier_audit',
    date: new Date('2024-01-10'),
    result: 'pass',
    details: 'Audit of ingredient supplier allergen management program. Supplier demonstrated adequate controls for allergen segregation and labeling.',
    conductedBy: 'Supplier Quality Manager',
    verifiedBy: 'Food Safety Director',
    attachments: ['supplier_audit_report_20240110.pdf'],
    createdBy: 'user1',
    createdAt: new Date('2024-01-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-01-10')
  }
];

// Mock data for training records
export const MOCK_TRAINING_RECORDS: TrainingRecord[] = [
  {
    id: 'tr1',
    type: 'allergen_awareness',
    title: 'Allergen Awareness Training',
    description: 'Basic training on food allergens, their risks, and the importance of allergen management',
    date: new Date('2024-02-15'),
    duration: 120, // 2 hours
    trainer: 'Food Safety Manager',
    attendees: ['Production Team', 'QA Team', 'Warehouse Staff', 'New Employees'],
    materials: 'Allergen Awareness Presentation, Allergen Identification Guide, Case Studies',
    assessmentResults: '95% pass rate, 2 employees requiring retraining',
    nextTrainingDate: new Date('2025-02-15'),
    createdBy: 'user1',
    createdAt: new Date('2024-02-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-02-15')
  },
  {
    id: 'tr2',
    type: 'cross_contact_prevention',
    title: 'Preventing Cross-Contact in Production',
    description: 'Advanced training for production staff on preventing allergen cross-contact during manufacturing',
    date: new Date('2024-03-10'),
    duration: 180, // 3 hours
    trainer: 'Production Manager',
    attendees: ['Production Team Leaders', 'Line Operators', 'Sanitation Team'],
    materials: 'Cross-Contact Prevention Manual, Practical Demonstrations, Assessment Quiz',
    assessmentResults: '100% pass rate',
    nextTrainingDate: new Date('2024-09-10'),
    createdBy: 'user1',
    createdAt: new Date('2024-03-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-03-10')
  },
  {
    id: 'tr3',
    type: 'cleaning_procedures',
    title: 'Allergen Cleaning Procedures',
    description: 'Training on validated cleaning procedures to remove allergen residues from equipment and facilities',
    date: new Date('2024-01-20'),
    duration: 240, // 4 hours
    trainer: 'Sanitation Manager',
    attendees: ['Sanitation Team', 'Production Supervisors'],
    materials: 'Allergen Cleaning SOP, Practical Demonstrations, Verification Methods',
    assessmentResults: '90% pass rate, follow-up training scheduled for those who did not pass',
    nextTrainingDate: new Date('2024-07-20'),
    createdBy: 'user1',
    createdAt: new Date('2024-01-20'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'tr4',
    type: 'label_verification',
    title: 'Allergen Labeling Requirements and Verification',
    description: 'Training on allergen labeling requirements, label verification procedures, and common errors',
    date: new Date('2024-04-05'),
    duration: 120, // 2 hours
    trainer: 'QA Manager',
    attendees: ['QA Team', 'Production Supervisors', 'Packaging Team'],
    materials: 'Labeling Regulations Guide, Label Verification Checklist, Case Studies of Labeling Errors',
    nextTrainingDate: new Date('2024-10-05'),
    createdBy: 'user1',
    createdAt: new Date('2024-04-05'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-04-05')
  }
];
