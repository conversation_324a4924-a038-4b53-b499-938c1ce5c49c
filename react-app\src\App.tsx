import React from 'react';
import { Toaster } from 'react-hot-toast';
import { DocumentProvider } from './context/DocumentContext';
import Header from './components/Header';
import WelcomeMessage from './components/WelcomeMessage';
import DocumentForm from './components/DocumentForm';
import SearchFilters from './components/SearchFilters';
import DocumentTable from './components/DocumentTable';
import ExportControls from './components/ExportControls';
import LoadingOverlay from './components/LoadingOverlay';
import './styles/App.css';

const App: React.FC = () => {
  return (
    <DocumentProvider>
      <div className="app">
        <LoadingOverlay />
        <div className="container">
          <Header />
          <WelcomeMessage />
          <DocumentForm />
          <SearchFilters />
          <DocumentTable />
          <ExportControls />
        </div>
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 3000,
            style: {
              background: '#fff',
              color: '#111827',
              boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
              borderRadius: '8px',
              fontSize: '14px',
            },
            success: {
              iconTheme: {
                primary: '#10b981',
                secondary: '#fff',
              },
            },
            error: {
              iconTheme: {
                primary: '#ef4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </div>
    </DocumentProvider>
  );
};

export default App;
