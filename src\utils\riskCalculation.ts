
/**
 * Calculates the risk level based on severity and likelihood
 * @param severity - Severity rating (1-5)
 * @param likelihood - Likelihood rating (1-5)
 * @returns Risk score and level
 */
export const calculateRisk = (severity: number, likelihood: number) => {
  // Validate inputs
  if (severity < 1 || severity > 5 || likelihood < 1 || likelihood > 5) {
    throw new Error('Severity and likelihood must be between 1 and 5');
  }

  const riskScore = severity * likelihood;
  
  // Determine risk level based on score
  let riskLevel: 'low' | 'medium' | 'high';
  if (riskScore <= 6) {
    riskLevel = 'low';
  } else if (riskScore <= 15) {
    riskLevel = 'medium';
  } else {
    riskLevel = 'high';
  }

  return {
    score: riskScore,
    level: riskLevel
  };
};

/**
 * Determines if a hazard should be a CCP based on decision tree
 * Codex Alimentarius decision tree implementation
 * @param answers - Answers to decision tree questions
 * @returns Boolean indicating if the hazard is a CCP
 */
export const determineCCP = (answers: {
  q1: boolean; // Is there a control measure?
  q2: boolean; // Is control at this step necessary for safety?
  q3: boolean; // Is the step specifically designed to eliminate or reduce the hazard?
  q4: boolean; // Could contamination occur or increase to unacceptable levels?
  q5: boolean; // Will a subsequent step eliminate or reduce the hazard?
}): boolean => {
  // Q1: Is there a control measure?
  if (!answers.q1) {
    // If no control measure exists, move to Q2
    // Q2: Is control at this step necessary for food safety?
    if (answers.q2) {
      // If control is necessary but doesn't exist, process should be modified
      return true; // This is a CCP that needs to be addressed
    }
    return false; // Not a CCP
  }
  
  // Q3: Is this step specifically designed to eliminate or reduce the hazard?
  if (answers.q3) {
    return true; // This is a CCP
  }
  
  // Q4: Could contamination occur or increase to unacceptable levels?
  if (answers.q4) {
    // Q5: Will a subsequent step eliminate or reduce the hazard?
    if (!answers.q5) {
      return true; // This is a CCP
    }
  }
  
  return false; // Not a CCP
};

/**
 * Get color class for risk level
 */
export const getRiskColorClass = (riskLevel: 'low' | 'medium' | 'high') => {
  switch (riskLevel) {
    case 'low':
      return 'risk-low';
    case 'medium':
      return 'risk-medium';
    case 'high':
      return 'risk-high';
    default:
      return '';
  }
};
