import React, { useState, useEffect } from 'react';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { ProcessStepCard } from './ProcessStepCard';
import { SortableProcessStep } from './SortableProcessStep';
import { Button } from '@/components/ui/button';
import { ArrowDown, ChevronDown, ChevronUp, GripVertical, Plus } from 'lucide-react';
import { ProcessStep, Hazard } from '@/models/types';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { HazardTable } from '@/components/hazards/hazard-table';
import { updateProcessStepOrder } from '@/services/planService';
import { toast } from '@/hooks/use-toast';

interface InteractiveProcessFlowProps {
  processSteps: ProcessStep[];
  hazards: Hazard[];
  ccpData: any[];
  readOnly?: boolean;
  onProcessStepsChange?: (steps: ProcessStep[]) => void;
}

export const InteractiveProcessFlow: React.FC<InteractiveProcessFlowProps> = ({
  processSteps,
  hazards,
  ccpData,
  readOnly = false,
  onProcessStepsChange,
}) => {
  const [steps, setSteps] = useState<ProcessStep[]>(processSteps);
  const [activeId, setActiveId] = useState<string | null>(null);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [selectedStep, setSelectedStep] = useState<ProcessStep | null>(null);
  const [showHazardsDialog, setShowHazardsDialog] = useState(false);

  // Update steps when processSteps prop changes
  useEffect(() => {
    setSteps(processSteps);
  }, [processSteps]);

  // Configure DnD sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Handle drag start
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // Handle drag end
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;
    
    if (over && active.id !== over.id) {
      setSteps((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        
        const newItems = arrayMove(items, oldIndex, newIndex);
        
        // Update the order property of each item
        const updatedItems = newItems.map((item, index) => ({
          ...item,
          order: index + 1,
        }));
        
        // Call the update function to persist changes
        if (onProcessStepsChange) {
          onProcessStepsChange(updatedItems);
        }
        
        // Update in backend/localStorage
        updateProcessStepOrder(updatedItems);
        
        toast({
          title: "Process flow updated",
          description: "The order of process steps has been updated.",
        });
        
        return updatedItems;
      });
    }
    
    setActiveId(null);
  };

  // Get the active item
  const getActiveItem = () => {
    if (!activeId) return null;
    return steps.find((step) => step.id === activeId) || null;
  };

  // Check if a step is a CCP
  const isStepCCP = (stepId: string) => {
    return ccpData.some(ccp => ccp.hazard.processStepId === stepId);
  };

  // Check if a step has hazards
  const stepHasHazards = (stepId: string) => {
    return hazards.some(hazard => hazard.processStepId === stepId);
  };

  // Get hazards for a step
  const getHazardsForStep = (stepId: string) => {
    return hazards.filter(hazard => hazard.processStepId === stepId);
  };

  // Handle step click
  const handleStepClick = (step: ProcessStep) => {
    if (readOnly) return;
    setSelectedStep(step);
    setShowHazardsDialog(true);
  };

  // Render the process flow
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">2. Process Flow</h3>
        <div className="flex items-center space-x-2">
          {!readOnly && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? <ChevronDown className="h-4 w-4 mr-1" /> : <ChevronUp className="h-4 w-4 mr-1" />}
              {isCollapsed ? 'Expand' : 'Collapse'}
            </Button>
          )}
        </div>
      </div>

      <Collapsible open={!isCollapsed} className="space-y-2">
        <CollapsibleContent className="space-y-2">
          <div className="flex flex-col items-center space-y-2">
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragStart={handleDragStart}
              onDragEnd={handleDragEnd}
              modifiers={[restrictToVerticalAxis]}
            >
              <SortableContext
                items={steps.map(step => step.id)}
                strategy={verticalListSortingStrategy}
              >
                {steps.map((step, index) => (
                  <React.Fragment key={step.id}>
                    {index > 0 && <ArrowDown className="h-5 w-5 text-gray-400" />}
                    <SortableProcessStep
                      step={step}
                      isCCP={isStepCCP(step.id)}
                      hasHazards={stepHasHazards(step.id)}
                      hazards={getHazardsForStep(step.id)}
                      onClick={() => handleStepClick(step)}
                      disabled={readOnly}
                    />
                  </React.Fragment>
                ))}
              </SortableContext>

              <DragOverlay>
                {activeId ? (
                  <ProcessStepCard
                    step={getActiveItem()!}
                    isCCP={isStepCCP(activeId)}
                    hasHazards={stepHasHazards(activeId)}
                    hazards={getHazardsForStep(activeId)}
                    isDragging={true}
                  />
                ) : null}
              </DragOverlay>
            </DndContext>
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Dialog to show hazards for a selected step */}
      {selectedStep && (
        <Dialog open={showHazardsDialog} onOpenChange={setShowHazardsDialog}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                Hazards for {selectedStep.name} {isStepCCP(selectedStep.id) && '(CCP)'}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">{selectedStep.description}</p>
              
              <HazardTable
                hazards={getHazardsForStep(selectedStep.id)}
                readonly={true}
              />
              
              {getHazardsForStep(selectedStep.id).length === 0 && (
                <p className="text-center text-muted-foreground py-4">
                  No hazards have been identified for this process step.
                </p>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default InteractiveProcessFlow;

