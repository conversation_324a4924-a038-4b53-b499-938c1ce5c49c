import { useState, useCallback } from 'react';
import { Claim } from '@/models/capa';

const useClaimForm = (initialClaim?: Claim) => {
  const [claim, setClaim] = useState<Claim>(initialClaim || {
    id: '',
    customerName: '',
    contactInfo: '',
    productId: '',
    batchNumber: '',
    dateReceived: new Date(),
    description: '',
    severity: 'minor',
    status: 'new',
    createdBy: '',
    createdAt: new Date(),
    updatedBy: '',
    updatedAt: new Date(),
  });

  const handleChange = useCallback((event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement> | { target: { name: string; value: any } }) => {
    const { name, value } = event.target;
    setClaim(prevClaim => ({
      ...prevClaim,
      [name]: value
    }));
  }, []);

  const resetForm = useCallback(() => {
    setClaim({
      id: '',
      customerName: '',
      contactInfo: '',
      productId: '',
      batchNumber: '',
      dateReceived: new Date(),
      description: '',
      severity: 'minor',
      status: 'new',
      createdBy: '',
      createdAt: new Date(),
      updatedBy: '',
      updatedAt: new Date(),
    });
  }, []);

  const loadClaim = useCallback((claimToLoad: Claim) => {
    setClaim({
      ...claimToLoad
    });
  }, []);

  const updateField = useCallback((name: string, value: any) => {
    setClaim(prevClaim => ({
      ...prevClaim,
      [name]: value
    }));
  }, []);

  return {
    claim,
    handleChange,
    resetForm,
    loadClaim,
    updateField,
  };
};

export default useClaimForm;