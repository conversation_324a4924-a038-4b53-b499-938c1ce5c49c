
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { LoginForm } from '@/components/auth/login-form';
import { useAuth } from '@/contexts/AuthContext';

export function Login() {
  const { currentUser } = useAuth();
  const navigate = useNavigate();
  
  useEffect(() => {
    if (currentUser) {
      navigate('/dashboard');
    }
  }, [currentUser, navigate]);
  
  return (
    <div className="min-h-screen flex flex-col justify-center items-center bg-muted/30 p-4">
      <div className="w-full max-w-md mb-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-primary mb-2">HACCP Manager</h1>
          <p className="text-muted-foreground mb-8">
            Food Safety Management System
          </p>
        </div>
        <LoginForm />
      </div>
      <div className="text-center text-sm text-muted-foreground mt-6">
        <p>This is a demonstration HACCP management application</p>
        <p>© {new Date().getFullYear()} HACCP Manager</p>
      </div>
    </div>
  );
}

export default Login;
