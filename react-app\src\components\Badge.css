/* Type Badges */
.badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: inline-block;
}

.badge-processus {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
}

.badge-procedure {
  background: rgba(16, 185, 129, 0.1);
  color: #047857;
}

.badge-enregistrement {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
}

.badge-manuel {
  background: rgba(139, 92, 246, 0.1);
  color: #6b21a8;
}

.badge-logiciel {
  background: rgba(239, 68, 68, 0.1);
  color: #b91c1c;
}

.badge-default {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

/* Status Badges */
.status-badge {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  display: inline-block;
}

.status-ras {
  background: rgba(16, 185, 129, 0.1);
  color: #047857;
}

.status-modifier {
  background: rgba(245, 158, 11, 0.1);
  color: #92400e;
}

.status-creer {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
}

.status-annuler {
  background: rgba(239, 68, 68, 0.1);
  color: #b91c1c;
}

.status-default {
  background: rgba(107, 114, 128, 0.1);
  color: #374151;
}

/* Version Badge */
.version-badge {
  background: var(--primary-color);
  color: var(--white);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: 500;
  display: inline-block;
}
