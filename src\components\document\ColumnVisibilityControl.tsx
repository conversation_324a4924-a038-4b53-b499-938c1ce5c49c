import React from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Settings } from 'lucide-react';
import { Document } from '@/models/document';

export interface ColumnConfig {
  key: keyof Document;
  label: string;
  visible: boolean;
  essential?: boolean; // Cannot be hidden
}

interface ColumnVisibilityControlProps {
  columns: ColumnConfig[];
  onColumnsChange: (columns: ColumnConfig[]) => void;
}

const ColumnVisibilityControl: React.FC<ColumnVisibilityControlProps> = ({
  columns,
  onColumnsChange,
}) => {
  const handleColumnToggle = (columnKey: keyof Document, visible: boolean) => {
    const updatedColumns = columns.map(col =>
      col.key === columnKey ? { ...col, visible } : col
    );
    onColumnsChange(updatedColumns);
  };

  const handleShowAll = () => {
    const updatedColumns = columns.map(col => ({ ...col, visible: true }));
    onColumnsChange(updatedColumns);
  };

  const handleShowEssential = () => {
    const updatedColumns = columns.map(col => ({
      ...col,
      visible: col.essential || false
    }));
    onColumnsChange(updatedColumns);
  };

  const visibleCount = columns.filter(col => col.visible).length;

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          <Settings className="h-4 w-4 mr-2" />
          Columns ({visibleCount}/{columns.length})
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Column Visibility</DropdownMenuLabel>
        <DropdownMenuSeparator />
        
        <div className="p-2 space-y-2">
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleShowAll}
              className="flex-1 text-xs"
            >
              Show All
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleShowEssential}
              className="flex-1 text-xs"
            >
              Essential Only
            </Button>
          </div>
        </div>
        
        <DropdownMenuSeparator />
        
        <div className="max-h-64 overflow-y-auto">
          {columns.map((column) => (
            <DropdownMenuItem
              key={column.key}
              className="flex items-center space-x-2 cursor-pointer"
              onSelect={(e) => e.preventDefault()}
            >
              <Checkbox
                id={`column-${column.key}`}
                checked={column.visible}
                onCheckedChange={(checked) =>
                  handleColumnToggle(column.key, checked as boolean)
                }
                disabled={column.essential}
              />
              <label
                htmlFor={`column-${column.key}`}
                className="flex-1 text-sm cursor-pointer"
              >
                {column.label}
                {column.essential && (
                  <span className="text-xs text-muted-foreground ml-1">
                    (required)
                  </span>
                )}
              </label>
            </DropdownMenuItem>
          ))}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ColumnVisibilityControl;
