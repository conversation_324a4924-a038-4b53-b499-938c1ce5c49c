import React from 'react';
import { Handle, Position } from 'reactflow';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, AlertTriangle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProcessStep, Hazard } from '@/models/types';

interface ProcessStepNodeProps {
  data: {
    step: ProcessStep;
    isCCP: boolean;
    hasHazards: boolean;
    hazards: Hazard[];
  };
  isConnectable: boolean;
}

export const ProcessStepNode: React.FC<ProcessStepNodeProps> = ({ data, isConnectable }) => {
  const { step, isCCP, hasHazards } = data;

  // Determine the card style based on CCP status and hazards
  const getCardStyle = () => {
    if (isCCP) {
      return 'border-yellow-400 bg-yellow-50 shadow-md';
    } else if (hasHazards) {
      return 'border-orange-200 bg-orange-50';
    } else {
      return 'border-gray-200 bg-white';
    }
  };

  return (
    <>
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-gray-400"
      />
      <Card
        className={cn(
          'p-2 min-w-[150px] text-center transition-all duration-200',
          getCardStyle(),
          isCCP && 'ring-1 ring-yellow-400'
        )}
      >
        <div className="flex flex-col items-center space-y-1">
          <div className="flex items-center justify-center w-full">
            <span className="font-medium text-sm">{step.name}</span>
            {isCCP && (
              <Badge className="ml-2 bg-yellow-500 hover:bg-yellow-600 text-xs">CCP</Badge>
            )}
          </div>
          
          <div className="flex items-center justify-center space-x-1 text-xs text-muted-foreground">
            {hasHazards && !isCCP && (
              <AlertCircle className="h-3 w-3 text-orange-500" />
            )}
            {isCCP && (
              <AlertTriangle className="h-3 w-3 text-yellow-600" />
            )}
          </div>
        </div>
      </Card>
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-gray-400"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="right"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-gray-400"
      />
      <Handle
        type="target"
        position={Position.Left}
        id="left"
        isConnectable={isConnectable}
        className="w-2 h-2 bg-gray-400"
      />
    </>
  );
};