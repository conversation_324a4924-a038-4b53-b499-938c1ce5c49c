import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DialogWithMaxHeightCSS } from '@/components/ui/dialog-with-max-height-css';

import { Badge } from '@/components/ui/badge';
import { toast } from '@/hooks/use-toast';
import { CalendarIcon, CheckCircle, ClipboardList, FileText, PenLine, Plus, Search, Trash2, Users } from 'lucide-react';
import { FoodSafetyCultureElement, FoodSafetyCultureCategory, FoodSafetyCultureStatus, FOOD_SAFETY_CULTURE_CATEGORIES, MOCK_FOOD_SAFETY_CULTURE_ELEMENTS } from '@/models/foodSafetyCulture';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useLocalStorage } from '@/hooks/use-local-storage';

export default function FoodSafetyCulture() {
  const [activeTab, setActiveTab] = useState<FoodSafetyCultureCategory | 'all'>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [elements, setElements] = useLocalStorage<FoodSafetyCultureElement[]>('haccp_food_safety_culture', MOCK_FOOD_SAFETY_CULTURE_ELEMENTS);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedElement, setSelectedElement] = useState<FoodSafetyCultureElement | null>(null);

  // Form state
  const [formValues, setFormValues] = useState<Partial<FoodSafetyCultureElement>>({
    category: undefined,
    name: '',
    description: '',
    status: 'planned',
    responsiblePerson: '',
    implementationDate: undefined,
    lastAssessmentDate: undefined,
    nextAssessmentDate: undefined,
    evidenceOfImplementation: '',
    improvementActions: ''
  });

  // Filter elements based on active tab and search query
  const filteredElements = elements.filter(elem => {
    const matchesTab = activeTab === 'all' || elem.category === activeTab;
    const matchesSearch =
      elem.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      elem.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      elem.responsiblePerson.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesSearch;
  });

  // Calculate implementation progress
  const calculateProgress = () => {
    const totalElements = Object.keys(FOOD_SAFETY_CULTURE_CATEGORIES).length;
    const implementedCategories = new Set();

    elements.forEach(elem => {
      if (elem.status === 'implemented') {
        implementedCategories.add(elem.category);
      }
    });

    return Math.round((implementedCategories.size / totalElements) * 100);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: string) => {
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  // Handle date changes
  const handleDateChange = (name: string, date: Date | undefined) => {
    setFormValues(prev => ({ ...prev, [name]: date }));
  };

  // Open add dialog
  const openAddDialog = () => {
    setFormValues({
      category: undefined,
      name: '',
      description: '',
      status: 'planned',
      responsiblePerson: '',
      implementationDate: undefined,
      lastAssessmentDate: undefined,
      nextAssessmentDate: undefined,
      evidenceOfImplementation: '',
      improvementActions: ''
    });
    setIsAddDialogOpen(true);
  };

  // Open edit dialog
  const openEditDialog = (element: FoodSafetyCultureElement) => {
    setSelectedElement(element);
    setFormValues({
      category: element.category,
      name: element.name,
      description: element.description,
      status: element.status,
      responsiblePerson: element.responsiblePerson,
      implementationDate: element.implementationDate,
      lastAssessmentDate: element.lastAssessmentDate,
      nextAssessmentDate: element.nextAssessmentDate,
      evidenceOfImplementation: element.evidenceOfImplementation,
      improvementActions: element.improvementActions
    });
    setIsEditDialogOpen(true);
  };

  // Handle form submission for adding a new element
  const handleAddElement = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formValues.category || !formValues.name || !formValues.description) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    const newElement: FoodSafetyCultureElement = {
      id: `fsc${elements.length + 1}`,
      category: formValues.category as FoodSafetyCultureCategory,
      name: formValues.name,
      description: formValues.description,
      status: formValues.status as FoodSafetyCultureStatus,
      responsiblePerson: formValues.responsiblePerson || '',
      implementationDate: formValues.implementationDate,
      lastAssessmentDate: formValues.lastAssessmentDate,
      nextAssessmentDate: formValues.nextAssessmentDate,
      evidenceOfImplementation: formValues.evidenceOfImplementation || '',
      improvementActions: formValues.improvementActions || '',
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date()
    };

    setElements([...elements, newElement]);
    setIsAddDialogOpen(false);

    toast({
      title: 'Success',
      description: 'Food safety culture element added successfully'
    });
  };

  // Handle form submission for editing an element
  const handleEditElement = (e: React.FormEvent) => {
    e.preventDefault();

    if (!selectedElement) return;

    if (!formValues.category || !formValues.name || !formValues.description) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        variant: 'destructive'
      });
      return;
    }

    const updatedElements = elements.map(elem =>
      elem.id === selectedElement.id
        ? {
            ...elem,
            category: formValues.category as FoodSafetyCultureCategory,
            name: formValues.name,
            description: formValues.description,
            status: formValues.status as FoodSafetyCultureStatus,
            responsiblePerson: formValues.responsiblePerson || '',
            implementationDate: formValues.implementationDate,
            lastAssessmentDate: formValues.lastAssessmentDate,
            nextAssessmentDate: formValues.nextAssessmentDate,
            evidenceOfImplementation: formValues.evidenceOfImplementation || '',
            improvementActions: formValues.improvementActions || '',
            updatedBy: 'user1',
            updatedAt: new Date()
          }
        : elem
    );

    setElements(updatedElements);
    setIsEditDialogOpen(false);

    toast({
      title: 'Success',
      description: 'Food safety culture element updated successfully'
    });
  };

  // Delete an element
  const handleDeleteElement = (id: string) => {
    const updatedElements = elements.filter(elem => elem.id !== id);
    setElements(updatedElements);

    toast({
      title: 'Success',
      description: 'Food safety culture element deleted successfully'
    });
  };

  // Render status badge
  const renderStatusBadge = (status: FoodSafetyCultureStatus) => {
    switch (status) {
      case 'implemented':
        return <Badge className="bg-green-500">Implemented</Badge>;
      case 'in_progress':
        return <Badge className="bg-blue-500">In Progress</Badge>;
      case 'planned':
        return <Badge className="bg-yellow-500">Planned</Badge>;
      case 'not_implemented':
        return <Badge className="bg-red-500">Not Implemented</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">Food Safety & Quality Culture</h1>
        <Button onClick={openAddDialog}>
          <Plus className="h-4 w-4 mr-2" />
          Add Element
        </Button>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Implementation Progress</CardTitle>
          <CardDescription>
            FSSC 22000 v6.0 requires a robust Food Safety and Quality Culture plan
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm font-medium">{calculateProgress()}%</span>
            </div>
            <Progress value={calculateProgress()} className="h-2" />

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
              {Object.entries(FOOD_SAFETY_CULTURE_CATEGORIES).map(([key, { title }]) => {
                const hasImplemented = elements.some(elem =>
                  elem.category === key && elem.status === 'implemented'
                );

                return (
                  <div key={key} className="flex items-center gap-2">
                    <div className={`w-3 h-3 rounded-full ${hasImplemented ? 'bg-green-500' : 'bg-gray-300'}`} />
                    <span className="text-sm">{title}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex items-center space-x-2">
        <Search className="h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search elements..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <Tabs defaultValue="all" value={activeTab} onValueChange={(value) => setActiveTab(value as FoodSafetyCultureCategory | 'all')}>
        <TabsList className="mb-4 flex flex-wrap">
          <TabsTrigger value="all">All Elements</TabsTrigger>
          {Object.entries(FOOD_SAFETY_CULTURE_CATEGORIES).map(([key, { title }]) => (
            <TabsTrigger key={key} value={key}>{title}</TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="mt-0">
          {filteredElements.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <Users className="h-16 w-16 text-muted-foreground mb-4" />
                <h3 className="text-xl font-semibold mb-2">No Food Safety Culture Elements Found</h3>
                <p className="text-center text-muted-foreground max-w-md mb-4">
                  {searchQuery
                    ? 'No elements match your search criteria. Try a different search term.'
                    : activeTab !== 'all'
                      ? `No elements found for ${FOOD_SAFETY_CULTURE_CATEGORIES[activeTab as FoodSafetyCultureCategory].title}. Add one to get started.`
                      : 'No food safety culture elements have been added yet. Add one to get started.'}
                </p>
                <Button onClick={openAddDialog}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Food Safety Culture Element
                </Button>
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredElements.map((element) => (
                <Card key={element.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{element.name}</CardTitle>
                        <CardDescription>
                          {FOOD_SAFETY_CULTURE_CATEGORIES[element.category].title}
                        </CardDescription>
                      </div>
                      {renderStatusBadge(element.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm text-muted-foreground mb-4">{element.description}</p>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="font-medium">Responsible:</span>
                        <span>{element.responsiblePerson}</span>
                      </div>
                      {element.implementationDate && (
                        <div className="flex justify-between">
                          <span className="font-medium">Implemented:</span>
                          <span>{format(element.implementationDate, 'MMM d, yyyy')}</span>
                        </div>
                      )}
                      {element.nextAssessmentDate && (
                        <div className="flex justify-between">
                          <span className="font-medium">Next Assessment:</span>
                          <span>{format(element.nextAssessmentDate, 'MMM d, yyyy')}</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-end gap-2">
                    <Button variant="outline" size="sm" onClick={() => openEditDialog(element)}>
                      <PenLine className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                    <Button variant="destructive" size="sm" onClick={() => handleDeleteElement(element.id)}>
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* Add Element Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogWithMaxHeightCSS className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Food Safety Culture Element</DialogTitle>
            <DialogDescription>
              Create a new element for your food safety and quality culture plan.
            </DialogDescription>
          </DialogHeader>

            <form onSubmit={handleAddElement} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={formValues.category}
                    onValueChange={(value) => handleSelectChange('category', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(FOOD_SAFETY_CULTURE_CATEGORIES).map(([key, { title }]) => (
                        <SelectItem key={key} value={key}>{title}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formValues.status}
                  onValueChange={(value) => handleSelectChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="implemented">Implemented</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="planned">Planned</SelectItem>
                    <SelectItem value="not_implemented">Not Implemented</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">Element Name *</Label>
              <Input
                id="name"
                name="name"
                value={formValues.name}
                onChange={handleInputChange}
                placeholder="e.g., Food Safety Communication Plan"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues.description}
                onChange={handleInputChange}
                placeholder="Describe the food safety culture element..."
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="responsiblePerson">Responsible Person</Label>
              <Input
                id="responsiblePerson"
                name="responsiblePerson"
                value={formValues.responsiblePerson}
                onChange={handleInputChange}
                placeholder="e.g., Food Safety Team Leader"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Implementation Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.implementationDate ? format(formValues.implementationDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.implementationDate}
                      onSelect={(date) => handleDateChange('implementationDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Last Assessment Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.lastAssessmentDate ? format(formValues.lastAssessmentDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.lastAssessmentDate}
                      onSelect={(date) => handleDateChange('lastAssessmentDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Next Assessment Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.nextAssessmentDate ? format(formValues.nextAssessmentDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.nextAssessmentDate}
                      onSelect={(date) => handleDateChange('nextAssessmentDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="evidenceOfImplementation">Evidence of Implementation</Label>
              <Textarea
                id="evidenceOfImplementation"
                name="evidenceOfImplementation"
                value={formValues.evidenceOfImplementation}
                onChange={handleInputChange}
                placeholder="Describe the evidence that demonstrates implementation..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="improvementActions">Improvement Actions</Label>
              <Textarea
                id="improvementActions"
                name="improvementActions"
                value={formValues.improvementActions}
                onChange={handleInputChange}
                placeholder="Describe planned actions to improve this element..."
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Add Element
              </Button>
            </DialogFooter>
          </form>
        </DialogWithMaxHeightCSS>
      </Dialog>

      {/* Edit Element Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogWithMaxHeightCSS className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Food Safety Culture Element</DialogTitle>
            <DialogDescription>
              Update the details of this food safety culture element.
            </DialogDescription>
          </DialogHeader>

            <form onSubmit={handleEditElement} className="space-y-4">
              {/* Same form fields as Add Dialog */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={formValues.category}
                    onValueChange={(value) => handleSelectChange('category', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(FOOD_SAFETY_CULTURE_CATEGORIES).map(([key, { title }]) => (
                        <SelectItem key={key} value={key}>{title}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status *</Label>
                <Select
                  value={formValues.status}
                  onValueChange={(value) => handleSelectChange('status', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="implemented">Implemented</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="planned">Planned</SelectItem>
                    <SelectItem value="not_implemented">Not Implemented</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="name">Element Name *</Label>
              <Input
                id="name"
                name="name"
                value={formValues.name}
                onChange={handleInputChange}
                placeholder="e.g., Food Safety Communication Plan"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                name="description"
                value={formValues.description}
                onChange={handleInputChange}
                placeholder="Describe the food safety culture element..."
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="responsiblePerson">Responsible Person</Label>
              <Input
                id="responsiblePerson"
                name="responsiblePerson"
                value={formValues.responsiblePerson}
                onChange={handleInputChange}
                placeholder="e.g., Food Safety Team Leader"
              />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Implementation Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.implementationDate ? format(formValues.implementationDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.implementationDate}
                      onSelect={(date) => handleDateChange('implementationDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Last Assessment Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.lastAssessmentDate ? format(formValues.lastAssessmentDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.lastAssessmentDate}
                      onSelect={(date) => handleDateChange('lastAssessmentDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="space-y-2">
                <Label>Next Assessment Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {formValues.nextAssessmentDate ? format(formValues.nextAssessmentDate, 'PPP') : 'Select date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={formValues.nextAssessmentDate}
                      onSelect={(date) => handleDateChange('nextAssessmentDate', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="evidenceOfImplementation">Evidence of Implementation</Label>
              <Textarea
                id="evidenceOfImplementation"
                name="evidenceOfImplementation"
                value={formValues.evidenceOfImplementation}
                onChange={handleInputChange}
                placeholder="Describe the evidence that demonstrates implementation..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="improvementActions">Improvement Actions</Label>
              <Textarea
                id="improvementActions"
                name="improvementActions"
                value={formValues.improvementActions}
                onChange={handleInputChange}
                placeholder="Describe planned actions to improve this element..."
              />
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <CheckCircle className="h-4 w-4 mr-2" />
                Update Element
              </Button>
            </DialogFooter>
          </form>
        </DialogWithMaxHeightCSS>
      </Dialog>
    </div>
  );
}
