.app-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
  padding: var(--spacing-xl) 0;
}

.app-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 var(--spacing-sm) 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.app-header h1 i {
  color: var(--primary-color);
  font-size: 2.25rem;
}

.app-subtitle {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
  font-weight: 400;
}

@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2rem;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .app-header h1 i {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: var(--spacing-md) 0;
  }

  .app-header h1 {
    font-size: 1.5rem;
  }
}
