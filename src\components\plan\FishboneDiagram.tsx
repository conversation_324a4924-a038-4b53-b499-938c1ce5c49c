import React, { useMemo } from 'react';
import { ProcessStep, Hazard } from '@/models/types';

interface FishboneDiagramProps {
  processSteps: ProcessStep[];
  hazards: Hazard[];
  ccpData: any[];
  title?: string;
  customCategories?: FishboneCategory[];
  problemStatement?: string;
}

interface FishboneCategory {
  name: string;
  causes: string[];
  position: 'top' | 'bottom';
  angle: number;
}

export const FishboneDiagram: React.FC<FishboneDiagramProps> = ({
  processSteps,
  hazards,
  ccpData,
  title = "Figure 1. Domaines de prévention en matière de Food Defense",
  customCategories,
  problemStatement = "Protection de la chaîne alimentaire contre les risques d'actions malveillantes, criminelles ou terroristes",
}) => {
  // Generate categories from hazards if no custom categories provided
  const generateCategoriesFromHazards = useMemo(() => {
    if (customCategories) return customCategories;

    // If we have hazards, we could group them by type
    if (hazards.length > 0) {
      const hazardGroups = hazards.reduce((acc, hazard) => {
        const category = hazard.type || 'General';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(hazard.description);
        return acc;
      }, {} as Record<string, string[]>);

      const categoryNames = Object.keys(hazardGroups);
      return categoryNames.map((name, index) => ({
        name,
        causes: hazardGroups[name],
        position: (index % 2 === 0 ? 'top' : 'bottom') as 'top' | 'bottom',
        angle: index % 2 === 0 ? -30 - (index * 10) : 30 + (index * 10)
      }));
    }

    // Default Food Defense categories
    return null;
  }, [hazards, customCategories]);

  // Food Defense categories based on the image
  const categories: FishboneCategory[] = useMemo(() => generateCategoriesFromHazards || [
    {
      name: 'Stocks',
      position: 'top',
      angle: -45,
      causes: [
        'Sûreté des stocks',
        'Gestion des stocks',
        'Inventaires'
      ]
    },
    {
      name: 'Flux',
      position: 'top',
      angle: -30,
      causes: [
        'Véhicules',
        'Produits chimiques et biologiques dangereux',
        'Personnes',
        'Marchandises',
        'Eau',
        'Laboratoires d\'analyses chimiques / bio'
      ]
    },
    {
      name: 'Accès',
      position: 'top',
      angle: -15,
      causes: [
        'Prévention et détection d\'intrusion dans les installations',
        'Accès aux stocks',
        'Protection physique des accès aux bâtiments, installations et lieux de stockage',
        'Protection physique périphérique'
      ]
    },
    {
      name: 'Suppléments spécifiques',
      position: 'bottom',
      angle: 45,
      causes: [
        'Collecte',
        'Restauration',
        'Importateurs',
        'Transport / logistique',
        'Sécurité informatique'
      ]
    },
    {
      name: 'Process',
      position: 'bottom',
      angle: 30,
      causes: [
        'Fournisseurs agréés',
        'Vêtements et locaux',
        'Mise en œuvre',
        'Intégrité des produits',
        'Quarantaine des produits suspects'
      ]
    },
    {
      name: 'Personnel',
      position: 'bottom',
      angle: 15,
      causes: [
        'Accueil des salariés',
        'Vêtements et locaux',
        'Formation',
        'Recrutement des salariés et collaborateurs internes',
        'Comportements hors normes',
        'Règlement intérieur'
      ]
    }
  ], [customCategories]);

  const svgWidth = 1200;
  const svgHeight = 700;
  const centerX = svgWidth * 0.1;
  const centerY = svgHeight / 2;
  const spineLength = svgWidth * 0.65;
  const boneLength = 180;

  return (
    <div className="w-full h-full flex items-center justify-center bg-white">
      <svg
        width={svgWidth}
        height={svgHeight}
        viewBox={`0 0 ${svgWidth} ${svgHeight}`}
        className="border rounded"
      >
        {/* Main spine (horizontal line) */}
        <line
          x1={centerX}
          y1={centerY}
          x2={centerX + spineLength}
          y2={centerY}
          stroke="#374151"
          strokeWidth="3"
          markerEnd="url(#arrowhead)"
        />

        {/* Arrow marker definition */}
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#374151"
            />
          </marker>
        </defs>

        {/* Main problem/effect box */}
        <g>
          <rect
            x={centerX + spineLength + 15}
            y={centerY - 50}
            width={280}
            height={100}
            fill="#f8f9fa"
            stroke="#374151"
            strokeWidth="2"
            rx="8"
          />
          <foreignObject
            x={centerX + spineLength + 25}
            y={centerY - 40}
            width={260}
            height={80}
          >
            <div className="text-sm font-semibold text-center p-3 leading-tight text-gray-800">
              {problemStatement}
            </div>
          </foreignObject>
        </g>

        {/* Category bones and labels */}
        {categories.map((category, index) => {
          const isTop = category.position === 'top';
          const categoryIndex = isTop ? index : index - 3;
          const boneStartX = centerX + (categoryIndex + 1) * (spineLength / 4);
          const boneStartY = centerY;

          const angleRad = (category.angle * Math.PI) / 180;
          const boneEndX = boneStartX + boneLength * Math.cos(angleRad);
          const boneEndY = boneStartY + boneLength * Math.sin(angleRad);

          return (
            <g key={category.name}>
              {/* Main bone line */}
              <line
                x1={boneStartX}
                y1={boneStartY}
                x2={boneEndX}
                y2={boneEndY}
                stroke="#6b7280"
                strokeWidth="2"
              />

              {/* Category label */}
              <g>
                <text
                  x={boneEndX}
                  y={boneEndY + (isTop ? -10 : 20)}
                  textAnchor="middle"
                  className="text-sm font-bold fill-gray-600"
                >
                  {category.name}
                </text>
              </g>

              {/* Sub-causes */}
              {category.causes.map((cause, causeIndex) => {
                const subBoneLength = 80;
                const spacing = boneLength / (category.causes.length + 1);

                const subBoneStartX = boneStartX + (causeIndex + 1) * spacing * Math.cos(angleRad);
                const subBoneStartY = boneStartY + (causeIndex + 1) * spacing * Math.sin(angleRad);

                // Sub-bones are perpendicular to main bone
                const perpAngleRad = angleRad + (isTop ? Math.PI/2 : -Math.PI/2);
                const subBoneEndX = subBoneStartX + subBoneLength * Math.cos(perpAngleRad);
                const subBoneEndY = subBoneStartY + subBoneLength * Math.sin(perpAngleRad);

                return (
                  <g key={`${category.name}-${causeIndex}`}>
                    {/* Sub-bone line */}
                    <line
                      x1={subBoneStartX}
                      y1={subBoneStartY}
                      x2={subBoneEndX}
                      y2={subBoneEndY}
                      stroke="#9ca3af"
                      strokeWidth="1"
                    />

                    {/* Cause text */}
                    <foreignObject
                      x={subBoneEndX + (isTop ? -100 : 5)}
                      y={subBoneEndY + (isTop ? -20 : -10)}
                      width={95}
                      height={40}
                    >
                      <div className="text-xs text-gray-700 leading-tight font-medium">
                        {cause}
                      </div>
                    </foreignObject>
                  </g>
                );
              })}
            </g>
          );
        })}

        {/* Title */}
        <text
          x={svgWidth / 2}
          y={30}
          textAnchor="middle"
          className="text-sm font-bold fill-gray-800"
        >
          {title}
        </text>
      </svg>
    </div>
  );
};
