import React from 'react';
import toast from 'react-hot-toast';
import { useDocuments } from '../context/DocumentContext';
import { Document } from '../types';
import TypeBadge from './TypeBadge';
import StatusBadge from './StatusBadge';
import ActionButtons from './ActionButtons';

interface DocumentRowProps {
  document: Document;
}

const DocumentRow: React.FC<DocumentRowProps> = ({ document }) => {
  const { setCurrentEditId } = useDocuments();

  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB');
  };

  const getFileIcon = (format: string): string => {
    const icons: Record<string, string> = {
      'Word': 'word',
      'Excel': 'excel',
      'PowerPoint': 'powerpoint',
      'PPT': 'powerpoint',
      'PDF': 'pdf'
    };
    return icons[format] || 'alt';
  };

  const handleView = () => {
    if (document.fileData) {
      window.open(document.fileData, '_blank');
    } else {
      toast.error('No file attached to this document');
    }
  };

  const handleEdit = () => {
    setCurrentEditId(document.id);
    // Scroll to form
    const formSection = window.document.querySelector('.form-section');
    if (formSection) {
      formSection.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
    toast.success('Document loaded for editing', { duration: 2000 });
  };

  return (
    <tr className={document.status === 'Modifier' ? 'status-modifier' : ''}>
      <td>
        <TypeBadge type={document.type} />
      </td>
      <td>
        <div className="cell-content">
          <div className="cell-title">{document.designation}</div>
          {document.createdAt && (
            <div className="cell-subtitle">
              Created: {formatDate(document.createdAt.split('T')[0])}
            </div>
          )}
        </div>
      </td>
      <td>
        <code className="ref-id">{document.referenceId}</code>
      </td>
      <td>
        <i className={`fas fa-file-${getFileIcon(document.format)}`}></i>
        {' '}{document.format}
      </td>
      <td>{formatDate(document.effectiveDate)}</td>
      <td className="text-center">
        <span className="version-badge">v{document.version}</span>
      </td>
      <td>
        <StatusBadge status={document.status} />
      </td>
      <td>
        {document.fileData ? (
          <button 
            onClick={handleView}
            className="file-link"
            type="button"
          >
            <i className="fas fa-paperclip"></i> {document.fileName}
          </button>
        ) : (
          <span className="text-muted">No attachment</span>
        )}
      </td>
      <td className="text-center">
        <ActionButtons 
          document={document}
          onView={handleView}
          onEdit={handleEdit}
        />
      </td>
    </tr>
  );
};

export default DocumentRow;
