export interface Document {
  id: string;
  documentReferenceNumber: string;
  documentTitle: string;
  version: number;
  issueDate: string;
  authorFunction: string;
  responsibleDepartment: string;
  status: DocumentStatus;
  approvalDate?: string;
  approvedByFunction?: string;
  revisionDate?: string;
  revisionReason?: string;
  previousReferenceNumber?: string;
  storageLocation: string;
  distributionMethod?: string;
  remarks?: string;
  fileName?: string;
  fileData?: string;
  createdAt: string;
  updatedAt: string;
  accessLevel: AccessLevel;
}

export type DocumentType = 
  | 'Processus'
  | 'Procédure'
  | 'Enregistrement'
  | 'Manuel'
  | 'Logiciel';

export type DocumentFormat = 
  | 'Word'
  | 'Excel'
  | 'PPT'
  | 'PDF'
  | 'Other';

export type DocumentStatus = 
  | 'RAS'
  | 'Modifier'
  | 'Créer'
  | 'Annuler';

export type AccessLevel =
  | 'Public'
  | 'Internal'
  | 'Confidential';

export interface DocumentFormData {
  documentReferenceNumber: string;
  documentTitle: string;
  version: number;
  issueDate: string;
  authorFunction: string;
  responsibleDepartment: string;
  status: DocumentStatus | '';
  approvalDate?: string;
  approvedByFunction?: string;
  revisionDate?: string;
  revisionReason?: string;
  previousReferenceNumber?: string;
  storageLocation: string;
  distributionMethod?: string;
  remarks?: string;
  accessLevel: AccessLevel | '';
}

export interface SearchFilters {
  searchTerm: string;
  typeFilter: DocumentType | '';
  statusFilter: DocumentStatus | '';
}

export interface SortConfig {
  column: keyof Document | null;
  direction: 'asc' | 'desc';
}

export interface DocumentState {
  documents: Document[];
  currentEditId: string | null;
  searchFilters: SearchFilters;
  sortConfig: SortConfig;
  isLoading: boolean;
}

// Document type options for forms
export const DOCUMENT_TYPES: DocumentType[] = [
  'Processus', 
  'Procédure', 
  'Enregistrement', 
  'Manuel', 
  'Logiciel'
];

export const DOCUMENT_FORMATS: DocumentFormat[] = [
  'Word', 
  'Excel', 
  'PPT', 
  'PDF', 
  'Other'
];

export const DOCUMENT_STATUSES: DocumentStatus[] = [
  'RAS', 
  'Modifier', 
  'Créer', 
  'Annuler'
];

export const DOCUMENT_ACCESS_LEVELS: AccessLevel[] = [
'Public',
'Internal',
'Confidential',
];

// Status color mappings for UI
export const STATUS_COLORS = {
  'RAS': 'bg-green-100 text-green-800',
  'Modifier': 'bg-yellow-100 text-yellow-800',
  'Créer': 'bg-blue-100 text-blue-800',
  'Annuler': 'bg-red-100 text-red-800',
} as const;

// Type color mappings for UI
export const TYPE_COLORS = {
  'Processus': 'bg-purple-100 text-purple-800',
  'Procédure': 'bg-blue-100 text-blue-800',
  'Enregistrement': 'bg-green-100 text-green-800',
  'Manuel': 'bg-orange-100 text-orange-800',
  'Logiciel': 'bg-gray-100 text-gray-800',
} as const;

// Format icons for UI
export const FORMAT_ICONS = {
  'Word': 'FileText',
  'Excel': 'FileSpreadsheet',
  'PPT': 'Presentation',
  'PDF': 'FileText',
  'Other': 'File',
} as const;
