import { Hazard, CCP, ProcessStep } from '@/models/types';

/**
 * Mock data service for testing
 */

// Mock process steps
export const mockProcessSteps: ProcessStep[] = [
  { id: 'step1', name: 'Raw Material Reception', description: 'Receiving raw materials from suppliers', order: 1 },
  { id: 'step2', name: 'Storage', description: 'Storing raw materials in appropriate conditions', order: 2 },
  { id: 'step3', name: 'Preparation', description: 'Preparing ingredients for cooking', order: 3 },
  { id: 'step4', name: 'Cooking', description: 'Cooking the product to required temperature', order: 4 },
  { id: 'step5', name: 'Cooling', description: 'Cooling the product to safe temperature', order: 5 },
  { id: 'step6', name: 'Packaging', description: 'Packaging the product for distribution', order: 6 },
  { id: 'step7', name: 'Storage & Distribution', description: 'Storing and distributing the finished product', order: 7 },
];

// Mock hazards
export const mockHazards: Hazard[] = [
  {
    id: 'h1',
    processStepId: 'step1',
    description: 'Salmonella in raw chicken',
    type: 'Biological',
    severity: 4,
    likelihood: 3,
    controlMeasures: 'Supplier approval program, Certificate of Analysis, Temperature control during reception',
    isCCP: false,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h2',
    processStepId: 'step1',
    description: 'Pesticide residues on produce',
    type: 'Chemical',
    severity: 3,
    likelihood: 2,
    controlMeasures: 'Approved supplier program, Certificates of Analysis',
    isCCP: false,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h3',
    processStepId: 'step2',
    description: 'Growth of pathogens during storage',
    type: 'Biological',
    severity: 3,
    likelihood: 3,
    controlMeasures: 'Temperature control, Stock rotation, Segregation of raw and cooked products',
    isCCP: false,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h4',
    processStepId: 'step4',
    description: 'Survival of pathogens due to inadequate cooking',
    type: 'Biological',
    severity: 5,
    likelihood: 3,
    controlMeasures: 'Time/temperature control, Staff training, Regular calibration of thermometers',
    isCCP: true,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h5',
    processStepId: 'step5',
    description: 'Growth of pathogens during cooling',
    type: 'Biological',
    severity: 5,
    likelihood: 4,
    controlMeasures: 'Rapid cooling, Temperature monitoring, Staff training',
    isCCP: true,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
  {
    id: 'h6',
    processStepId: 'step6',
    description: 'Metal contamination from equipment',
    type: 'Physical',
    severity: 4,
    likelihood: 2,
    controlMeasures: 'Metal detection, Equipment maintenance, Visual inspection',
    isCCP: true,
    createdBy: 'user1',
    createdAt: new Date(),
    updatedBy: 'user1',
    updatedAt: new Date(),
  },
];

// Mock CCPs
export const mockCCPs: CCP[] = [
  {
    id: 'ccp1',
    hazardId: 'h4',
    criticalLimit: 'Internal temperature must reach minimum 75°C for at least 15 seconds',
    monitoringProcedure: 'Measure core temperature with calibrated thermometer',
    monitoringFrequency: 'Every batch',
    correctiveAction: 'If temperature is below 75°C, continue cooking until temperature is reached. If product cannot reach temperature, reject the batch.',
    verificationProcedure: 'Daily calibration of thermometers, Weekly review of monitoring records',
    recordkeepingProcedure: 'Cooking temperature log, Corrective action records, Thermometer calibration records',
  },
  {
    id: 'ccp2',
    hazardId: 'h5',
    criticalLimit: 'Cool from 60°C to 21°C within 2 hours and from 21°C to 5°C within 4 hours',
    monitoringProcedure: 'Measure temperature with calibrated thermometer at specified intervals',
    monitoringFrequency: 'Every batch at 2-hour and 4-hour marks',
    correctiveAction: 'If cooling rate is too slow, divide product into smaller portions, use ice bath, or discard if time/temperature parameters cannot be met',
    verificationProcedure: 'Weekly review of cooling records, Periodic testing of cooling equipment',
    recordkeepingProcedure: 'Cooling temperature log, Corrective action records, Equipment maintenance records',
  },
  {
    id: 'ccp3',
    hazardId: 'h6',
    criticalLimit: 'No metal fragments > 2mm',
    monitoringProcedure: 'Pass all products through calibrated metal detector',
    monitoringFrequency: 'All products',
    correctiveAction: 'If metal is detected, reject product, identify source, and inspect equipment',
    verificationProcedure: 'Hourly checks of metal detector with test pieces, Maintenance records review',
    recordkeepingProcedure: 'Metal detector check log, Corrective action records, Maintenance records',
  },
];

// Initialize mock data in localStorage
export const initializeMockData = () => {
  // Check if data already exists
  const hazards = localStorage.getItem('haccp_hazards');
  const ccps = localStorage.getItem('haccp_ccps');
  const processSteps = localStorage.getItem('haccp_process_steps');
  
  // If not, add mock data
  if (!hazards) {
    localStorage.setItem('haccp_hazards', JSON.stringify(mockHazards));
  }
  
  if (!ccps) {
    localStorage.setItem('haccp_ccps', JSON.stringify(mockCCPs));
  }
  
  if (!processSteps) {
    localStorage.setItem('haccp_process_steps', JSON.stringify(mockProcessSteps));
  }
};
