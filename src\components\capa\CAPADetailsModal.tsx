import React from 'react';
import { format } from 'date-fns';
import { 
  Sheet, 
  <PERSON><PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  Sheet<PERSON><PERSON><PERSON>, 
  SheetDes<PERSON>,
  SheetFooter,
  SheetClose
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CAPA, Claim } from '@/models/capa';
import { PenLine, FileText, Calendar } from 'lucide-react';

interface CAPADetailsModalProps {
  capa: CAPA | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onEdit: (capa: CAPA) => void;
  onNavigateTab: (tab: string, capaId: string) => void;
  products: { id: string; name: string; description: string }[];
  claims: Claim[];
  getStatusBadge: (status: string) => React.ReactNode;
  getPriorityBadge: (status: string) => React.ReactNode;
}

export function CAPADetailsModal({
  capa,
  open,
  onOpenChange,
  onEdit,
  onNavigateTab,
  products,
  claims,
  getStatusBadge,
  getPriorityBadge
}: CAPADetailsModalProps) {
  if (!capa) return null;

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent 
        side="right" 
        className="w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl overflow-y-auto"
        onKeyDown={(e) => {
          // Add keyboard shortcuts
          if (e.key === 'e' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            onEdit(capa);
          }
        }}
      >
        <SheetHeader className="mb-6">
          <SheetTitle className="text-xl">CAPA Details</SheetTitle>
          <SheetDescription>
            View complete information and navigate to related items
          </SheetDescription>
        </SheetHeader>

        <div className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">ID</h3>
              <p>{capa.id}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Status</h3>
              <p>{getStatusBadge(capa.status)}</p>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Title</h3>
            <p className="font-medium">{capa.title}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
            <p>{capa.description}</p>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Category</h3>
              <p className="capitalize">{capa.category.replace('_', ' ')}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Priority</h3>
              <p>{getPriorityBadge(capa.priority)}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Source</h3>
              <p className="capitalize">{capa.source.replace('_', ' ')}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Responsible Person</h3>
              <p>{capa.responsiblePerson}</p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Date Identified</h3>
              <p>{format(capa.dateIdentified, 'PPP')}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Target Completion Date</h3>
              <p>{format(capa.targetCompletionDate, 'PPP')}</p>
            </div>
          </div>
          
          {capa.actualCompletionDate && (
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Actual Completion Date</h3>
              <p>{format(capa.actualCompletionDate, 'PPP')}</p>
            </div>
          )}
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Related Product</h3>
              <p>
                {capa.productId 
                  ? products.find(p => p.id === capa.productId)?.name || 'Unknown Product'
                  : 'None'}
              </p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Related Claim</h3>
              <p>
                {capa.claimId 
                  ? claims.find(c => c.id === capa.claimId)?.customerName || 'Unknown Claim'
                  : 'None'}
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Created By</h3>
              <p>{capa.createdBy} on {format(capa.createdAt, 'PPP')}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Updated</h3>
              <p>{capa.updatedBy} on {format(capa.updatedAt, 'PPP')}</p>
            </div>
          </div>
          
          <div className="pt-4 border-t">
            <h3 className="font-medium mb-4">Related Items</h3>
            <div className="grid grid-cols-1 gap-4">
              <div className="p-4 bg-muted rounded-md flex flex-col">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                    <p className="font-medium">Root Cause Analysis</p>
                  </div>
                  {capa.rootCauseAnalysisId && (
                    <Badge variant="outline" className="ml-2">1</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {capa.rootCauseAnalysisId ? '1 analysis conducted' : 'None conducted yet'}
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-auto"
                  onClick={() => onNavigateTab('root-cause', capa.id)}
                >
                  {capa.rootCauseAnalysisId ? 'View Analysis' : 'Add Analysis'}
                </Button>
              </div>
              
              <div className="p-4 bg-muted rounded-md flex flex-col">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <p className="font-medium">Corrective Actions</p>
                  </div>
                  {capa.correctiveActionIds.length > 0 && (
                    <Badge variant="outline" className="ml-2">{capa.correctiveActionIds.length}</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {capa.correctiveActionIds.length} action(s) defined
                </p>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="mt-auto"
                  onClick={() => onNavigateTab('corrective-actions', capa.id)}
                >
                  {capa.correctiveActionIds.length > 0 ? 'View Actions' : 'Add Action'}
                </Button>
              </div>
              
              <div className="p-4 bg-muted rounded-md flex flex-col">
                <div className="flex justify-between items-center mb-2">
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                    <p className="font-medium">Preventive Actions</p>
                  </div>
                  {capa.preventiveActionIds.length > 0 && (
                    <Badge variant="outline" className="ml-2">{capa.preventiveActionIds.length}</Badge>
                  )}
                </div>
                <p className="text-sm text-muted-foreground mb-3">
                  {capa.preventiveActionIds.length} action(s) defined
                </p>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="mt-auto"
                  onClick={() => onNavigateTab('preventive-actions', capa.id)}
                >
                  {capa.preventiveActionIds.length > 0 ? 'View Actions' : 'Add Action'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        <SheetFooter className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mt-6">
          <div className="flex flex-col sm:flex-row gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="mr-2"
              onClick={() => onEdit(capa)}
            >
              <PenLine className="h-4 w-4 mr-2" />
              Edit CAPA
            </Button>
            <div className="text-xs text-muted-foreground mt-1">
              Keyboard shortcuts: <kbd className="px-1 py-0.5 bg-muted rounded border">Esc</kbd> to close, 
              <kbd className="px-1 py-0.5 bg-muted rounded border ml-1">{navigator.platform.includes('Mac') ? '⌘' : 'Ctrl'}+E</kbd> to edit
            </div>
          </div>
          <SheetClose asChild>
            <Button variant="default">Close</Button>
          </SheetClose>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
