import React, { useMemo, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useDocuments } from '@/contexts/DocumentContext';
import { DOCUMENT_STATUSES } from '@/models/document';
import { Search, Filter, X } from 'lucide-react';
import AdvancedSearch, { AdvancedFilters } from './AdvancedSearch';

interface SearchFiltersProps {
}

const SearchFilters: React.FC<SearchFiltersProps> = () => {
  const { searchFilters, setSearchFilters, documents } = useDocuments();

  // Advanced search state
  const [advancedFilters, setAdvancedFilters] = useState<AdvancedFilters>({
    searchTerm: '',
    departments: [],
    accessLevels: [],
    dateRange: { from: null, to: null },
    statuses: [],
    authors: [],
  });

  // Extract unique values from documents for filter options
  const availableDepartments = useMemo(() => {
    const departments = documents.map(doc => doc.responsibleDepartment);
    return [...new Set(departments)].filter(Boolean).sort();
  }, [documents]);

  const availableAuthors = useMemo(() => {
    const authors = documents.map(doc => doc.authorFunction);
    return [...new Set(authors)].filter(Boolean).sort();
  }, [documents]);

  const handleSearchChange = (value: string) => {
    setSearchFilters({ searchTerm: value });
    setAdvancedFilters(prev => ({ ...prev, searchTerm: value }));
  };

  const handleStatusFilterChange = (value: string) => {
    setSearchFilters({ statusFilter: value === 'all' ? '' : value as any });
  };

  const handleClearFilters = () => {
    setSearchFilters({
      searchTerm: '',
      typeFilter: '',
      statusFilter: '',
    });
    setAdvancedFilters({
      searchTerm: '',
      departments: [],
      accessLevels: [],
      dateRange: { from: null, to: null },
      statuses: [],
      authors: [],
    });
  };

  const handleAdvancedFiltersChange = (filters: AdvancedFilters) => {
    setAdvancedFilters(filters);
    // Update basic search term to sync with advanced search
    setSearchFilters({ searchTerm: filters.searchTerm });
  };

  const hasActiveFilters = searchFilters.searchTerm || searchFilters.statusFilter;
  const hasAdvancedFilters = advancedFilters.departments.length > 0 ||
    advancedFilters.accessLevels.length > 0 ||
    advancedFilters.dateRange.from ||
    advancedFilters.dateRange.to ||
    advancedFilters.statuses.length > 0 ||
    advancedFilters.authors.length > 0;

  const statusCounts = DOCUMENT_STATUSES.reduce((acc: Record<string, number>, status) => {
    acc[status] = documents.filter(doc => !searchFilters.statusFilter || DOCUMENT_STATUSES.includes(doc.status as any)).length;
    return acc;
  }, {} as Record<string, number>);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Search & Filters
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by document title or reference number..."
              value={searchFilters.searchTerm}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filters Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Status Filter */}
            <div>
              <label className="text-sm font-medium mb-2 block">Status</label>
              <Select
                value={searchFilters.statusFilter || 'all'}
                onValueChange={handleStatusFilterChange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses ({documents.length})</SelectItem>
                  {DOCUMENT_STATUSES.map(status => (
                    <SelectItem key={status} value={status}>
                      {status} ({statusCounts[status] || 0})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Advanced Search */}
            <div className="flex items-end">
              <AdvancedSearch
                filters={advancedFilters}
                onFiltersChange={handleAdvancedFiltersChange}
                availableDepartments={availableDepartments}
                availableAuthors={availableAuthors}
              />
            </div>

            {/* Clear Filters */}
            <div className="flex items-end">
              <Button
                variant="outline"
                onClick={handleClearFilters}
                disabled={!hasActiveFilters && !hasAdvancedFilters}
                className="w-full"
              >
                <X className="h-4 w-4 mr-2" />
                Clear All Filters
              </Button>
            </div>
          </div>

          {/* Active Filters Summary */}
          {(hasActiveFilters || hasAdvancedFilters) && (
            <div className="flex flex-wrap gap-2 pt-2 border-t">
              <span className="text-sm text-muted-foreground">Active filters:</span>
              {searchFilters.searchTerm && (
                <Badge variant="secondary" className="text-xs">
                  Search: "{searchFilters.searchTerm}"
                </Badge>
              )}

              {searchFilters.statusFilter && (
                <Badge variant="secondary" className="text-xs">
                  Status: {searchFilters.statusFilter}
                </Badge>
              )}

              {advancedFilters.departments.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  Departments: {advancedFilters.departments.length}
                </Badge>
              )}

              {advancedFilters.accessLevels.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  Access Levels: {advancedFilters.accessLevels.length}
                </Badge>
              )}

              {advancedFilters.statuses.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  Statuses: {advancedFilters.statuses.length}
                </Badge>
              )}

              {advancedFilters.authors.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  Authors: <AUTHORS>
                </Badge>
              )}

              {(advancedFilters.dateRange.from || advancedFilters.dateRange.to) && (
                <Badge variant="secondary" className="text-xs">
                  Date Range: {advancedFilters.dateRange.from ? 'From ' + advancedFilters.dateRange.from.toLocaleDateString() : ''}
                  {advancedFilters.dateRange.to ? ' To ' + advancedFilters.dateRange.to.toLocaleDateString() : ''}
                </Badge>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchFilters;
