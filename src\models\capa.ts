// CAPA (Corrective Action and Preventive Action) Types for FSSC 22000 v6.0, ISO 22000:2018, and ISO 9001 compliance

// Status types for CAPA
export type CAPAStatus =
  | 'open'
  | 'investigation'
  | 'action_planned'
  | 'implementing'
  | 'verification'
  | 'closed'
  | 'rejected';

// Source of the CAPA
export type CAPASource =
  | 'customer_claim'
  | 'internal_audit'
  | 'external_audit'
  | 'monitoring'
  | 'testing'
  | 'employee_suggestion'
  | 'management_review'
  | 'regulatory_inspection';

// Priority levels
export type CAPAPriority = 'critical' | 'high' | 'medium' | 'low';

// Categories for classification
export type CAPACategory =
  | 'product_quality'
  | 'food_safety'
  | 'packaging'
  | 'labeling'
  | 'documentation'
  | 'process_control'
  | 'allergen_management'
  | 'foreign_material'
  | 'other';

// Main CAPA interface
export interface CAPA {
  id: string;
  title: string;
  description: string;
  source: CAPASource;
  category: CAPACategory;
  priority: CAPAPriority;
  status: CAPAStatus;
  dateIdentified: Date;
  targetCompletionDate: Date;
  actualCompletionDate?: Date;
  responsiblePerson: string;
  productId?: string;
  claimId?: string;
  rootCauseAnalysisId?: string;
  correctiveActionIds: string[];
  preventiveActionIds: string[];
  verificationIds: string[];
  attachments?: string[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Customer claim interface
export interface Claim {
  id: string;
  customerName: string;
  contactInfo: string;
  productId: string;
  batchNumber: string;
  dateReceived: Date;
  description: string;
  severity: 'critical' | 'major' | 'minor';
  status: 'new' | 'under_investigation' | 'resolved' | 'closed' | 'rejected';
  responseDetails?: string;
  responseDate?: Date;
  capaId?: string;
  attachments?: string[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Root cause analysis interface
export interface RootCauseAnalysis {
  id: string;
  capaId: string;
  method: '5why' | 'fishbone' | 'fmea' | 'other';
  findings: string;
  rootCauses: string[];
  contributingFactors: string[];
  analysisDate: Date;
  conductedBy: string;
  reviewedBy?: string;
  reviewDate?: Date;
  attachments?: string[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Corrective action interface
export interface CorrectiveAction {
  id: string;
  capaId: string;
  description: string;
  targetDate: Date;
  completionDate?: Date;
  status: 'planned' | 'in_progress' | 'completed' | 'verified' | 'delayed';
  responsiblePerson: string;
  verificationMethod: string;
  verificationResults?: string;
  verifiedBy?: string;
  verificationDate?: Date;
  effectiveness: 'effective' | 'partially_effective' | 'not_effective' | 'not_verified';
  notes?: string;
  attachments?: string[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Preventive action interface (similar to corrective action but focused on prevention)
export interface PreventiveAction {
  id: string;
  capaId: string;
  description: string;
  targetDate: Date;
  completionDate?: Date;
  status: 'planned' | 'in_progress' | 'completed' | 'verified' | 'delayed';
  responsiblePerson: string;
  verificationMethod: string;
  verificationResults?: string;
  verifiedBy?: string;
  verificationDate?: Date;
  effectiveness: 'effective' | 'partially_effective' | 'not_effective' | 'not_verified';
  notes?: string;
  attachments?: string[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Verification activity interface
export interface VerificationActivity {
  id: string;
  capaId: string;
  description: string;
  date: Date;
  method: string;
  result: 'pass' | 'fail' | 'pending';
  findings: string;
  conductedBy: string;
  reviewedBy?: string;
  reviewDate?: Date;
  attachments?: string[];
  createdBy: string;
  createdAt: Date;
  updatedBy: string;
  updatedAt: Date;
}

// Mock data for claims
export const MOCK_CLAIMS: Claim[] = [
  {
    id: 'claim1',
    customerName: 'ABC Foods Inc.',
    contactInfo: '<EMAIL>',
    productId: 'product1',
    batchNumber: 'LOT2024-056',
    dateReceived: new Date('2024-05-10'),
    description: 'Foreign material (plastic fragment) found in product',
    severity: 'major',
    status: 'under_investigation',
    capaId: 'capa1',
    attachments: [
      'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1567306301408-9b74779a11af?w=400&h=300&fit=crop'
    ],
    createdBy: 'user1',
    createdAt: new Date('2024-05-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-10')
  },
  {
    id: 'claim2',
    customerName: 'Quick Mart',
    contactInfo: '<EMAIL>',
    productId: 'product2',
    batchNumber: 'LOT2024-042',
    dateReceived: new Date('2024-04-25'),
    description: 'Product packaging seal broken upon delivery',
    severity: 'minor',
    status: 'resolved',
    responseDetails: 'Replacement product sent to customer. Issue logged for CAPA.',
    responseDate: new Date('2024-04-27'),
    capaId: 'capa2',
    attachments: [
      'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=300&fit=crop'
    ],
    createdBy: 'user1',
    createdAt: new Date('2024-04-25'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-04-27')
  },
  {
    id: 'claim3',
    customerName: 'Health Foods Ltd',
    contactInfo: '<EMAIL>',
    productId: 'product3',
    batchNumber: 'LOT2024-039',
    dateReceived: new Date('2024-04-18'),
    description: 'Allergen (milk) not declared on label',
    severity: 'critical',
    status: 'closed',
    responseDetails: 'Product recall initiated. Root cause identified as label design change without allergen review.',
    responseDate: new Date('2024-04-19'),
    capaId: 'capa3',
    createdBy: 'user1',
    createdAt: new Date('2024-04-18'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-04-20')
  }
];

// Mock data for CAPAs
export const MOCK_CAPAS: CAPA[] = [
  {
    id: 'capa1',
    title: 'Foreign material contamination investigation',
    description: 'Investigation of plastic fragment found in product by customer ABC Foods',
    source: 'customer_claim',
    category: 'foreign_material',
    priority: 'high',
    status: 'investigation',
    dateIdentified: new Date('2024-05-10'),
    targetCompletionDate: new Date('2024-06-10'),
    responsiblePerson: 'Quality Manager',
    productId: 'product1',
    claimId: 'claim1',
    rootCauseAnalysisId: 'rca1',
    correctiveActionIds: ['ca1'],
    preventiveActionIds: ['pa1'],
    verificationIds: [],
    createdBy: 'user1',
    createdAt: new Date('2024-05-10'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-12')
  },
  {
    id: 'capa2',
    title: 'Packaging integrity improvement',
    description: 'Address issues with packaging seals breaking during distribution',
    source: 'customer_claim',
    category: 'packaging',
    priority: 'medium',
    status: 'action_planned',
    dateIdentified: new Date('2024-04-25'),
    targetCompletionDate: new Date('2024-05-25'),
    responsiblePerson: 'Production Manager',
    productId: 'product2',
    claimId: 'claim2',
    rootCauseAnalysisId: 'rca2',
    correctiveActionIds: ['ca2'],
    preventiveActionIds: ['pa2'],
    verificationIds: [],
    createdBy: 'user1',
    createdAt: new Date('2024-04-25'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-02')
  },
  {
    id: 'capa3',
    title: 'Allergen labeling process improvement',
    description: 'Corrective and preventive actions for allergen labeling failure',
    source: 'customer_claim',
    category: 'allergen_management',
    priority: 'critical',
    status: 'closed',
    dateIdentified: new Date('2024-04-18'),
    targetCompletionDate: new Date('2024-05-18'),
    actualCompletionDate: new Date('2024-05-15'),
    responsiblePerson: 'Food Safety Team Leader',
    productId: 'product3',
    claimId: 'claim3',
    rootCauseAnalysisId: 'rca3',
    correctiveActionIds: ['ca3', 'ca4'],
    preventiveActionIds: ['pa3', 'pa4'],
    verificationIds: ['v1'],
    createdBy: 'user1',
    createdAt: new Date('2024-04-18'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-15')
  }
];

// Mock data for root cause analyses
export const MOCK_ROOT_CAUSE_ANALYSES: RootCauseAnalysis[] = [
  {
    id: 'rca1',
    capaId: 'capa1',
    method: '5why',
    findings: 'Initial investigation suggests plastic fragment may have come from damaged conveyor belt component',
    rootCauses: ['Worn conveyor belt parts', 'Inadequate preventive maintenance schedule'],
    contributingFactors: ['High production volume', 'Delayed equipment replacement'],
    analysisDate: new Date('2024-05-15'),
    conductedBy: 'Quality Technician',
    createdBy: 'user1',
    createdAt: new Date('2024-05-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-15')
  },
  {
    id: 'rca2',
    capaId: 'capa2',
    method: 'fishbone',
    findings: 'Packaging seal integrity compromised due to heat sealer temperature variation and operator error',
    rootCauses: ['Inconsistent heat sealer temperature', 'Inadequate operator training'],
    contributingFactors: ['Equipment aging', 'High staff turnover', 'Rushed production schedule'],
    analysisDate: new Date('2024-04-30'),
    conductedBy: 'Production Supervisor',
    reviewedBy: 'Quality Manager',
    reviewDate: new Date('2024-05-01'),
    createdBy: 'user1',
    createdAt: new Date('2024-04-30'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-01')
  },
  {
    id: 'rca3',
    capaId: 'capa3',
    method: 'fishbone',
    findings: 'Allergen information omitted from label due to breakdown in label approval process',
    rootCauses: ['Label design change process bypassed allergen review', 'No verification step before printing'],
    contributingFactors: ['Tight deadline for packaging redesign', 'Communication gap between marketing and quality'],
    analysisDate: new Date('2024-04-20'),
    conductedBy: 'Food Safety Team Leader',
    reviewedBy: 'Quality Director',
    reviewDate: new Date('2024-04-21'),
    createdBy: 'user1',
    createdAt: new Date('2024-04-20'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-04-21')
  }
];

// Mock data for corrective actions
export const MOCK_CORRECTIVE_ACTIONS: CorrectiveAction[] = [
  {
    id: 'ca1',
    capaId: 'capa1',
    description: 'Replace damaged conveyor belt and inspect all similar equipment',
    targetDate: new Date('2024-05-25'),
    status: 'in_progress',
    responsiblePerson: 'Maintenance Manager',
    verificationMethod: 'Visual inspection and documentation of replacement',
    effectiveness: 'not_verified',
    createdBy: 'user1',
    createdAt: new Date('2024-05-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-15')
  },
  {
    id: 'ca2',
    capaId: 'capa2',
    description: 'Calibrate all heat sealers and implement daily temperature verification',
    targetDate: new Date('2024-05-10'),
    completionDate: new Date('2024-05-08'),
    status: 'completed',
    responsiblePerson: 'Production Supervisor',
    verificationMethod: 'Equipment calibration records and daily check logs',
    verificationResults: 'All heat sealers calibrated and daily check procedure implemented',
    verifiedBy: 'Quality Technician',
    verificationDate: new Date('2024-05-09'),
    effectiveness: 'effective',
    createdBy: 'user1',
    createdAt: new Date('2024-05-02'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-09')
  },
  {
    id: 'ca3',
    capaId: 'capa3',
    description: 'Recall affected product from distribution',
    targetDate: new Date('2024-04-25'),
    completionDate: new Date('2024-04-24'),
    status: 'completed',
    responsiblePerson: 'Logistics Manager',
    verificationMethod: 'Reconciliation of shipped vs. returned product',
    verificationResults: '98% of affected product recovered',
    verifiedBy: 'Quality Manager',
    verificationDate: new Date('2024-04-26'),
    effectiveness: 'effective',
    createdBy: 'user1',
    createdAt: new Date('2024-04-19'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-04-26')
  },
  {
    id: 'ca4',
    capaId: 'capa3',
    description: 'Implement mandatory allergen review step in label approval workflow',
    targetDate: new Date('2024-05-05'),
    completionDate: new Date('2024-05-03'),
    status: 'completed',
    responsiblePerson: 'Quality Manager',
    verificationMethod: 'Updated SOP and workflow in document control system',
    verificationResults: 'New procedure implemented and all relevant staff trained',
    verifiedBy: 'Food Safety Director',
    verificationDate: new Date('2024-05-05'),
    effectiveness: 'effective',
    createdBy: 'user1',
    createdAt: new Date('2024-04-22'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-05')
  }
];

// Mock data for preventive actions
export const MOCK_PREVENTIVE_ACTIONS: PreventiveAction[] = [
  {
    id: 'pa1',
    capaId: 'capa1',
    description: 'Implement enhanced preventive maintenance schedule for all conveyor systems',
    targetDate: new Date('2024-06-05'),
    status: 'planned',
    responsiblePerson: 'Maintenance Manager',
    verificationMethod: 'Review of updated maintenance schedule and first cycle completion',
    effectiveness: 'not_verified',
    createdBy: 'user1',
    createdAt: new Date('2024-05-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-15')
  },
  {
    id: 'pa2',
    capaId: 'capa2',
    description: 'Develop and implement packaging integrity training program for all production staff',
    targetDate: new Date('2024-05-20'),
    status: 'in_progress',
    responsiblePerson: 'Training Coordinator',
    verificationMethod: 'Training records and post-training assessment',
    effectiveness: 'not_verified',
    createdBy: 'user1',
    createdAt: new Date('2024-05-02'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-10')
  },
  {
    id: 'pa3',
    capaId: 'capa3',
    description: 'Implement digital label management system with mandatory allergen verification',
    targetDate: new Date('2024-05-15'),
    completionDate: new Date('2024-05-12'),
    status: 'completed',
    responsiblePerson: 'IT Manager',
    verificationMethod: 'System validation and user acceptance testing',
    verificationResults: 'System implemented and validated',
    verifiedBy: 'Quality Manager',
    verificationDate: new Date('2024-05-14'),
    effectiveness: 'effective',
    createdBy: 'user1',
    createdAt: new Date('2024-04-22'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-14')
  },
  {
    id: 'pa4',
    capaId: 'capa3',
    description: 'Conduct refresher training on allergen management for all relevant staff',
    targetDate: new Date('2024-05-10'),
    completionDate: new Date('2024-05-08'),
    status: 'completed',
    responsiblePerson: 'Food Safety Team Leader',
    verificationMethod: 'Training attendance records and assessment scores',
    verificationResults: 'All staff trained with 95% pass rate on assessment',
    verifiedBy: 'HR Manager',
    verificationDate: new Date('2024-05-10'),
    effectiveness: 'effective',
    createdBy: 'user1',
    createdAt: new Date('2024-04-22'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-10')
  }
];

// Mock data for verification activities
export const MOCK_VERIFICATION_ACTIVITIES: VerificationActivity[] = [
  {
    id: 'v1',
    capaId: 'capa3',
    description: 'Verification of allergen labeling process improvements',
    date: new Date('2024-05-15'),
    method: 'Process audit and label review',
    result: 'pass',
    findings: 'New allergen review process effectively implemented. Sample of 20 recent labels all correctly included allergen information.',
    conductedBy: 'Quality Auditor',
    reviewedBy: 'Food Safety Director',
    reviewDate: new Date('2024-05-16'),
    createdBy: 'user1',
    createdAt: new Date('2024-05-15'),
    updatedBy: 'user1',
    updatedAt: new Date('2024-05-16')
  }
];
