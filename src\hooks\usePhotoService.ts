import { useState, useCallback, useEffect } from 'react';
import { PhotoFile } from '@/components/ui/photo-upload';
import { toast } from '@/hooks/use-toast';
import * as photoService from '@/services/photoStorageService';

export interface UsePhotoServiceOptions {
  claimId?: string;
  maxFiles?: number;
  autoCleanup?: boolean;
  onUploadComplete?: (photos: photoService.StoredPhoto[]) => void;
  onUploadError?: (error: Error) => void;
}

export interface UsePhotoServiceReturn {
  photos: PhotoFile[];
  storedPhotos: photoService.StoredPhoto[];
  isUploading: boolean;
  uploadProgress: Record<string, number>;
  addPhotos: (files: FileList) => Promise<void>;
  removePhoto: (photoId: string) => Promise<void>;
  clearAllPhotos: () => void;
  uploadAllPhotos: () => Promise<void>;
  validateFile: (file: File) => photoService.PhotoValidationResult;
  getPhotoCount: () => number;
  hasPhotos: () => boolean;
  refreshStoredPhotos: () => Promise<void>;
}

export const usePhotoService = (options: UsePhotoServiceOptions = {}): UsePhotoServiceReturn => {
  const {
    claimId,
    maxFiles = 10,
    autoCleanup = true,
    onUploadComplete,
    onUploadError
  } = options;

  const [photos, setPhotos] = useState<PhotoFile[]>([]);
  const [storedPhotos, setStoredPhotos] = useState<photoService.StoredPhoto[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  // Load existing photos for the claim
  const refreshStoredPhotos = useCallback(async () => {
    if (!claimId) return;
    
    try {
      const existingPhotos = photoService.getPhotosForClaim(claimId);
      setStoredPhotos(existingPhotos);
    } catch (error) {
      console.error('Failed to load stored photos:', error);
      toast({
        title: 'Error',
        description: 'Failed to load existing photos',
        variant: 'destructive'
      });
    }
  }, [claimId]);

  // Load stored photos on mount and when claimId changes
  useEffect(() => {
    refreshStoredPhotos();
  }, [refreshStoredPhotos]);

  // Cleanup blob URLs on unmount
  useEffect(() => {
    if (!autoCleanup) return;

    return () => {
      photos.forEach(photo => {
        if (photo.url.startsWith('blob:')) {
          photoService.cleanupPhotoUrl(photo.url);
        }
      });
    };
  }, [photos, autoCleanup]);

  const validateFile = useCallback((file: File) => {
    return photoService.validatePhotoFile(file);
  }, []);

  const addPhotos = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    
    // Check total file limit
    if (photos.length + fileArray.length > maxFiles) {
      toast({
        title: 'Too many files',
        description: `You can only upload up to ${maxFiles} photos.`,
        variant: 'destructive'
      });
      return;
    }

    const newPhotos: PhotoFile[] = [];
    const validationErrors: string[] = [];

    for (const file of fileArray) {
      const validation = validateFile(file);
      
      if (!validation.isValid) {
        validationErrors.push(`${file.name}: ${validation.error?.message}`);
        continue;
      }

      // Show warnings if any
      if (validation.warnings) {
        validation.warnings.forEach(warning => {
          toast({
            title: 'Warning',
            description: `${file.name}: ${warning}`,
            variant: 'default'
          });
        });
      }

      const photoId = photoService.generatePhotoId();
      const url = URL.createObjectURL(file);

      const photoFile: PhotoFile = {
        id: photoId,
        file,
        url,
        name: file.name,
        size: file.size,
        uploading: false,
        uploadProgress: 0
      };

      newPhotos.push(photoFile);
    }

    // Show validation errors
    if (validationErrors.length > 0) {
      toast({
        title: 'Invalid files',
        description: validationErrors.join('\n'),
        variant: 'destructive'
      });
    }

    if (newPhotos.length > 0) {
      setPhotos(prev => [...prev, ...newPhotos]);
      
      toast({
        title: 'Photos added',
        description: `${newPhotos.length} photo(s) ready for upload`,
      });
    }
  }, [photos.length, maxFiles, validateFile]);

  const removePhoto = useCallback(async (photoId: string) => {
    // Remove from local photos
    const photoToRemove = photos.find(p => p.id === photoId);
    if (photoToRemove) {
      photoService.cleanupPhotoUrl(photoToRemove.url);
      setPhotos(prev => prev.filter(p => p.id !== photoId));
    }

    // Remove from stored photos if it exists
    try {
      const success = await photoService.deletePhoto(photoId);
      if (success) {
        setStoredPhotos(prev => prev.filter(p => p.id !== photoId));
        toast({
          title: 'Photo removed',
          description: 'Photo has been deleted successfully',
        });
      }
    } catch (error) {
      console.error('Failed to delete photo:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete photo',
        variant: 'destructive'
      });
    }
  }, [photos]);

  const clearAllPhotos = useCallback(() => {
    // Cleanup blob URLs
    photos.forEach(photo => {
      photoService.cleanupPhotoUrl(photo.url);
    });
    
    setPhotos([]);
    setUploadProgress({});
    
    toast({
      title: 'Photos cleared',
      description: 'All pending photos have been removed',
    });
  }, [photos]);

  const uploadAllPhotos = useCallback(async () => {
    if (photos.length === 0) return;

    setIsUploading(true);
    
    try {
      const photosToUpload = photos.filter(photo => !photo.uploading && photo.uploadProgress !== 100);
      
      if (photosToUpload.length === 0) {
        toast({
          title: 'No photos to upload',
          description: 'All photos are already uploaded',
        });
        return;
      }

      // Mark photos as uploading
      setPhotos(prev => prev.map(photo => 
        photosToUpload.some(p => p.id === photo.id) 
          ? { ...photo, uploading: true, uploadProgress: 0 }
          : photo
      ));

      const uploadedPhotos = await photoService.uploadPhotos(
        photosToUpload,
        claimId,
        (photoId, progress) => {
          setUploadProgress(prev => ({ ...prev, [photoId]: progress }));
          setPhotos(prev => prev.map(p => 
            p.id === photoId 
              ? { ...p, uploadProgress: progress, uploading: progress < 100 }
              : p
          ));
        }
      );

      // Refresh stored photos
      await refreshStoredPhotos();

      onUploadComplete?.(uploadedPhotos);

      toast({
        title: 'Upload complete',
        description: `${uploadedPhotos.length} photo(s) uploaded successfully`,
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      onUploadError?.(error instanceof Error ? error : new Error(errorMessage));
      
      toast({
        title: 'Upload failed',
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
    }
  }, [photos, claimId, onUploadComplete, onUploadError, refreshStoredPhotos]);

  const getPhotoCount = useCallback(() => {
    return photos.length + storedPhotos.length;
  }, [photos.length, storedPhotos.length]);

  const hasPhotos = useCallback(() => {
    return getPhotoCount() > 0;
  }, [getPhotoCount]);

  return {
    photos,
    storedPhotos,
    isUploading,
    uploadProgress,
    addPhotos,
    removePhoto,
    clearAllPhotos,
    uploadAllPhotos,
    validateFile,
    getPhotoCount,
    hasPhotos,
    refreshStoredPhotos
  };
};
