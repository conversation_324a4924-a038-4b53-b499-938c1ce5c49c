import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CAPA, Claim, CorrectiveAction, PreventiveAction, VerificationActivity } from '@/models/capa';
import { 
  Alert<PERSON>ircle, 
  CheckCircle2, 
  Clock, 
  FileSearch, 
  FileWarning, 
  ShieldAlert, 
  ThumbsUp, 
  Wrench 
} from 'lucide-react';
import { Progress } from '@/components/ui/progress';

interface CAPADashboardProps {
  capas: CAPA[];
  claims: Claim[];
  correctiveActions: CorrectiveAction[];
  preventiveActions: PreventiveAction[];
  verificationActivities: VerificationActivity[];
}

export function CAPADashboard({
  capas,
  claims,
  correctiveActions,
  preventiveActions,
  verificationActivities
}: CAPADashboardProps) {
  // Calculate summary statistics
  const totalCAPAs = capas.length;
  const openCAPAs = capas.filter(capa => capa.status !== 'closed' && capa.status !== 'rejected').length;
  const closedCAPAs = capas.filter(capa => capa.status === 'closed').length;
  const capaCompletionRate = totalCAPAs > 0 ? Math.round((closedCAPAs / totalCAPAs) * 100) : 0;
  
  const totalClaims = claims.length;
  const openClaims = claims.filter(claim => claim.status !== 'closed' && claim.status !== 'rejected').length;
  
  const totalActions = correctiveActions.length + preventiveActions.length;
  const completedActions = 
    correctiveActions.filter(action => action.status === 'completed' || action.status === 'verified').length +
    preventiveActions.filter(action => action.status === 'completed' || action.status === 'verified').length;
  const actionCompletionRate = totalActions > 0 ? Math.round((completedActions / totalActions) * 100) : 0;
  
  // Calculate CAPA by priority
  const capasByPriority = {
    critical: capas.filter(capa => capa.priority === 'critical').length,
    high: capas.filter(capa => capa.priority === 'high').length,
    medium: capas.filter(capa => capa.priority === 'medium').length,
    low: capas.filter(capa => capa.priority === 'low').length,
  };
  
  // Calculate CAPA by category
  const capasByCategory = {
    product_quality: capas.filter(capa => capa.category === 'product_quality').length,
    food_safety: capas.filter(capa => capa.category === 'food_safety').length,
    packaging: capas.filter(capa => capa.category === 'packaging').length,
    labeling: capas.filter(capa => capa.category === 'labeling').length,
    allergen_management: capas.filter(capa => capa.category === 'allergen_management').length,
    foreign_material: capas.filter(capa => capa.category === 'foreign_material').length,
    documentation: capas.filter(capa => capa.category === 'documentation').length,
    process_control: capas.filter(capa => capa.category === 'process_control').length,
    other: capas.filter(capa => capa.category === 'other').length,
  };
  
  // Calculate CAPA by status
  const capasByStatus = {
    open: capas.filter(capa => capa.status === 'open').length,
    investigation: capas.filter(capa => capa.status === 'investigation').length,
    action_planned: capas.filter(capa => capa.status === 'action_planned').length,
    implementing: capas.filter(capa => capa.status === 'implementing').length,
    verification: capas.filter(capa => capa.status === 'verification').length,
    closed: capas.filter(capa => capa.status === 'closed').length,
    rejected: capas.filter(capa => capa.status === 'rejected').length,
  };
  
  return (
    <div className="space-y-6">
      <h2 className="text-xl font-semibold">CAPA Dashboard</h2>
      
      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Total CAPAs</CardTitle>
            <FileSearch className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalCAPAs}</div>
            <p className="text-xs text-muted-foreground">
              {openCAPAs} open, {closedCAPAs} closed
            </p>
            <div className="mt-3">
              <Progress value={capaCompletionRate} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {capaCompletionRate}% completion rate
              </p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Customer Claims</CardTitle>
            <FileWarning className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalClaims}</div>
            <p className="text-xs text-muted-foreground">
              {openClaims} open claims requiring action
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Actions</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalActions}</div>
            <p className="text-xs text-muted-foreground">
              {completedActions} completed, {totalActions - completedActions} pending
            </p>
            <div className="mt-3">
              <Progress value={actionCompletionRate} className="h-2" />
              <p className="text-xs text-muted-foreground mt-1">
                {actionCompletionRate}% completion rate
              </p>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
            <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{capasByPriority.critical}</div>
            <p className="text-xs text-muted-foreground">
              Critical priority CAPAs requiring immediate attention
            </p>
          </CardContent>
        </Card>
      </div>
      
      {/* CAPA by Priority */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>CAPA by Priority</CardTitle>
            <CardDescription>Distribution of CAPAs by priority level</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-red-500 mr-2"></div>
                    <span className="text-sm">Critical</span>
                  </div>
                  <span className="text-sm font-medium">{capasByPriority.critical}</span>
                </div>
                <Progress value={(capasByPriority.critical / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-red-500" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
                    <span className="text-sm">High</span>
                  </div>
                  <span className="text-sm font-medium">{capasByPriority.high}</span>
                </div>
                <Progress value={(capasByPriority.high / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-orange-500" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                    <span className="text-sm">Medium</span>
                  </div>
                  <span className="text-sm font-medium">{capasByPriority.medium}</span>
                </div>
                <Progress value={(capasByPriority.medium / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-yellow-500" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                    <span className="text-sm">Low</span>
                  </div>
                  <span className="text-sm font-medium">{capasByPriority.low}</span>
                </div>
                <Progress value={(capasByPriority.low / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* CAPA by Status */}
        <Card>
          <CardHeader>
            <CardTitle>CAPA by Status</CardTitle>
            <CardDescription>Current status of all CAPAs</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-blue-500 mr-2" />
                    <span className="text-sm">Open</span>
                  </div>
                  <span className="text-sm font-medium">{capasByStatus.open}</span>
                </div>
                <Progress value={(capasByStatus.open / totalCAPAs) * 100} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <FileSearch className="h-4 w-4 text-purple-500 mr-2" />
                    <span className="text-sm">Investigation</span>
                  </div>
                  <span className="text-sm font-medium">{capasByStatus.investigation}</span>
                </div>
                <Progress value={(capasByStatus.investigation / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-purple-500" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Wrench className="h-4 w-4 text-yellow-500 mr-2" />
                    <span className="text-sm">Action Planned</span>
                  </div>
                  <span className="text-sm font-medium">{capasByStatus.action_planned}</span>
                </div>
                <Progress value={(capasByStatus.action_planned / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-yellow-500" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <ShieldAlert className="h-4 w-4 text-orange-500 mr-2" />
                    <span className="text-sm">Implementing</span>
                  </div>
                  <span className="text-sm font-medium">{capasByStatus.implementing}</span>
                </div>
                <Progress value={(capasByStatus.implementing / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-orange-500" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <ThumbsUp className="h-4 w-4 text-green-500 mr-2" />
                    <span className="text-sm">Verification</span>
                  </div>
                  <span className="text-sm font-medium">{capasByStatus.verification}</span>
                </div>
                <Progress value={(capasByStatus.verification / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-green-500" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <CheckCircle2 className="h-4 w-4 text-green-700 mr-2" />
                    <span className="text-sm">Closed</span>
                  </div>
                  <span className="text-sm font-medium">{capasByStatus.closed}</span>
                </div>
                <Progress value={(capasByStatus.closed / totalCAPAs) * 100} className="h-2 bg-muted" indicatorClassName="bg-green-700" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* CAPA by Category */}
        <Card>
          <CardHeader>
            <CardTitle>CAPA by Category</CardTitle>
            <CardDescription>Types of issues being addressed</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(capasByCategory)
                .filter(([_, count]) => count > 0)
                .sort(([_, countA], [__, countB]) => countB - countA)
                .map(([category, count]) => (
                  <div key={category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm capitalize">{category.replace('_', ' ')}</span>
                      <span className="text-sm font-medium">{count}</span>
                    </div>
                    <Progress value={(count / totalCAPAs) * 100} className="h-2" />
                  </div>
                ))
              }
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
