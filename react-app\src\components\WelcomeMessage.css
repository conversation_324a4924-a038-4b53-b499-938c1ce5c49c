.welcome-message {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--box-shadow-md);
  animation: slideDown 0.5s ease-out;
}

.welcome-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
}

.welcome-content i {
  font-size: 1.5rem;
  margin-top: var(--spacing-xs);
  opacity: 0.9;
}

.welcome-content h3 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: 1.125rem;
  font-weight: 600;
}

.welcome-content p {
  margin: 0 0 var(--spacing-md) 0;
  opacity: 0.9;
  line-height: 1.5;
}

.feature-list {
  margin: 0 0 var(--spacing-md) 0;
  padding-left: var(--spacing-lg);
  opacity: 0.9;
}

.feature-list li {
  margin-bottom: var(--spacing-sm);
  line-height: 1.4;
}

.feature-list strong {
  color: var(--white);
  opacity: 1;
}

.btn-link {
  background: none;
  border: none;
  color: var(--white);
  text-decoration: underline;
  cursor: pointer;
  font-size: 0.875rem;
  padding: 0;
  opacity: 0.8;
  transition: var(--transition);
}

.btn-link:hover {
  opacity: 1;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
