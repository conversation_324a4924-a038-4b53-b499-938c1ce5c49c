import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from '@/components/ui/tabs';
import {
  CAPA as CAPAType,
  Claim,
  RootCauseAnalysis,
  CorrectiveAction,
  PreventiveAction,
  VerificationActivity,
  MOCK_CAPAS,
  MOCK_CLAIMS,
  MOCK_ROOT_CAUSE_ANALYSES,
  MOCK_CORRECTIVE_ACTIONS,
  MOCK_PREVENTIVE_ACTIONS,
  MOCK_VERIFICATION_ACTIVITIES
} from '@/models/capa';
import { CAPADashboard } from '../components/capa/CAPADashboard';
import { ClaimsManagement } from '../components/capa/ClaimsManagement';
import { CAPAManagement } from '../components/capa/CAPAManagement';
import RootCauseAnalysisComponent from '../components/capa/RootCauseAnalysis';
import { CorrectiveActionsManagement } from '../components/capa/CorrectiveActionsManagement';
import { PreventiveActionsManagement } from '../components/capa/PreventiveActionsManagement';
import { VerificationActivitiesManagement } from '../components/capa/VerificationActivitiesManagement';
import { ReportingAnalytics } from '../components/capa/ReportingAnalytics';
import { toast } from '@/hooks/use-toast';
import { useLocalStorage } from '@/hooks/use-local-storage';

export function CAPA() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCapaId, setSelectedCapaId] = useState<string | null>(null);

  // State for all CAPA-related data using localStorage for persistence
  const [capas, setCapas] = useLocalStorage<CAPAType[]>('haccp_capas', MOCK_CAPAS);
  const [claims, setClaims] = useLocalStorage<Claim[]>('haccp_claims', MOCK_CLAIMS);
  const [rootCauseAnalyses, setRootCauseAnalyses] = useLocalStorage<RootCauseAnalysis[]>('haccp_root_cause_analyses', MOCK_ROOT_CAUSE_ANALYSES);
  const [correctiveActions, setCorrectiveActions] = useLocalStorage<CorrectiveAction[]>('haccp_corrective_actions', MOCK_CORRECTIVE_ACTIONS);
  const [preventiveActions, setPreventiveActions] = useLocalStorage<PreventiveAction[]>('haccp_preventive_actions', MOCK_PREVENTIVE_ACTIONS);
  const [verificationActivities, setVerificationActivities] = useLocalStorage<VerificationActivity[]>('haccp_verification_activities', MOCK_VERIFICATION_ACTIVITIES);

  // Products state (mock data for now)
  const [products, setProducts] = useLocalStorage('haccp_products', [
    { id: 'product1', name: 'Chicken Soup', description: 'Classic chicken soup with vegetables' },
    { id: 'product2', name: 'Beef Stew', description: 'Hearty beef stew with potatoes and carrots' },
    { id: 'product3', name: 'Vegetable Curry', description: 'Spicy vegetable curry with coconut milk' }
  ]);

  // Event listener for tab navigation from CAPA details view
  useEffect(() => {
    const handleNavigateTab = (event: Event) => {
      const customEvent = event as CustomEvent<{ tab: string; capaId: string }>;
      const { tab, capaId } = customEvent.detail;

      // Set the active tab
      setActiveTab(tab);
      setSelectedCapaId(capaId);

      // Show a toast notification
      toast({
        title: "Navigating to " + tab.replace('-', ' '),
        description: `Viewing related items for CAPA ${capaId}`
      });
    };

    window.addEventListener('navigate-tab', handleNavigateTab as EventListener);

    return () => {
      window.removeEventListener('navigate-tab', handleNavigateTab as EventListener);
    };
  }, []);

  // Navigation function to switch to CAPA tab and highlight specific CAPA
  const handleNavigateToCAPA = (capaId: string) => {
    setActiveTab('capa');
    setSelectedCapaId(capaId);

    toast({
      title: "Navigating to CAPA",
      description: `Viewing CAPA ${capaId} details`
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">CAPA Management</h1>
      </div>

      <Tabs defaultValue="dashboard" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid grid-cols-2 md:grid-cols-8 gap-2">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="claims">Claims</TabsTrigger>
          <TabsTrigger value="capa">CAPA</TabsTrigger>
          <TabsTrigger value="root-cause">Root Cause</TabsTrigger>
          <TabsTrigger value="corrective-actions">Corrective</TabsTrigger>
          <TabsTrigger value="preventive-actions">Preventive</TabsTrigger>
          <TabsTrigger value="verification">Verification</TabsTrigger>
          <TabsTrigger value="reporting">Reporting</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <CAPADashboard
            capas={capas}
            claims={claims}
            correctiveActions={correctiveActions}
            preventiveActions={preventiveActions}
            verificationActivities={verificationActivities}
          />
        </TabsContent>

        <TabsContent value="claims" className="space-y-4">
          <ClaimsManagement
            claims={claims}
            setClaims={setClaims}
            products={products}
            capas={capas}
            setCapas={setCapas}
            onNavigateToCAPA={handleNavigateToCAPA}
          />
        </TabsContent>

        <TabsContent value="capa" className="space-y-4">
          <CAPAManagement
            capas={capas}
            setCapas={setCapas}
            claims={claims}
            products={products}
          />
        </TabsContent>

        <TabsContent value="root-cause" className="space-y-4">
          <RootCauseAnalysisComponent />
        </TabsContent>

        <TabsContent value="corrective-actions" className="space-y-4">
          <CorrectiveActionsManagement
            correctiveActions={correctiveActions}
            setCorrectiveActions={setCorrectiveActions}
            capas={capas}
          />
        </TabsContent>

        <TabsContent value="preventive-actions" className="space-y-4">
          <PreventiveActionsManagement
            preventiveActions={preventiveActions}
            setPreventiveActions={setPreventiveActions}
            capas={capas}
          />
        </TabsContent>

        <TabsContent value="verification" className="space-y-4">
          <VerificationActivitiesManagement
            verificationActivities={verificationActivities}
            setVerificationActivities={setVerificationActivities}
            capas={capas}
            correctiveActions={correctiveActions}
            preventiveActions={preventiveActions}
          />
        </TabsContent>

        <TabsContent value="reporting" className="space-y-4">
          <ReportingAnalytics
            capas={capas}
            claims={claims}
            correctiveActions={correctiveActions}
            preventiveActions={preventiveActions}
            verificationActivities={verificationActivities}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default CAPA;
