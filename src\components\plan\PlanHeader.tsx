
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { FileText, FileSpreadsheet, Printer } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogDescription, DialogClose } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

interface PlanHeaderProps {
  handleExportPDF: () => void;
}

export const PlanHeader: React.FC<PlanHeaderProps> = ({ handleExportPDF }) => {
  const [selectedSections, setSelectedSections] = useState({
    productDescription: true,
    processFlow: true,
    hazardAnalysis: true,
    criticalPoints: true,
    verificationProcedures: true,
    approvals: true
  });

  const handleCheckboxChange = (section: keyof typeof selectedSections) => {
    setSelectedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Direct export without options
  const handleDirectExport = () => {
    handleExportPDF();
  };

  return (
    <div className="flex justify-between items-center">
      <h1 className="text-2xl font-bold tracking-tight">HACCP Plan Generator</h1>
      <div className="flex gap-2">
        <Button variant="outline" size="sm" onClick={handleDirectExport}>
          <FileText className="h-4 w-4 mr-2" />
          Export PDF
        </Button>
        <Button variant="outline" size="sm">
          <FileSpreadsheet className="h-4 w-4 mr-2" />
          Export Excel
        </Button>
        <Button variant="outline" size="sm">
          <Printer className="h-4 w-4 mr-2" />
          Print
        </Button>
      </div>
    </div>
  );
};
