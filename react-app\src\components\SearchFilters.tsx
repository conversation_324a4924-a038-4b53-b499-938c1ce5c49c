import React, { useState, useEffect } from 'react';
import { useDocuments } from '../context/DocumentContext';
import { DocumentType, DocumentStatus } from '../types';
import './SearchFilters.css';

const SearchFilters: React.FC = () => {
  const { searchFilters, setSearchFilters } = useDocuments();
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleSearchChange = (value: string) => {
    // Clear previous timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout for debounced search
    const timeout = setTimeout(() => {
      setSearchFilters({ searchTerm: value });
    }, 300);

    setSearchTimeout(timeout);
  };

  const handleTypeFilterChange = (value: DocumentType | '') => {
    setSearchFilters({ typeFilter: value });
  };

  const handleStatusFilterChange = (value: DocumentStatus | '') => {
    setSearchFilters({ statusFilter: value });
  };

  const clearSearch = () => {
    setSearchFilters({ searchTerm: '' });
    // Clear the input field
    const searchInput = document.getElementById('searchInput') as HTMLInputElement;
    if (searchInput) {
      searchInput.value = '';
    }
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  const documentTypes: DocumentType[] = ['Processus', 'Procédure', 'Enregistrement', 'Manuel', 'Logiciel'];
  const documentStatuses: DocumentStatus[] = ['RAS', 'Modifier', 'Créer', 'Annuler'];

  return (
    <section className="section search-section">
      <div className="section-header">
        <h2>
          <i className="fas fa-search"></i>
          Find Documents
        </h2>
        <p>Search and filter your documents</p>
      </div>

      <div className="search-controls">
        <div className="search-group">
          <div className="search-input-container">
            <i className="fas fa-search search-icon"></i>
            <input
              type="text"
              id="searchInput"
              placeholder="Search by designation or reference ID..."
              onChange={(e) => handleSearchChange(e.target.value)}
              aria-label="Search documents"
            />
            <button
              type="button"
              onClick={clearSearch}
              className={`clear-search ${searchFilters.searchTerm ? 'show' : ''}`}
              title="Clear search"
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
        </div>

        <div className="filter-group">
          <select
            value={searchFilters.typeFilter}
            onChange={(e) => handleTypeFilterChange(e.target.value as DocumentType | '')}
            aria-label="Filter by document type"
          >
            <option value="">All Types</option>
            {documentTypes.map(type => (
              <option key={type} value={type}>{type}</option>
            ))}
          </select>

          <select
            value={searchFilters.statusFilter}
            onChange={(e) => handleStatusFilterChange(e.target.value as DocumentStatus | '')}
            aria-label="Filter by status"
          >
            <option value="">All Statuses</option>
            {documentStatuses.map(status => (
              <option key={status} value={status}>{status}</option>
            ))}
          </select>
        </div>
      </div>
    </section>
  );
};

export default SearchFilters;
