import { render, screen } from '@testing-library/react';
import App from './App';

test('renders document control system header', () => {
  render(<App />);
  const headerElement = screen.getByText(/Document Control System/i);
  expect(headerElement).toBeInTheDocument();
});

test('renders add new document section', () => {
  render(<App />);
  const addDocumentElement = screen.getByText(/Add New Document/i);
  expect(addDocumentElement).toBeInTheDocument();
});
