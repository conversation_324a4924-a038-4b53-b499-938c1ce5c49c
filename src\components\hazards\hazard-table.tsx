
import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Hazard } from '@/models/types';
import { calculateRisk, getRiskColorClass } from '@/utils/riskCalculation';
import { Edit, Trash2, CheckCircle, AlertCircle } from 'lucide-react';

interface HazardTableProps {
  hazards: Hazard[];
  onEdit?: (hazard: Hazard) => void;
  onDelete?: (hazardId: string) => void;
  onViewCCP?: (hazard: Hazard) => void;
  readonly?: boolean;
}

export function HazardTable({ hazards, onEdit, onDelete, onViewCCP, readonly = false }: HazardTableProps) {
  if (!hazards.length) {
    return (
      <div className="p-6 text-center bg-muted/30 rounded-lg border border-dashed">
        <p className="text-muted-foreground">No hazards have been identified yet.</p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Type</TableHead>
            <TableHead className="w-full">Description</TableHead>
            <TableHead>Severity</TableHead>
            <TableHead>Likelihood</TableHead>
            <TableHead>Risk Level</TableHead>
            <TableHead>CCP</TableHead>
            {!readonly && <TableHead className="text-right">Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {hazards.map((hazard) => {
            const { score, level } = calculateRisk(hazard.severity, hazard.likelihood);
            const riskColorClass = getRiskColorClass(level);
            
            return (
              <TableRow key={hazard.id}>
                <TableCell className="font-medium">
                  <span className={`inline-block px-2 py-1 rounded-full text-xs ${
                    hazard.type === 'Biological' ? 'bg-green-100 text-green-800' :
                    hazard.type === 'Chemical' ? 'bg-purple-100 text-purple-800' :
                    'bg-orange-100 text-orange-800'
                  }`}>
                    {hazard.type}
                  </span>
                </TableCell>
                <TableCell>{hazard.description}</TableCell>
                <TableCell className="text-center">{hazard.severity}</TableCell>
                <TableCell className="text-center">{hazard.likelihood}</TableCell>
                <TableCell>
                  <span className={`inline-block px-2 py-1 rounded text-xs ${riskColorClass}`}>
                    {score} ({level})
                  </span>
                </TableCell>
                <TableCell>
                  {hazard.isCCP ? (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="text-yellow-600 hover:text-yellow-700"
                      onClick={() => onViewCCP && onViewCCP(hazard)}
                    >
                      <CheckCircle className="h-5 w-5 mr-1" />
                      CCP
                    </Button>
                  ) : (
                    <span className="text-gray-500 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      No
                    </span>
                  )}
                </TableCell>
                {!readonly && (
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      {onEdit && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => onEdit(hazard)}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                      )}
                      {onDelete && (
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="text-destructive"
                          onClick={() => onDelete(hazard.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      )}
                    </div>
                  </TableCell>
                )}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
}

export default HazardTable;
