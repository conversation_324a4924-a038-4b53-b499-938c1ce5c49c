import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react";
import { CAPA, CorrectiveAction } from '@/models/capa';

interface AddCorrectiveActionDialogProps {
  open: boolean;
  onClose: () => void;
  capas: CAPA[];
  onAdd: (newAction: CorrectiveAction) => void;
}

export function AddCorrectiveActionDialog({ open, onClose, capas, onAdd }: AddCorrectiveActionDialogProps) {
  const [capaId, setCapaId] = useState('');
  const [description, setDescription] = useState('');
  const [targetDate, setTargetDate] = useState<Date | undefined>(undefined);
  const [responsiblePerson, setResponsiblePerson] = useState('');
  const [verificationMethod, setVerificationMethod] = useState('');
  const [status, setStatus] = useState('planned');
  const [notes, setNotes] = useState('');

  const handleSubmit = () => {
    // Validate form fields
    if (!capaId || !description || !targetDate || !responsiblePerson || !verificationMethod) {
      alert('Please fill in all required fields.');
      return;
    }

    // Create new corrective action object
    const newAction: CorrectiveAction = {
      id: Math.random().toString(36).substring(7), // Generate a random ID
      capaId,
      description,
      targetDate,
      responsiblePerson,
      verificationMethod,
      status: status as 'planned' | 'in_progress' | 'completed' | 'verified' | 'delayed',
      notes,
      effectiveness: 'not_verified',
      createdBy: 'user1',
      createdAt: new Date(),
      updatedBy: 'user1',
      updatedAt: new Date(),
    };

    onAdd(newAction);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Corrective Action</DialogTitle>
          <DialogDescription>
            Add a new corrective action to a CAPA.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="capa" className="text-right">
              CAPA
            </Label>
            <select
              id="capa"
              value={capaId}
              onChange={(e) => setCapaId(e.target.value)}
              className="col-span-3 rounded-md border border-gray-200 px-2 py-1 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="">Select CAPA</option>
              {capas.map((capa) => (
                <option key={capa.id} value={capa.id}>{capa.title}</option>
              ))}
            </select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="description" className="text-right">
              Description
            </Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="targetDate" className="text-right">
              Target Date
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "col-span-3 pl-3 text-left font-normal",
                    !targetDate && "text-muted-foreground"
                  )}
                >
                  {targetDate ? format(targetDate, "PPP") : (
                    <span>Pick a date</span>
                  )}
                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={targetDate}
                  onSelect={setTargetDate}
                  disabled={(date) =>
                    date < new Date()
                  }
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="responsiblePerson" className="text-right">
              Responsible Person
            </Label>
            <Input
              type="text"
              id="responsiblePerson"
              value={responsiblePerson}
              onChange={(e) => setResponsiblePerson(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="verificationMethod" className="text-right">
              Verification Method
            </Label>
            <Textarea
              id="verificationMethod"
              value={verificationMethod}
              onChange={(e) => setVerificationMethod(e.target.value)}
              className="col-span-3"
            />
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="status" className="text-right">
              Status
            </Label>
            <select
              id="status"
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="col-span-3 rounded-md border border-gray-200 px-2 py-1 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            >
              <option value="planned">Planned</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="verified">Verified</option>
              <option value="delayed">Delayed</option>
            </select>
          </div>
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="notes" className="text-right">
              Notes/Comments
            </Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="col-span-3"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" onClick={handleSubmit}>Add Action</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
