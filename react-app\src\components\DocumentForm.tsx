import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { useDocuments } from '../context/DocumentContext';
import { DocumentFormData, DocumentType, DocumentFormat, DocumentStatus } from '../types';
import FileUpload from './FileUpload';
import './DocumentForm.css';

const DocumentForm: React.FC = () => {
  const { 
    addDocument, 
    updateDocument, 
    currentEditId, 
    setCurrentEditId, 
    documents,
    setLoading 
  } = useDocuments();

  const [currentFile, setCurrentFile] = useState<File | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<DocumentFormData>({
    defaultValues: {
      type: '',
      designation: '',
      referenceId: '',
      format: '',
      effectiveDate: '',
      version: '00',
      status: ''
    }
  });

  // Load document data when editing
  useEffect(() => {
    if (currentEditId) {
      const document = documents.find(doc => doc.id === currentEditId);
      if (document) {
        setValue('type', document.type);
        setValue('designation', document.designation);
        setValue('referenceId', document.referenceId);
        setValue('format', document.format);
        setValue('effectiveDate', document.effectiveDate);
        setValue('version', document.version);
        setValue('status', document.status);
      }
    }
  }, [currentEditId, documents, setValue]);

  const onSubmit = async (data: DocumentFormData) => {
    try {
      setLoading(true);

      const documentData = {
        type: data.type as DocumentType,
        designation: data.designation,
        referenceId: data.referenceId,
        format: data.format as DocumentFormat,
        effectiveDate: data.effectiveDate,
        version: data.version,
        status: data.status as DocumentStatus,
        fileName: currentFile?.name || '',
        fileData: currentFile ? URL.createObjectURL(currentFile) : '',
      };

      if (currentEditId) {
        updateDocument(currentEditId, documentData);
        toast.success('Document updated successfully');
      } else {
        addDocument(documentData);
        toast.success('Document saved successfully');
      }

      handleClear();
    } catch (error) {
      toast.error('Error saving document. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    reset();
    setCurrentEditId(null);
    setCurrentFile(null);
  };

  const documentTypes: DocumentType[] = ['Processus', 'Procédure', 'Enregistrement', 'Manuel', 'Logiciel'];
  const documentFormats: DocumentFormat[] = ['Word', 'Excel', 'PPT', 'PDF', 'Other'];
  const documentStatuses: DocumentStatus[] = ['RAS', 'Modifier', 'Créer', 'Annuler'];

  return (
    <section className="section form-section">
      <div className="section-header">
        <h2>
          <i className="fas fa-plus-circle"></i>
          {currentEditId ? 'Edit Document' : 'Add New Document'}
        </h2>
        <p>Fill in the document details below</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="input-form">
        <div className="form-row">
          <div className="form-group">
            <label htmlFor="docType" className="required">
              <i className="fas fa-tag"></i> Document Type
            </label>
            <select
              id="docType"
              {...register('type', { required: 'Document type is required' })}
              className={errors.type ? 'error' : ''}
            >
              <option value="">Select document type...</option>
              {documentTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
            {errors.type && <div className="error-message">{errors.type.message}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="designation" className="required">
              <i className="fas fa-file-signature"></i> Designation
            </label>
            <input
              type="text"
              id="designation"
              {...register('designation', { required: 'Designation is required' })}
              placeholder="Enter document designation"
              className={errors.designation ? 'error' : ''}
            />
            {errors.designation && <div className="error-message">{errors.designation.message}</div>}
          </div>

          <div className="form-group tooltip-container">
            <label htmlFor="referenceId" className="required">
              <i className="fas fa-hashtag"></i> Reference ID
            </label>
            <input
              type="text"
              id="referenceId"
              {...register('referenceId', { 
                required: 'Reference ID is required',
                pattern: {
                  value: /^[A-Z]{2}\.[A-Z]{2}\.[0-9]{2}$/,
                  message: 'Format: XX.XX.01 (e.g., PR.EP.01)'
                }
              })}
              placeholder="e.g., PR.EP.01"
              className={errors.referenceId ? 'error' : ''}
            />
            <div className="tooltip">
              <i className="fas fa-info-circle"></i>
              <span className="tooltip-text">Format: XX.XX.01 (e.g., PR.EP.01)</span>
            </div>
            {errors.referenceId && <div className="error-message">{errors.referenceId.message}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="format" className="required">
              <i className="fas fa-file-code"></i> Format
            </label>
            <select
              id="format"
              {...register('format', { required: 'Format is required' })}
              className={errors.format ? 'error' : ''}
            >
              <option value="">Select format...</option>
              {documentFormats.map(format => (
                <option key={format} value={format}>{format}</option>
              ))}
            </select>
            {errors.format && <div className="error-message">{errors.format.message}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="effectiveDate" className="required">
              <i className="fas fa-calendar-alt"></i> Effective Date
            </label>
            <input
              type="date"
              id="effectiveDate"
              {...register('effectiveDate', { required: 'Effective date is required' })}
              className={errors.effectiveDate ? 'error' : ''}
            />
            {errors.effectiveDate && <div className="error-message">{errors.effectiveDate.message}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="version" className="required">
              <i className="fas fa-code-branch"></i> Version
            </label>
            <input
              type="text"
              id="version"
              {...register('version', { 
                required: 'Version is required',
                pattern: {
                  value: /^[0-9]{2}$/,
                  message: 'Version must be 2 digits (e.g., 00, 01, 02)'
                }
              })}
              placeholder="00"
              className={errors.version ? 'error' : ''}
            />
            {errors.version && <div className="error-message">{errors.version.message}</div>}
          </div>

          <div className="form-group">
            <label htmlFor="status" className="required">
              <i className="fas fa-info-circle"></i> Status
            </label>
            <select
              id="status"
              {...register('status', { required: 'Status is required' })}
              className={errors.status ? 'error' : ''}
            >
              <option value="">Select status...</option>
              {documentStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
            {errors.status && <div className="error-message">{errors.status.message}</div>}
          </div>

          <div className="form-group file-upload-group">
            <FileUpload 
              currentFile={currentFile}
              onFileChange={setCurrentFile}
            />
          </div>
        </div>

        <div className="form-actions">
          <button 
            type="button" 
            onClick={handleClear}
            className="btn btn-secondary"
            disabled={isSubmitting}
          >
            <i className="fas fa-times"></i> Clear
          </button>
          <button 
            type="submit" 
            className="btn btn-primary"
            disabled={isSubmitting}
          >
            <i className="fas fa-save"></i> 
            {currentEditId ? 'Update Document' : 'Save Document'}
          </button>
        </div>
      </form>
    </section>
  );
};

export default DocumentForm;
